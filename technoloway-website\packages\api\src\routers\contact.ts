import { z } from 'zod';
import { createTRPCRouter, publicProcedure, adminProcedure } from '../trpc';
import { 
  createContactInquirySchema, 
  updateContactInquirySchema,
  paginationSchema 
} from '@technoloway/database';

export const contactRouter = createTRPCRouter({
  create: publicProcedure
    .input(createContactInquirySchema)
    .mutation(async ({ ctx, input }) => {
      return ctx.prisma.contactInquiry.create({
        data: input,
      });
    }),

  getAll: adminProcedure
    .input(
      paginationSchema.extend({
        status: z.enum(['PENDING', 'IN_PROGRESS', 'RESOLVED', 'CLOSED']).optional(),
      })
    )
    .query(async ({ ctx, input }) => {
      const { page, limit, status } = input;
      const skip = (page - 1) * limit;

      const where = status ? { status } : {};

      const [inquiries, total] = await Promise.all([
        ctx.prisma.contactInquiry.findMany({
          where,
          skip,
          take: limit,
          orderBy: { createdAt: 'desc' },
          include: {
            assignedTo: {
              select: {
                id: true,
                firstName: true,
                lastName: true,
                email: true,
              },
            },
          },
        }),
        ctx.prisma.contactInquiry.count({ where }),
      ]);

      return {
        inquiries,
        pagination: {
          page,
          limit,
          total,
          pages: Math.ceil(total / limit),
        },
      };
    }),

  getById: adminProcedure
    .input(z.object({ id: z.string() }))
    .query(async ({ ctx, input }) => {
      return ctx.prisma.contactInquiry.findUnique({
        where: { id: input.id },
        include: {
          assignedTo: {
            select: {
              id: true,
              firstName: true,
              lastName: true,
              email: true,
            },
          },
        },
      });
    }),

  update: adminProcedure
    .input(
      z.object({
        id: z.string(),
        data: updateContactInquirySchema,
      })
    )
    .mutation(async ({ ctx, input }) => {
      return ctx.prisma.contactInquiry.update({
        where: { id: input.id },
        data: input.data,
        include: {
          assignedTo: {
            select: {
              id: true,
              firstName: true,
              lastName: true,
              email: true,
            },
          },
        },
      });
    }),

  delete: adminProcedure
    .input(z.object({ id: z.string() }))
    .mutation(async ({ ctx, input }) => {
      return ctx.prisma.contactInquiry.delete({
        where: { id: input.id },
      });
    }),

  getStats: adminProcedure.query(async ({ ctx }) => {
    const [total, pending, inProgress, resolved] = await Promise.all([
      ctx.prisma.contactInquiry.count(),
      ctx.prisma.contactInquiry.count({ where: { status: 'PENDING' } }),
      ctx.prisma.contactInquiry.count({ where: { status: 'IN_PROGRESS' } }),
      ctx.prisma.contactInquiry.count({ where: { status: 'RESOLVED' } }),
    ]);

    return {
      total,
      pending,
      inProgress,
      resolved,
    };
  }),
});
