{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Project%20By%20AI/Technoloway/Technoloway%20%28Processing%29/technoloway-simple/src/app/admin/legal-pages/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport { motion } from 'framer-motion';\nimport {\n  PlusIcon,\n  PencilIcon,\n  TrashIcon,\n  EyeIcon,\n  DocumentTextIcon,\n  CheckCircleIcon,\n  XCircleIcon,\n  ShieldCheckIcon,\n  ScaleIcon,\n} from '@heroicons/react/24/outline';\n\n// Mock data - in real app, this would come from API\nconst legalPages = [\n  {\n    id: 1,\n    title: 'Privacy Policy',\n    slug: 'privacy-policy',\n    type: 'Privacy',\n    content: `<h2>Privacy Policy</h2>\n    <p>Last updated: January 15, 2024</p>\n    \n    <h3>Information We Collect</h3>\n    <p>We collect information you provide directly to us, such as when you create an account, make a purchase, or contact us for support.</p>\n    \n    <h3>How We Use Your Information</h3>\n    <p>We use the information we collect to provide, maintain, and improve our services, process transactions, and communicate with you.</p>\n    \n    <h3>Information Sharing</h3>\n    <p>We do not sell, trade, or otherwise transfer your personal information to third parties without your consent, except as described in this policy.</p>\n    \n    <h3>Data Security</h3>\n    <p>We implement appropriate security measures to protect your personal information against unauthorized access, alteration, disclosure, or destruction.</p>`,\n    metaTitle: 'Privacy Policy - Technoloway',\n    metaDescription: 'Learn how Technoloway collects, uses, and protects your personal information.',\n    isPublished: true,\n    isRequired: true,\n    lastReviewed: '2024-01-15T10:00:00Z',\n    nextReview: '2024-07-15T10:00:00Z',\n    version: '2.1',\n    publishedAt: '2024-01-15T10:00:00Z',\n    createdAt: '2024-01-10T09:00:00Z',\n    updatedAt: '2024-01-15T10:00:00Z',\n    author: 'Legal Team',\n  },\n  {\n    id: 2,\n    title: 'Terms of Service',\n    slug: 'terms-of-service',\n    type: 'Terms',\n    content: `<h2>Terms of Service</h2>\n    <p>Last updated: January 12, 2024</p>\n    \n    <h3>Acceptance of Terms</h3>\n    <p>By accessing and using our services, you accept and agree to be bound by the terms and provision of this agreement.</p>\n    \n    <h3>Use License</h3>\n    <p>Permission is granted to temporarily download one copy of the materials on our website for personal, non-commercial transitory viewing only.</p>\n    \n    <h3>Disclaimer</h3>\n    <p>The materials on our website are provided on an 'as is' basis. We make no warranties, expressed or implied, and hereby disclaim all other warranties.</p>\n    \n    <h3>Limitations</h3>\n    <p>In no event shall our company or its suppliers be liable for any damages arising out of the use or inability to use the materials on our website.</p>`,\n    metaTitle: 'Terms of Service - Technoloway',\n    metaDescription: 'Read our terms of service and understand your rights and responsibilities when using our services.',\n    isPublished: true,\n    isRequired: true,\n    lastReviewed: '2024-01-12T11:00:00Z',\n    nextReview: '2024-07-12T11:00:00Z',\n    version: '1.8',\n    publishedAt: '2024-01-12T11:00:00Z',\n    createdAt: '2024-01-08T10:00:00Z',\n    updatedAt: '2024-01-12T11:00:00Z',\n    author: 'Legal Team',\n  },\n  {\n    id: 3,\n    title: 'Cookie Policy',\n    slug: 'cookie-policy',\n    type: 'Privacy',\n    content: `<h2>Cookie Policy</h2>\n    <p>Last updated: January 10, 2024</p>\n    \n    <h3>What Are Cookies</h3>\n    <p>Cookies are small text files that are placed on your computer or mobile device when you visit our website.</p>\n    \n    <h3>How We Use Cookies</h3>\n    <p>We use cookies to improve your browsing experience, analyze site traffic, and personalize content.</p>\n    \n    <h3>Types of Cookies We Use</h3>\n    <ul>\n      <li>Essential cookies: Required for the website to function properly</li>\n      <li>Analytics cookies: Help us understand how visitors interact with our website</li>\n      <li>Marketing cookies: Used to track visitors across websites</li>\n    </ul>\n    \n    <h3>Managing Cookies</h3>\n    <p>You can control and/or delete cookies as you wish through your browser settings.</p>`,\n    metaTitle: 'Cookie Policy - Technoloway',\n    metaDescription: 'Learn about how we use cookies and how you can manage your cookie preferences.',\n    isPublished: true,\n    isRequired: false,\n    lastReviewed: '2024-01-10T14:00:00Z',\n    nextReview: '2024-07-10T14:00:00Z',\n    version: '1.3',\n    publishedAt: '2024-01-10T14:00:00Z',\n    createdAt: '2024-01-05T16:00:00Z',\n    updatedAt: '2024-01-10T14:00:00Z',\n    author: 'Legal Team',\n  },\n  {\n    id: 4,\n    title: 'Data Processing Agreement',\n    slug: 'data-processing-agreement',\n    type: 'Compliance',\n    content: `<h2>Data Processing Agreement</h2>\n    <p>Last updated: January 08, 2024</p>\n    \n    <h3>Purpose and Scope</h3>\n    <p>This Data Processing Agreement governs the processing of personal data by Technoloway on behalf of its clients.</p>\n    \n    <h3>Data Controller and Processor</h3>\n    <p>The client acts as the data controller, and Technoloway acts as the data processor for the personal data processed under this agreement.</p>\n    \n    <h3>Processing Instructions</h3>\n    <p>Technoloway will process personal data only on documented instructions from the client, including transfers to third countries.</p>\n    \n    <h3>Security Measures</h3>\n    <p>We implement appropriate technical and organizational measures to ensure a level of security appropriate to the risk.</p>`,\n    metaTitle: 'Data Processing Agreement - Technoloway',\n    metaDescription: 'Our data processing agreement outlining how we handle personal data on behalf of our clients.',\n    isPublished: false,\n    isRequired: false,\n    lastReviewed: '2024-01-08T12:00:00Z',\n    nextReview: '2024-07-08T12:00:00Z',\n    version: '1.0',\n    publishedAt: null,\n    createdAt: '2024-01-03T12:00:00Z',\n    updatedAt: '2024-01-08T12:00:00Z',\n    author: 'Legal Team',\n  },\n  {\n    id: 5,\n    title: 'Acceptable Use Policy',\n    slug: 'acceptable-use-policy',\n    type: 'Policy',\n    content: `<h2>Acceptable Use Policy</h2>\n    <p>Last updated: January 05, 2024</p>\n    \n    <h3>Permitted Uses</h3>\n    <p>You may use our services for lawful purposes only and in accordance with these terms.</p>\n    \n    <h3>Prohibited Uses</h3>\n    <p>You may not use our services to transmit, distribute, or store material that is unlawful, harmful, threatening, abusive, or otherwise objectionable.</p>\n    \n    <h3>Enforcement</h3>\n    <p>We reserve the right to investigate and take appropriate action against anyone who violates this policy.</p>\n    \n    <h3>Reporting Violations</h3>\n    <p>If you become aware of any violation of this policy, please report it to us immediately.</p>`,\n    metaTitle: 'Acceptable Use Policy - Technoloway',\n    metaDescription: 'Guidelines for acceptable use of our services and prohibited activities.',\n    isPublished: true,\n    isRequired: false,\n    lastReviewed: '2024-01-05T15:00:00Z',\n    nextReview: '2024-07-05T15:00:00Z',\n    version: '1.2',\n    publishedAt: '2024-01-05T15:00:00Z',\n    createdAt: '2024-01-01T10:00:00Z',\n    updatedAt: '2024-01-05T15:00:00Z',\n    author: 'Legal Team',\n  },\n];\n\nconst pageTypes = ['All', 'Privacy', 'Terms', 'Compliance', 'Policy'];\n\nexport default function LegalPagesPage() {\n  const [pages, setPages] = useState(legalPages);\n  const [selectedType, setSelectedType] = useState('All');\n  const [selectedPage, setSelectedPage] = useState<any>(null);\n  const [showAddModal, setShowAddModal] = useState(false);\n\n  const filteredPages = pages.filter(page => \n    selectedType === 'All' || page.type === selectedType\n  );\n\n  const handleTogglePublished = (id: number) => {\n    setPages(prev => prev.map(page => \n      page.id === id \n        ? { \n            ...page, \n            isPublished: !page.isPublished,\n            publishedAt: !page.isPublished ? new Date().toISOString() : null\n          }\n        : page\n    ));\n  };\n\n  const handleDelete = (id: number) => {\n    const page = pages.find(p => p.id === id);\n    if (page?.isRequired) {\n      alert('This page is required and cannot be deleted.');\n      return;\n    }\n    if (confirm('Are you sure you want to delete this legal page?')) {\n      setPages(prev => prev.filter(page => page.id !== id));\n    }\n  };\n\n  const formatDate = (dateString: string | null) => {\n    if (!dateString) return 'Not published';\n    return new Date(dateString).toLocaleDateString('en-US', {\n      year: 'numeric',\n      month: 'short',\n      day: 'numeric',\n    });\n  };\n\n  const isReviewDue = (nextReviewDate: string) => {\n    return new Date(nextReviewDate) <= new Date();\n  };\n\n  const stripHtml = (html: string) => {\n    return html.replace(/<[^>]*>/g, '').substring(0, 150) + '...';\n  };\n\n  return (\n    <div className=\"py-6\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 md:px-8\">\n        {/* Header */}\n        <div className=\"md:flex md:items-center md:justify-between mb-8\">\n          <div className=\"flex-1 min-w-0\">\n            <h2 className=\"text-2xl font-bold leading-7 text-gray-900 sm:text-3xl sm:truncate\">\n              Legal Pages\n            </h2>\n            <p className=\"mt-1 text-sm text-gray-500\">\n              Manage privacy policies, terms of service, and other legal documentation\n            </p>\n          </div>\n          <div className=\"mt-4 flex md:mt-0 md:ml-4\">\n            <button\n              onClick={() => setShowAddModal(true)}\n              className=\"ml-3 inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500\"\n            >\n              <PlusIcon className=\"-ml-1 mr-2 h-5 w-5\" />\n              Add Legal Page\n            </button>\n          </div>\n        </div>\n\n        {/* Stats */}\n        <div className=\"mb-6 grid grid-cols-1 gap-5 sm:grid-cols-4\">\n          <div className=\"bg-white overflow-hidden shadow rounded-lg\">\n            <div className=\"p-5\">\n              <div className=\"flex items-center\">\n                <div className=\"flex-shrink-0\">\n                  <div className=\"w-8 h-8 bg-blue-500 rounded-md flex items-center justify-center\">\n                    <span className=\"text-white text-sm font-bold\">{pages.length}</span>\n                  </div>\n                </div>\n                <div className=\"ml-5 w-0 flex-1\">\n                  <dl>\n                    <dt className=\"text-sm font-medium text-gray-500 truncate\">Total Pages</dt>\n                    <dd className=\"text-lg font-medium text-gray-900\">Created</dd>\n                  </dl>\n                </div>\n              </div>\n            </div>\n          </div>\n\n          <div className=\"bg-white overflow-hidden shadow rounded-lg\">\n            <div className=\"p-5\">\n              <div className=\"flex items-center\">\n                <div className=\"flex-shrink-0\">\n                  <div className=\"w-8 h-8 bg-green-500 rounded-md flex items-center justify-center\">\n                    <span className=\"text-white text-sm font-bold\">\n                      {pages.filter(p => p.isPublished).length}\n                    </span>\n                  </div>\n                </div>\n                <div className=\"ml-5 w-0 flex-1\">\n                  <dl>\n                    <dt className=\"text-sm font-medium text-gray-500 truncate\">Published</dt>\n                    <dd className=\"text-lg font-medium text-gray-900\">Live</dd>\n                  </dl>\n                </div>\n              </div>\n            </div>\n          </div>\n\n          <div className=\"bg-white overflow-hidden shadow rounded-lg\">\n            <div className=\"p-5\">\n              <div className=\"flex items-center\">\n                <div className=\"flex-shrink-0\">\n                  <div className=\"w-8 h-8 bg-red-500 rounded-md flex items-center justify-center\">\n                    <span className=\"text-white text-sm font-bold\">\n                      {pages.filter(p => isReviewDue(p.nextReview)).length}\n                    </span>\n                  </div>\n                </div>\n                <div className=\"ml-5 w-0 flex-1\">\n                  <dl>\n                    <dt className=\"text-sm font-medium text-gray-500 truncate\">Review Due</dt>\n                    <dd className=\"text-lg font-medium text-gray-900\">Needs Update</dd>\n                  </dl>\n                </div>\n              </div>\n            </div>\n          </div>\n\n          <div className=\"bg-white overflow-hidden shadow rounded-lg\">\n            <div className=\"p-5\">\n              <div className=\"flex items-center\">\n                <div className=\"flex-shrink-0\">\n                  <div className=\"w-8 h-8 bg-yellow-500 rounded-md flex items-center justify-center\">\n                    <span className=\"text-white text-sm font-bold\">\n                      {pages.filter(p => p.isRequired).length}\n                    </span>\n                  </div>\n                </div>\n                <div className=\"ml-5 w-0 flex-1\">\n                  <dl>\n                    <dt className=\"text-sm font-medium text-gray-500 truncate\">Required</dt>\n                    <dd className=\"text-lg font-medium text-gray-900\">Essential</dd>\n                  </dl>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        {/* Type Filter */}\n        <div className=\"mb-6 bg-white shadow rounded-lg\">\n          <div className=\"px-4 py-5 sm:p-6\">\n            <div className=\"flex flex-wrap gap-2\">\n              {pageTypes.map(type => (\n                <button\n                  key={type}\n                  onClick={() => setSelectedType(type)}\n                  className={`px-4 py-2 rounded-full text-sm font-medium transition-colors ${\n                    selectedType === type\n                      ? 'bg-blue-600 text-white'\n                      : 'bg-gray-100 text-gray-700 hover:bg-gray-200'\n                  }`}\n                >\n                  {type}\n                </button>\n              ))}\n            </div>\n          </div>\n        </div>\n\n        {/* Legal Pages List */}\n        <div className=\"bg-white shadow overflow-hidden sm:rounded-md\">\n          <ul className=\"divide-y divide-gray-200\">\n            {filteredPages.map((page, index) => (\n              <motion.li\n                key={page.id}\n                initial={{ opacity: 0, y: 20 }}\n                animate={{ opacity: 1, y: 0 }}\n                transition={{ duration: 0.3, delay: index * 0.1 }}\n              >\n                <div className=\"px-4 py-4 sm:px-6\">\n                  <div className=\"flex items-center justify-between\">\n                    <div className=\"flex items-start space-x-4 flex-1\">\n                      <div className=\"flex-shrink-0\">\n                        <div className={`w-10 h-10 rounded-lg flex items-center justify-center ${\n                          page.isPublished ? 'bg-green-100' : 'bg-gray-100'\n                        }`}>\n                          {page.type === 'Privacy' ? (\n                            <ShieldCheckIcon className={`w-6 h-6 ${page.isPublished ? 'text-green-600' : 'text-gray-400'}`} />\n                          ) : (\n                            <ScaleIcon className={`w-6 h-6 ${page.isPublished ? 'text-green-600' : 'text-gray-400'}`} />\n                          )}\n                        </div>\n                      </div>\n                      \n                      <div className=\"flex-1 min-w-0\">\n                        <div className=\"flex items-center space-x-2 mb-1\">\n                          <h3 className=\"text-lg font-medium text-gray-900 truncate\">\n                            {page.title}\n                          </h3>\n                          <div className=\"flex items-center space-x-1\">\n                            <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${\n                              page.isPublished \n                                ? 'bg-green-100 text-green-800'\n                                : 'bg-gray-100 text-gray-800'\n                            }`}>\n                              {page.isPublished ? 'Published' : 'Draft'}\n                            </span>\n                            {page.isRequired && (\n                              <span className=\"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800\">\n                                Required\n                              </span>\n                            )}\n                            {isReviewDue(page.nextReview) && (\n                              <span className=\"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800\">\n                                Review Due\n                              </span>\n                            )}\n                          </div>\n                        </div>\n                        \n                        <div className=\"flex items-center space-x-4 text-sm text-gray-500 mb-2\">\n                          <span>Type: {page.type}</span>\n                          <span>Version: {page.version}</span>\n                          <span>URL: /{page.slug}</span>\n                        </div>\n                        \n                        <p className=\"text-sm text-gray-600 mb-3\">\n                          {stripHtml(page.content)}\n                        </p>\n                        \n                        <div className=\"flex items-center space-x-4 text-sm text-gray-500\">\n                          <span>Last Reviewed: {formatDate(page.lastReviewed)}</span>\n                          <span>Next Review: {formatDate(page.nextReview)}</span>\n                          <span>Updated: {formatDate(page.updatedAt)}</span>\n                        </div>\n                      </div>\n                    </div>\n                    \n                    <div className=\"flex items-center space-x-2 ml-4\">\n                      <button\n                        onClick={() => setSelectedPage(page)}\n                        className=\"text-gray-400 hover:text-blue-600 transition-colors\"\n                        title=\"View\"\n                      >\n                        <EyeIcon className=\"h-5 w-5\" />\n                      </button>\n                      <button\n                        className=\"text-gray-400 hover:text-blue-600 transition-colors\"\n                        title=\"Edit\"\n                      >\n                        <PencilIcon className=\"h-5 w-5\" />\n                      </button>\n                      <button\n                        onClick={() => handleTogglePublished(page.id)}\n                        className={`text-gray-400 hover:text-green-600 transition-colors ${\n                          page.isPublished ? 'text-green-600' : ''\n                        }`}\n                        title={page.isPublished ? 'Unpublish' : 'Publish'}\n                      >\n                        <CheckCircleIcon className=\"h-5 w-5\" />\n                      </button>\n                      <button\n                        onClick={() => handleDelete(page.id)}\n                        className={`text-gray-400 transition-colors ${\n                          page.isRequired \n                            ? 'cursor-not-allowed opacity-50' \n                            : 'hover:text-red-600'\n                        }`}\n                        title={page.isRequired ? 'Required page cannot be deleted' : 'Delete'}\n                        disabled={page.isRequired}\n                      >\n                        <TrashIcon className=\"h-5 w-5\" />\n                      </button>\n                    </div>\n                  </div>\n                </div>\n              </motion.li>\n            ))}\n          </ul>\n        </div>\n\n        {/* Empty State */}\n        {filteredPages.length === 0 && (\n          <div className=\"bg-white shadow rounded-lg\">\n            <div className=\"text-center py-12\">\n              <DocumentTextIcon className=\"mx-auto h-12 w-12 text-gray-400\" />\n              <h3 className=\"mt-2 text-sm font-medium text-gray-900\">No legal pages found</h3>\n              <p className=\"mt-1 text-sm text-gray-500\">\n                {selectedType === 'All' \n                  ? 'Get started by creating your first legal page.'\n                  : `No legal pages found in the ${selectedType} category.`\n                }\n              </p>\n            </div>\n          </div>\n        )}\n\n        {/* Page Preview Modal */}\n        {selectedPage && (\n          <div className=\"fixed inset-0 z-50 overflow-y-auto\">\n            <div className=\"flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0\">\n              <div className=\"fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity\" onClick={() => setSelectedPage(null)} />\n              \n              <div className=\"inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-4xl sm:w-full\">\n                <div className=\"bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4\">\n                  <div className=\"sm:flex sm:items-start\">\n                    <div className=\"mt-3 text-center sm:mt-0 sm:text-left w-full\">\n                      <h3 className=\"text-lg leading-6 font-medium text-gray-900 mb-4\">\n                        {selectedPage.title}\n                      </h3>\n                      \n                      <div className=\"mb-4 p-4 bg-gray-50 rounded-lg\">\n                        <div className=\"grid grid-cols-2 gap-4 text-sm\">\n                          <div>\n                            <strong>Type:</strong> {selectedPage.type}\n                          </div>\n                          <div>\n                            <strong>Version:</strong> {selectedPage.version}\n                          </div>\n                          <div>\n                            <strong>URL:</strong> /{selectedPage.slug}\n                          </div>\n                          <div>\n                            <strong>Status:</strong> {selectedPage.isPublished ? 'Published' : 'Draft'}\n                          </div>\n                          <div>\n                            <strong>Last Reviewed:</strong> {formatDate(selectedPage.lastReviewed)}\n                          </div>\n                          <div>\n                            <strong>Next Review:</strong> {formatDate(selectedPage.nextReview)}\n                          </div>\n                        </div>\n                      </div>\n                      \n                      <div className=\"prose max-w-none\">\n                        <div dangerouslySetInnerHTML={{ __html: selectedPage.content }} />\n                      </div>\n                    </div>\n                  </div>\n                </div>\n                <div className=\"bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse\">\n                  <button\n                    type=\"button\"\n                    className=\"w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-blue-600 text-base font-medium text-white hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 sm:ml-3 sm:w-auto sm:text-sm\"\n                  >\n                    Edit Page\n                  </button>\n                  <button\n                    type=\"button\"\n                    className=\"mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 sm:mt-0 sm:w-auto sm:text-sm\"\n                    onClick={() => setSelectedPage(null)}\n                  >\n                    Close\n                  </button>\n                </div>\n              </div>\n            </div>\n          </div>\n        )}\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;AAJA;;;;AAgBA,oDAAoD;AACpD,MAAM,aAAa;IACjB;QACE,IAAI;QACJ,OAAO;QACP,MAAM;QACN,MAAM;QACN,SAAS,CAAC;;;;;;;;;;;;;+JAai<PERSON>,CAAC;QAC5J,WAAW;QACX,iBAAiB;QACjB,aAAa;QACb,YAAY;QACZ,cAAc;QACd,YAAY;QACZ,SAAS;QACT,aAAa;QACb,WAAW;QACX,WAAW;QACX,QAAQ;IACV;IACA;QACE,IAAI;QACJ,OAAO;QACP,MAAM;QACN,MAAM;QACN,SAAS,CAAC;;;;;;;;;;;;;4JAa8I,CAAC;QACzJ,WAAW;QACX,iBAAiB;QACjB,aAAa;QACb,YAAY;QACZ,cAAc;QACd,YAAY;QACZ,SAAS;QACT,aAAa;QACb,WAAW;QACX,WAAW;QACX,QAAQ;IACV;IACA;QACE,IAAI;QACJ,OAAO;QACP,MAAM;QACN,MAAM;QACN,SAAS,CAAC;;;;;;;;;;;;;;;;;2FAiB6E,CAAC;QACxF,WAAW;QACX,iBAAiB;QACjB,aAAa;QACb,YAAY;QACZ,cAAc;QACd,YAAY;QACZ,SAAS;QACT,aAAa;QACb,WAAW;QACX,WAAW;QACX,QAAQ;IACV;IACA;QACE,IAAI;QACJ,OAAO;QACP,MAAM;QACN,MAAM;QACN,SAAS,CAAC;;;;;;;;;;;;;gIAakH,CAAC;QAC7H,WAAW;QACX,iBAAiB;QACjB,aAAa;QACb,YAAY;QACZ,cAAc;QACd,YAAY;QACZ,SAAS;QACT,aAAa;QACb,WAAW;QACX,WAAW;QACX,QAAQ;IACV;IACA;QACE,IAAI;QACJ,OAAO;QACP,MAAM;QACN,MAAM;QACN,SAAS,CAAC;;;;;;;;;;;;;mGAaqF,CAAC;QAChG,WAAW;QACX,iBAAiB;QACjB,aAAa;QACb,YAAY;QACZ,cAAc;QACd,YAAY;QACZ,SAAS;QACT,aAAa;QACb,WAAW;QACX,WAAW;QACX,QAAQ;IACV;CACD;AAED,MAAM,YAAY;IAAC;IAAO;IAAW;IAAS;IAAc;CAAS;AAEtD,SAAS;;IACtB,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAO;IACtD,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEjD,MAAM,gBAAgB,MAAM,MAAM,CAAC,CAAA,OACjC,iBAAiB,SAAS,KAAK,IAAI,KAAK;IAG1C,MAAM,wBAAwB,CAAC;QAC7B,SAAS,CAAA,OAAQ,KAAK,GAAG,CAAC,CAAA,OACxB,KAAK,EAAE,KAAK,KACR;oBACE,GAAG,IAAI;oBACP,aAAa,CAAC,KAAK,WAAW;oBAC9B,aAAa,CAAC,KAAK,WAAW,GAAG,IAAI,OAAO,WAAW,KAAK;gBAC9D,IACA;IAER;IAEA,MAAM,eAAe,CAAC;QACpB,MAAM,OAAO,MAAM,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;QACtC,IAAI,MAAM,YAAY;YACpB,MAAM;YACN;QACF;QACA,IAAI,QAAQ,qDAAqD;YAC/D,SAAS,CAAA,OAAQ,KAAK,MAAM,CAAC,CAAA,OAAQ,KAAK,EAAE,KAAK;QACnD;IACF;IAEA,MAAM,aAAa,CAAC;QAClB,IAAI,CAAC,YAAY,OAAO;QACxB,OAAO,IAAI,KAAK,YAAY,kBAAkB,CAAC,SAAS;YACtD,MAAM;YACN,OAAO;YACP,KAAK;QACP;IACF;IAEA,MAAM,cAAc,CAAC;QACnB,OAAO,IAAI,KAAK,mBAAmB,IAAI;IACzC;IAEA,MAAM,YAAY,CAAC;QACjB,OAAO,KAAK,OAAO,CAAC,YAAY,IAAI,SAAS,CAAC,GAAG,OAAO;IAC1D;IAEA,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAI,WAAU;;8BAEb,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAG,WAAU;8CAAqE;;;;;;8CAGnF,6LAAC;oCAAE,WAAU;8CAA6B;;;;;;;;;;;;sCAI5C,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCACC,SAAS,IAAM,gBAAgB;gCAC/B,WAAU;;kDAEV,6LAAC,kNAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;oCAAuB;;;;;;;;;;;;;;;;;;8BAOjD,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDAAK,WAAU;8DAAgC,MAAM,MAAM;;;;;;;;;;;;;;;;sDAGhE,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;;kEACC,6LAAC;wDAAG,WAAU;kEAA6C;;;;;;kEAC3D,6LAAC;wDAAG,WAAU;kEAAoC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAO5D,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDAAK,WAAU;8DACb,MAAM,MAAM,CAAC,CAAA,IAAK,EAAE,WAAW,EAAE,MAAM;;;;;;;;;;;;;;;;sDAI9C,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;;kEACC,6LAAC;wDAAG,WAAU;kEAA6C;;;;;;kEAC3D,6LAAC;wDAAG,WAAU;kEAAoC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAO5D,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDAAK,WAAU;8DACb,MAAM,MAAM,CAAC,CAAA,IAAK,YAAY,EAAE,UAAU,GAAG,MAAM;;;;;;;;;;;;;;;;sDAI1D,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;;kEACC,6LAAC;wDAAG,WAAU;kEAA6C;;;;;;kEAC3D,6LAAC;wDAAG,WAAU;kEAAoC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAO5D,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDAAK,WAAU;8DACb,MAAM,MAAM,CAAC,CAAA,IAAK,EAAE,UAAU,EAAE,MAAM;;;;;;;;;;;;;;;;sDAI7C,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;;kEACC,6LAAC;wDAAG,WAAU;kEAA6C;;;;;;kEAC3D,6LAAC;wDAAG,WAAU;kEAAoC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAS9D,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;sCACZ,UAAU,GAAG,CAAC,CAAA,qBACb,6LAAC;oCAEC,SAAS,IAAM,gBAAgB;oCAC/B,WAAW,CAAC,6DAA6D,EACvE,iBAAiB,OACb,2BACA,+CACJ;8CAED;mCARI;;;;;;;;;;;;;;;;;;;;8BAgBf,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAG,WAAU;kCACX,cAAc,GAAG,CAAC,CAAC,MAAM,sBACxB,6LAAC,6LAAA,CAAA,SAAM,CAAC,EAAE;gCAER,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAG;gCAC7B,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAE;gCAC5B,YAAY;oCAAE,UAAU;oCAAK,OAAO,QAAQ;gCAAI;0CAEhD,cAAA,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;kEACb,cAAA,6LAAC;4DAAI,WAAW,CAAC,sDAAsD,EACrE,KAAK,WAAW,GAAG,iBAAiB,eACpC;sEACC,KAAK,IAAI,KAAK,0BACb,6LAAC,gOAAA,CAAA,kBAAe;gEAAC,WAAW,CAAC,QAAQ,EAAE,KAAK,WAAW,GAAG,mBAAmB,iBAAiB;;;;;qFAE9F,6LAAC,oNAAA,CAAA,YAAS;gEAAC,WAAW,CAAC,QAAQ,EAAE,KAAK,WAAW,GAAG,mBAAmB,iBAAiB;;;;;;;;;;;;;;;;kEAK9F,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;wEAAG,WAAU;kFACX,KAAK,KAAK;;;;;;kFAEb,6LAAC;wEAAI,WAAU;;0FACb,6LAAC;gFAAK,WAAW,CAAC,wEAAwE,EACxF,KAAK,WAAW,GACZ,gCACA,6BACJ;0FACC,KAAK,WAAW,GAAG,cAAc;;;;;;4EAEnC,KAAK,UAAU,kBACd,6LAAC;gFAAK,WAAU;0FAAkG;;;;;;4EAInH,YAAY,KAAK,UAAU,mBAC1B,6LAAC;gFAAK,WAAU;0FAAwG;;;;;;;;;;;;;;;;;;0EAO9H,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;;4EAAK;4EAAO,KAAK,IAAI;;;;;;;kFACtB,6LAAC;;4EAAK;4EAAU,KAAK,OAAO;;;;;;;kFAC5B,6LAAC;;4EAAK;4EAAO,KAAK,IAAI;;;;;;;;;;;;;0EAGxB,6LAAC;gEAAE,WAAU;0EACV,UAAU,KAAK,OAAO;;;;;;0EAGzB,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;;4EAAK;4EAAgB,WAAW,KAAK,YAAY;;;;;;;kFAClD,6LAAC;;4EAAK;4EAAc,WAAW,KAAK,UAAU;;;;;;;kFAC9C,6LAAC;;4EAAK;4EAAU,WAAW,KAAK,SAAS;;;;;;;;;;;;;;;;;;;;;;;;;0DAK/C,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDACC,SAAS,IAAM,gBAAgB;wDAC/B,WAAU;wDACV,OAAM;kEAEN,cAAA,6LAAC,gNAAA,CAAA,UAAO;4DAAC,WAAU;;;;;;;;;;;kEAErB,6LAAC;wDACC,WAAU;wDACV,OAAM;kEAEN,cAAA,6LAAC,sNAAA,CAAA,aAAU;4DAAC,WAAU;;;;;;;;;;;kEAExB,6LAAC;wDACC,SAAS,IAAM,sBAAsB,KAAK,EAAE;wDAC5C,WAAW,CAAC,qDAAqD,EAC/D,KAAK,WAAW,GAAG,mBAAmB,IACtC;wDACF,OAAO,KAAK,WAAW,GAAG,cAAc;kEAExC,cAAA,6LAAC,gOAAA,CAAA,kBAAe;4DAAC,WAAU;;;;;;;;;;;kEAE7B,6LAAC;wDACC,SAAS,IAAM,aAAa,KAAK,EAAE;wDACnC,WAAW,CAAC,gCAAgC,EAC1C,KAAK,UAAU,GACX,kCACA,sBACJ;wDACF,OAAO,KAAK,UAAU,GAAG,oCAAoC;wDAC7D,UAAU,KAAK,UAAU;kEAEzB,cAAA,6LAAC,oNAAA,CAAA,YAAS;4DAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;+BAjGxB,KAAK,EAAE;;;;;;;;;;;;;;;gBA4GnB,cAAc,MAAM,KAAK,mBACxB,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,kOAAA,CAAA,mBAAgB;gCAAC,WAAU;;;;;;0CAC5B,6LAAC;gCAAG,WAAU;0CAAyC;;;;;;0CACvD,6LAAC;gCAAE,WAAU;0CACV,iBAAiB,QACd,mDACA,CAAC,4BAA4B,EAAE,aAAa,UAAU,CAAC;;;;;;;;;;;;;;;;;gBAQlE,8BACC,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;gCAA6D,SAAS,IAAM,gBAAgB;;;;;;0CAE3G,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAG,WAAU;kEACX,aAAa,KAAK;;;;;;kEAGrB,6LAAC;wDAAI,WAAU;kEACb,cAAA,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;;sFACC,6LAAC;sFAAO;;;;;;wEAAc;wEAAE,aAAa,IAAI;;;;;;;8EAE3C,6LAAC;;sFACC,6LAAC;sFAAO;;;;;;wEAAiB;wEAAE,aAAa,OAAO;;;;;;;8EAEjD,6LAAC;;sFACC,6LAAC;sFAAO;;;;;;wEAAa;wEAAG,aAAa,IAAI;;;;;;;8EAE3C,6LAAC;;sFACC,6LAAC;sFAAO;;;;;;wEAAgB;wEAAE,aAAa,WAAW,GAAG,cAAc;;;;;;;8EAErE,6LAAC;;sFACC,6LAAC;sFAAO;;;;;;wEAAuB;wEAAE,WAAW,aAAa,YAAY;;;;;;;8EAEvE,6LAAC;;sFACC,6LAAC;sFAAO;;;;;;wEAAqB;wEAAE,WAAW,aAAa,UAAU;;;;;;;;;;;;;;;;;;kEAKvE,6LAAC;wDAAI,WAAU;kEACb,cAAA,6LAAC;4DAAI,yBAAyB;gEAAE,QAAQ,aAAa,OAAO;4DAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;kDAKrE,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDACC,MAAK;gDACL,WAAU;0DACX;;;;;;0DAGD,6LAAC;gDACC,MAAK;gDACL,WAAU;gDACV,SAAS,IAAM,gBAAgB;0DAChC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAWnB;GAjXwB;KAAA", "debugId": null}}, {"offset": {"line": 1233, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Project%20By%20AI/Technoloway/Technoloway%20%28Processing%29/technoloway-simple/node_modules/%40heroicons/react/24/outline/esm/PlusIcon.js"], "sourcesContent": ["import * as React from \"react\";\nfunction PlusIcon({\n  title,\n  titleId,\n  ...props\n}, svgRef) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    xmlns: \"http://www.w3.org/2000/svg\",\n    fill: \"none\",\n    viewBox: \"0 0 24 24\",\n    strokeWidth: 1.5,\n    stroke: \"currentColor\",\n    \"aria-hidden\": \"true\",\n    \"data-slot\": \"icon\",\n    ref: svgRef,\n    \"aria-labelledby\": titleId\n  }, props), title ? /*#__PURE__*/React.createElement(\"title\", {\n    id: titleId\n  }, title) : null, /*#__PURE__*/React.createElement(\"path\", {\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\",\n    d: \"M12 4.5v15m7.5-7.5h-15\"\n  }));\n}\nconst ForwardRef = /*#__PURE__*/ React.forwardRef(PlusIcon);\nexport default ForwardRef;"], "names": [], "mappings": ";;;AAAA;;AACA,SAAS,SAAS,EAChB,KAAK,EACL,OAAO,EACP,GAAG,OACJ,EAAE,MAAM;IACP,OAAO,WAAW,GAAE,CAAA,GAAA,6JAAA,CAAA,gBAAmB,AAAD,EAAE,OAAO,OAAO,MAAM,CAAC;QAC3D,OAAO;QACP,MAAM;QACN,SAAS;QACT,aAAa;QACb,QAAQ;QACR,eAAe;QACf,aAAa;QACb,KAAK;QACL,mBAAmB;IACrB,GAAG,QAAQ,QAAQ,WAAW,GAAE,CAAA,GAAA,6JAAA,CAAA,gBAAmB,AAAD,EAAE,SAAS;QAC3D,IAAI;IACN,GAAG,SAAS,MAAM,WAAW,GAAE,CAAA,GAAA,6JAAA,CAAA,gBAAmB,AAAD,EAAE,QAAQ;QACzD,eAAe;QACf,gBAAgB;QAChB,GAAG;IACL;AACF;AACA,MAAM,aAAa,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,EAAE;uCACnC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1275, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Project%20By%20AI/Technoloway/Technoloway%20%28Processing%29/technoloway-simple/node_modules/%40heroicons/react/24/outline/esm/PencilIcon.js"], "sourcesContent": ["import * as React from \"react\";\nfunction PencilIcon({\n  title,\n  titleId,\n  ...props\n}, svgRef) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    xmlns: \"http://www.w3.org/2000/svg\",\n    fill: \"none\",\n    viewBox: \"0 0 24 24\",\n    strokeWidth: 1.5,\n    stroke: \"currentColor\",\n    \"aria-hidden\": \"true\",\n    \"data-slot\": \"icon\",\n    ref: svgRef,\n    \"aria-labelledby\": titleId\n  }, props), title ? /*#__PURE__*/React.createElement(\"title\", {\n    id: titleId\n  }, title) : null, /*#__PURE__*/React.createElement(\"path\", {\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\",\n    d: \"m16.862 4.487 1.687-1.688a1.875 1.875 0 1 1 2.652 2.652L6.832 19.82a4.5 4.5 0 0 1-1.897 1.13l-2.685.8.8-2.685a4.5 4.5 0 0 1 1.13-1.897L16.863 4.487Zm0 0L19.5 7.125\"\n  }));\n}\nconst ForwardRef = /*#__PURE__*/ React.forwardRef(PencilIcon);\nexport default ForwardRef;"], "names": [], "mappings": ";;;AAAA;;AACA,SAAS,WAAW,EAClB,KAAK,EACL,OAAO,EACP,GAAG,OACJ,EAAE,MAAM;IACP,OAAO,WAAW,GAAE,CAAA,GAAA,6JAAA,CAAA,gBAAmB,AAAD,EAAE,OAAO,OAAO,MAAM,CAAC;QAC3D,OAAO;QACP,MAAM;QACN,SAAS;QACT,aAAa;QACb,QAAQ;QACR,eAAe;QACf,aAAa;QACb,KAAK;QACL,mBAAmB;IACrB,GAAG,QAAQ,QAAQ,WAAW,GAAE,CAAA,GAAA,6JAAA,CAAA,gBAAmB,AAAD,EAAE,SAAS;QAC3D,IAAI;IACN,GAAG,SAAS,MAAM,WAAW,GAAE,CAAA,GAAA,6JAAA,CAAA,gBAAmB,AAAD,EAAE,QAAQ;QACzD,eAAe;QACf,gBAAgB;QAChB,GAAG;IACL;AACF;AACA,MAAM,aAAa,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,EAAE;uCACnC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1317, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Project%20By%20AI/Technoloway/Technoloway%20%28Processing%29/technoloway-simple/node_modules/%40heroicons/react/24/outline/esm/TrashIcon.js"], "sourcesContent": ["import * as React from \"react\";\nfunction TrashIcon({\n  title,\n  titleId,\n  ...props\n}, svgRef) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    xmlns: \"http://www.w3.org/2000/svg\",\n    fill: \"none\",\n    viewBox: \"0 0 24 24\",\n    strokeWidth: 1.5,\n    stroke: \"currentColor\",\n    \"aria-hidden\": \"true\",\n    \"data-slot\": \"icon\",\n    ref: svgRef,\n    \"aria-labelledby\": titleId\n  }, props), title ? /*#__PURE__*/React.createElement(\"title\", {\n    id: titleId\n  }, title) : null, /*#__PURE__*/React.createElement(\"path\", {\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\",\n    d: \"m14.74 9-.346 9m-4.788 0L9.26 9m9.968-3.21c.342.052.682.107 1.022.166m-1.022-.165L18.16 19.673a2.25 2.25 0 0 1-2.244 2.077H8.084a2.25 2.25 0 0 1-2.244-2.077L4.772 5.79m14.456 0a48.108 48.108 0 0 0-3.478-.397m-12 .562c.34-.059.68-.114 1.022-.165m0 0a48.11 48.11 0 0 1 3.478-.397m7.5 0v-.916c0-1.18-.91-2.164-2.09-2.201a51.964 51.964 0 0 0-3.32 0c-1.18.037-2.09 1.022-2.09 2.201v.916m7.5 0a48.667 48.667 0 0 0-7.5 0\"\n  }));\n}\nconst ForwardRef = /*#__PURE__*/ React.forwardRef(TrashIcon);\nexport default ForwardRef;"], "names": [], "mappings": ";;;AAAA;;AACA,SAAS,UAAU,EACjB,KAAK,EACL,OAAO,EACP,GAAG,OACJ,EAAE,MAAM;IACP,OAAO,WAAW,GAAE,CAAA,GAAA,6JAAA,CAAA,gBAAmB,AAAD,EAAE,OAAO,OAAO,MAAM,CAAC;QAC3D,OAAO;QACP,MAAM;QACN,SAAS;QACT,aAAa;QACb,QAAQ;QACR,eAAe;QACf,aAAa;QACb,KAAK;QACL,mBAAmB;IACrB,GAAG,QAAQ,QAAQ,WAAW,GAAE,CAAA,GAAA,6JAAA,CAAA,gBAAmB,AAAD,EAAE,SAAS;QAC3D,IAAI;IACN,GAAG,SAAS,MAAM,WAAW,GAAE,CAAA,GAAA,6JAAA,CAAA,gBAAmB,AAAD,EAAE,QAAQ;QACzD,eAAe;QACf,gBAAgB;QAChB,GAAG;IACL;AACF;AACA,MAAM,aAAa,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,EAAE;uCACnC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1359, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Project%20By%20AI/Technoloway/Technoloway%20%28Processing%29/technoloway-simple/node_modules/%40heroicons/react/24/outline/esm/EyeIcon.js"], "sourcesContent": ["import * as React from \"react\";\nfunction EyeIcon({\n  title,\n  titleId,\n  ...props\n}, svgRef) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    xmlns: \"http://www.w3.org/2000/svg\",\n    fill: \"none\",\n    viewBox: \"0 0 24 24\",\n    strokeWidth: 1.5,\n    stroke: \"currentColor\",\n    \"aria-hidden\": \"true\",\n    \"data-slot\": \"icon\",\n    ref: svgRef,\n    \"aria-labelledby\": titleId\n  }, props), title ? /*#__PURE__*/React.createElement(\"title\", {\n    id: titleId\n  }, title) : null, /*#__PURE__*/React.createElement(\"path\", {\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\",\n    d: \"M2.036 12.322a1.012 1.012 0 0 1 0-.639C3.423 7.51 7.36 4.5 12 4.5c4.638 0 8.573 3.007 9.963 7.178.07.207.07.431 0 .639C20.577 16.49 16.64 19.5 12 19.5c-4.638 0-8.573-3.007-9.963-7.178Z\"\n  }), /*#__PURE__*/React.createElement(\"path\", {\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\",\n    d: \"M15 12a3 3 0 1 1-6 0 3 3 0 0 1 6 0Z\"\n  }));\n}\nconst ForwardRef = /*#__PURE__*/ React.forwardRef(EyeIcon);\nexport default ForwardRef;"], "names": [], "mappings": ";;;AAAA;;AACA,SAAS,QAAQ,EACf,KAAK,EACL,OAAO,EACP,GAAG,OACJ,EAAE,MAAM;IACP,OAAO,WAAW,GAAE,CAAA,GAAA,6JAAA,CAAA,gBAAmB,AAAD,EAAE,OAAO,OAAO,MAAM,CAAC;QAC3D,OAAO;QACP,MAAM;QACN,SAAS;QACT,aAAa;QACb,QAAQ;QACR,eAAe;QACf,aAAa;QACb,KAAK;QACL,mBAAmB;IACrB,GAAG,QAAQ,QAAQ,WAAW,GAAE,CAAA,GAAA,6JAAA,CAAA,gBAAmB,AAAD,EAAE,SAAS;QAC3D,IAAI;IACN,GAAG,SAAS,MAAM,WAAW,GAAE,CAAA,GAAA,6JAAA,CAAA,gBAAmB,AAAD,EAAE,QAAQ;QACzD,eAAe;QACf,gBAAgB;QAChB,GAAG;IACL,IAAI,WAAW,GAAE,CAAA,GAAA,6JAAA,CAAA,gBAAmB,AAAD,EAAE,QAAQ;QAC3C,eAAe;QACf,gBAAgB;QAChB,GAAG;IACL;AACF;AACA,MAAM,aAAa,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,EAAE;uCACnC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1405, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Project%20By%20AI/Technoloway/Technoloway%20%28Processing%29/technoloway-simple/node_modules/%40heroicons/react/24/outline/esm/CheckCircleIcon.js"], "sourcesContent": ["import * as React from \"react\";\nfunction CheckCircleIcon({\n  title,\n  titleId,\n  ...props\n}, svgRef) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    xmlns: \"http://www.w3.org/2000/svg\",\n    fill: \"none\",\n    viewBox: \"0 0 24 24\",\n    strokeWidth: 1.5,\n    stroke: \"currentColor\",\n    \"aria-hidden\": \"true\",\n    \"data-slot\": \"icon\",\n    ref: svgRef,\n    \"aria-labelledby\": titleId\n  }, props), title ? /*#__PURE__*/React.createElement(\"title\", {\n    id: titleId\n  }, title) : null, /*#__PURE__*/React.createElement(\"path\", {\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\",\n    d: \"M9 12.75 11.25 15 15 9.75M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z\"\n  }));\n}\nconst ForwardRef = /*#__PURE__*/ React.forwardRef(CheckCircleIcon);\nexport default ForwardRef;"], "names": [], "mappings": ";;;AAAA;;AACA,SAAS,gBAAgB,EACvB,KAAK,EACL,OAAO,EACP,GAAG,OACJ,EAAE,MAAM;IACP,OAAO,WAAW,GAAE,CAAA,GAAA,6JAAA,CAAA,gBAAmB,AAAD,EAAE,OAAO,OAAO,MAAM,CAAC;QAC3D,OAAO;QACP,MAAM;QACN,SAAS;QACT,aAAa;QACb,QAAQ;QACR,eAAe;QACf,aAAa;QACb,KAAK;QACL,mBAAmB;IACrB,GAAG,QAAQ,QAAQ,WAAW,GAAE,CAAA,GAAA,6JAAA,CAAA,gBAAmB,AAAD,EAAE,SAAS;QAC3D,IAAI;IACN,GAAG,SAAS,MAAM,WAAW,GAAE,CAAA,GAAA,6JAAA,CAAA,gBAAmB,AAAD,EAAE,QAAQ;QACzD,eAAe;QACf,gBAAgB;QAChB,GAAG;IACL;AACF;AACA,MAAM,aAAa,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,EAAE;uCACnC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1447, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Project%20By%20AI/Technoloway/Technoloway%20%28Processing%29/technoloway-simple/node_modules/%40heroicons/react/24/outline/esm/ShieldCheckIcon.js"], "sourcesContent": ["import * as React from \"react\";\nfunction ShieldCheckIcon({\n  title,\n  titleId,\n  ...props\n}, svgRef) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    xmlns: \"http://www.w3.org/2000/svg\",\n    fill: \"none\",\n    viewBox: \"0 0 24 24\",\n    strokeWidth: 1.5,\n    stroke: \"currentColor\",\n    \"aria-hidden\": \"true\",\n    \"data-slot\": \"icon\",\n    ref: svgRef,\n    \"aria-labelledby\": titleId\n  }, props), title ? /*#__PURE__*/React.createElement(\"title\", {\n    id: titleId\n  }, title) : null, /*#__PURE__*/React.createElement(\"path\", {\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\",\n    d: \"M9 12.75 11.25 15 15 9.75m-3-7.036A11.959 11.959 0 0 1 3.598 6 11.99 11.99 0 0 0 3 9.749c0 5.592 3.824 10.29 9 11.623 5.176-1.332 9-6.03 9-11.622 0-1.31-.21-2.571-.598-3.751h-.152c-3.196 0-6.1-1.248-8.25-3.285Z\"\n  }));\n}\nconst ForwardRef = /*#__PURE__*/ React.forwardRef(ShieldCheckIcon);\nexport default ForwardRef;"], "names": [], "mappings": ";;;AAAA;;AACA,SAAS,gBAAgB,EACvB,KAAK,EACL,OAAO,EACP,GAAG,OACJ,EAAE,MAAM;IACP,OAAO,WAAW,GAAE,CAAA,GAAA,6JAAA,CAAA,gBAAmB,AAAD,EAAE,OAAO,OAAO,MAAM,CAAC;QAC3D,OAAO;QACP,MAAM;QACN,SAAS;QACT,aAAa;QACb,QAAQ;QACR,eAAe;QACf,aAAa;QACb,KAAK;QACL,mBAAmB;IACrB,GAAG,QAAQ,QAAQ,WAAW,GAAE,CAAA,GAAA,6JAAA,CAAA,gBAAmB,AAAD,EAAE,SAAS;QAC3D,IAAI;IACN,GAAG,SAAS,MAAM,WAAW,GAAE,CAAA,GAAA,6JAAA,CAAA,gBAAmB,AAAD,EAAE,QAAQ;QACzD,eAAe;QACf,gBAAgB;QAChB,GAAG;IACL;AACF;AACA,MAAM,aAAa,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,EAAE;uCACnC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1489, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Project%20By%20AI/Technoloway/Technoloway%20%28Processing%29/technoloway-simple/node_modules/%40heroicons/react/24/outline/esm/ScaleIcon.js"], "sourcesContent": ["import * as React from \"react\";\nfunction ScaleIcon({\n  title,\n  titleId,\n  ...props\n}, svgRef) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    xmlns: \"http://www.w3.org/2000/svg\",\n    fill: \"none\",\n    viewBox: \"0 0 24 24\",\n    strokeWidth: 1.5,\n    stroke: \"currentColor\",\n    \"aria-hidden\": \"true\",\n    \"data-slot\": \"icon\",\n    ref: svgRef,\n    \"aria-labelledby\": titleId\n  }, props), title ? /*#__PURE__*/React.createElement(\"title\", {\n    id: titleId\n  }, title) : null, /*#__PURE__*/React.createElement(\"path\", {\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\",\n    d: \"M12 3v17.25m0 0c-1.472 0-2.882.265-4.185.75M12 20.25c1.472 0 2.882.265 4.185.75M18.75 4.97A48.416 48.416 0 0 0 12 4.5c-2.291 0-4.545.16-6.75.47m13.5 0c1.01.143 2.01.317 3 .52m-3-.52 2.62 10.726c.122.499-.106 1.028-.589 1.202a5.988 5.988 0 0 1-2.031.352 5.988 5.988 0 0 1-2.031-.352c-.483-.174-.711-.703-.59-1.202L18.75 4.971Zm-16.5.52c.99-.203 1.99-.377 3-.52m0 0 2.62 10.726c.122.499-.106 1.028-.589 1.202a5.989 5.989 0 0 1-2.031.352 5.989 5.989 0 0 1-2.031-.352c-.483-.174-.711-.703-.59-1.202L5.25 4.971Z\"\n  }));\n}\nconst ForwardRef = /*#__PURE__*/ React.forwardRef(ScaleIcon);\nexport default ForwardRef;"], "names": [], "mappings": ";;;AAAA;;AACA,SAAS,UAAU,EACjB,KAAK,EACL,OAAO,EACP,GAAG,OACJ,EAAE,MAAM;IACP,OAAO,WAAW,GAAE,CAAA,GAAA,6JAAA,CAAA,gBAAmB,AAAD,EAAE,OAAO,OAAO,MAAM,CAAC;QAC3D,OAAO;QACP,MAAM;QACN,SAAS;QACT,aAAa;QACb,QAAQ;QACR,eAAe;QACf,aAAa;QACb,KAAK;QACL,mBAAmB;IACrB,GAAG,QAAQ,QAAQ,WAAW,GAAE,CAAA,GAAA,6JAAA,CAAA,gBAAmB,AAAD,EAAE,SAAS;QAC3D,IAAI;IACN,GAAG,SAAS,MAAM,WAAW,GAAE,CAAA,GAAA,6JAAA,CAAA,gBAAmB,AAAD,EAAE,QAAQ;QACzD,eAAe;QACf,gBAAgB;QAChB,GAAG;IACL;AACF;AACA,MAAM,aAAa,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,EAAE;uCACnC", "ignoreList": [0], "debugId": null}}]}