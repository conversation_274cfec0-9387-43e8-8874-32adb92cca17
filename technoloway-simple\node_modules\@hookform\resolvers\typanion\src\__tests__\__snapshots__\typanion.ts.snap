// Vitest Snapshot v1, https://vitest.dev/guide/snapshot.html

exports[`typanionResolver > should return a single error from typanionResolver when validation fails 1`] = `
{
  "errors": {
    "accessToken": {
      "message": "Expected a string (got undefined)",
      "ref": undefined,
    },
    "birthYear": {
      "message": "Expected a number (got "birthYear")",
      "ref": undefined,
    },
    "email": {
      "message": "Expected to match the pattern /^\\S+@\\S+$/ (got an empty string)",
      "ref": {
        "name": "email",
      },
    },
    "enabled": {
      "message": "Expected a boolean (got undefined)",
      "ref": undefined,
    },
    "like": {
      "id": {
        "message": "Expected a number (got "z")",
        "ref": undefined,
      },
      "name": {
        "message": "Expected a string (got undefined)",
        "ref": undefined,
      },
    },
    "password": {
      "message": "Expected to match the pattern /.*[A-Z].*/ (got "___")",
      "ref": {
        "name": "password",
      },
    },
    "repeatPassword": {
      "message": "Expected a string (got undefined)",
      "ref": undefined,
    },
    "tags": [
      {
        "message": "Expected a string (got 1)",
        "ref": undefined,
      },
      {
        "message": "Expected a string (got 2)",
        "ref": undefined,
      },
      {
        "message": "Expected a string (got 3)",
        "ref": undefined,
      },
    ],
    "username": {
      "message": "Expected a string (got undefined)",
      "ref": {
        "name": "username",
      },
    },
  },
  "values": {},
}
`;
