'use client';

import { useState } from 'react';
import { motion } from 'framer-motion';
import {
  PlusIcon,
  PencilIcon,
  TrashIcon,
  EyeIcon,
  CogIcon,
  CodeBracketIcon,
  DevicePhoneMobileIcon,
  CloudIcon,
  ShieldCheckIcon,
  ChartBarIcon,
  CurrencyDollarIcon,
} from '@heroicons/react/24/outline';

// Mock data - in real app, this would come from API
const services = [
  {
    id: 1,
    name: 'Web Development',
    description: 'Modern, responsive web applications built with the latest technologies and best practices.',
    shortDescription: 'Custom web applications and websites',
    icon: 'CodeBracketIcon',
    features: ['React/Next.js', 'TypeScript', 'Tailwind CSS', 'API Integration'],
    pricing: {
      startingPrice: 5000,
      currency: 'USD',
      billingType: 'project'
    },
    category: 'Development',
    isActive: true,
    isFeatured: true,
    order: 1,
    createdAt: '2024-01-15T10:00:00Z',
    updatedAt: '2024-01-20T14:30:00Z',
  },
  {
    id: 2,
    name: 'Mobile Development',
    description: 'Native and cross-platform mobile applications for iOS and Android with seamless user experiences.',
    shortDescription: 'iOS and Android mobile applications',
    icon: 'DevicePhoneMobileIcon',
    features: ['React Native', 'Flutter', 'iOS/Android', 'App Store Deployment'],
    pricing: {
      startingPrice: 8000,
      currency: 'USD',
      billingType: 'project'
    },
    category: 'Development',
    isActive: true,
    isFeatured: true,
    order: 2,
    createdAt: '2024-01-12T11:00:00Z',
    updatedAt: '2024-01-18T16:45:00Z',
  },
  {
    id: 3,
    name: 'Cloud Solutions',
    description: 'Scalable cloud infrastructure and deployment solutions for modern applications with high availability.',
    shortDescription: 'Cloud infrastructure and DevOps',
    icon: 'CloudIcon',
    features: ['AWS/Azure/GCP', 'Docker/Kubernetes', 'CI/CD Pipelines', 'Monitoring'],
    pricing: {
      startingPrice: 3000,
      currency: 'USD',
      billingType: 'monthly'
    },
    category: 'Infrastructure',
    isActive: true,
    isFeatured: false,
    order: 3,
    createdAt: '2024-01-10T09:00:00Z',
    updatedAt: '2024-01-15T13:20:00Z',
  },
  {
    id: 4,
    name: 'API Development',
    description: 'Robust and scalable APIs and microservices architecture for enterprise applications.',
    shortDescription: 'RESTful APIs and microservices',
    icon: 'CogIcon',
    features: ['REST/GraphQL', 'Microservices', 'Database Design', 'Documentation'],
    pricing: {
      startingPrice: 4000,
      currency: 'USD',
      billingType: 'project'
    },
    category: 'Development',
    isActive: true,
    isFeatured: false,
    order: 4,
    createdAt: '2024-01-08T14:00:00Z',
    updatedAt: '2024-01-12T10:15:00Z',
  },
  {
    id: 5,
    name: 'Security & Testing',
    description: 'Comprehensive security audits and automated testing solutions to ensure application reliability.',
    shortDescription: 'Security audits and quality assurance',
    icon: 'ShieldCheckIcon',
    features: ['Security Audits', 'Automated Testing', 'Code Review', 'Performance'],
    pricing: {
      startingPrice: 2500,
      currency: 'USD',
      billingType: 'project'
    },
    category: 'Quality Assurance',
    isActive: true,
    isFeatured: false,
    order: 5,
    createdAt: '2024-01-05T16:00:00Z',
    updatedAt: '2024-01-10T12:30:00Z',
  },
  {
    id: 6,
    name: 'Analytics & Insights',
    description: 'Data-driven insights and analytics solutions for business growth and decision making.',
    shortDescription: 'Business intelligence and analytics',
    icon: 'ChartBarIcon',
    features: ['Data Analytics', 'Business Intelligence', 'Reporting', 'Dashboards'],
    pricing: {
      startingPrice: 3500,
      currency: 'USD',
      billingType: 'project'
    },
    category: 'Analytics',
    isActive: false,
    isFeatured: false,
    order: 6,
    createdAt: '2024-01-03T12:00:00Z',
    updatedAt: '2024-01-08T15:45:00Z',
  },
];

const categories = ['All', 'Development', 'Infrastructure', 'Quality Assurance', 'Analytics'];

const iconMap: { [key: string]: any } = {
  CodeBracketIcon,
  DevicePhoneMobileIcon,
  CloudIcon,
  CogIcon,
  ShieldCheckIcon,
  ChartBarIcon,
};

export default function ServicesPage() {
  const [servicesList, setServicesList] = useState(services);
  const [selectedCategory, setSelectedCategory] = useState('All');
  const [selectedService, setSelectedService] = useState<any>(null);
  const [showAddModal, setShowAddModal] = useState(false);

  const filteredServices = servicesList.filter(service => 
    selectedCategory === 'All' || service.category === selectedCategory
  );

  const handleToggleActive = (id: number) => {
    setServicesList(prev => prev.map(service => 
      service.id === id 
        ? { ...service, isActive: !service.isActive }
        : service
    ));
  };

  const handleToggleFeatured = (id: number) => {
    setServicesList(prev => prev.map(service => 
      service.id === id 
        ? { ...service, isFeatured: !service.isFeatured }
        : service
    ));
  };

  const handleDelete = (id: number) => {
    if (confirm('Are you sure you want to delete this service?')) {
      setServicesList(prev => prev.filter(service => service.id !== id));
    }
  };

  const formatPrice = (pricing: any) => {
    const { startingPrice, currency, billingType } = pricing;
    const formattedPrice = new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: currency,
      minimumFractionDigits: 0,
    }).format(startingPrice);
    
    return `${formattedPrice}${billingType === 'monthly' ? '/month' : ''}`;
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
    });
  };

  return (
    <div className="py-6">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 md:px-8">
        {/* Header */}
        <div className="md:flex md:items-center md:justify-between mb-8">
          <div className="flex-1 min-w-0">
            <h2 className="text-2xl font-bold leading-7 text-gray-900 sm:text-3xl sm:truncate">
              Services
            </h2>
            <p className="mt-1 text-sm text-gray-500">
              Manage your service offerings, pricing, and features
            </p>
          </div>
          <div className="mt-4 flex md:mt-0 md:ml-4">
            <button
              onClick={() => setShowAddModal(true)}
              className="ml-3 inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
            >
              <PlusIcon className="-ml-1 mr-2 h-5 w-5" />
              Add Service
            </button>
          </div>
        </div>

        {/* Stats */}
        <div className="mb-6 grid grid-cols-1 gap-5 sm:grid-cols-4">
          <div className="bg-white overflow-hidden shadow rounded-lg">
            <div className="p-5">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <div className="w-8 h-8 bg-blue-500 rounded-md flex items-center justify-center">
                    <span className="text-white text-sm font-bold">{servicesList.length}</span>
                  </div>
                </div>
                <div className="ml-5 w-0 flex-1">
                  <dl>
                    <dt className="text-sm font-medium text-gray-500 truncate">Total Services</dt>
                    <dd className="text-lg font-medium text-gray-900">Available</dd>
                  </dl>
                </div>
              </div>
            </div>
          </div>

          <div className="bg-white overflow-hidden shadow rounded-lg">
            <div className="p-5">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <div className="w-8 h-8 bg-green-500 rounded-md flex items-center justify-center">
                    <span className="text-white text-sm font-bold">
                      {servicesList.filter(s => s.isActive).length}
                    </span>
                  </div>
                </div>
                <div className="ml-5 w-0 flex-1">
                  <dl>
                    <dt className="text-sm font-medium text-gray-500 truncate">Active</dt>
                    <dd className="text-lg font-medium text-gray-900">Live</dd>
                  </dl>
                </div>
              </div>
            </div>
          </div>

          <div className="bg-white overflow-hidden shadow rounded-lg">
            <div className="p-5">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <div className="w-8 h-8 bg-yellow-500 rounded-md flex items-center justify-center">
                    <span className="text-white text-sm font-bold">
                      {servicesList.filter(s => s.isFeatured).length}
                    </span>
                  </div>
                </div>
                <div className="ml-5 w-0 flex-1">
                  <dl>
                    <dt className="text-sm font-medium text-gray-500 truncate">Featured</dt>
                    <dd className="text-lg font-medium text-gray-900">Highlighted</dd>
                  </dl>
                </div>
              </div>
            </div>
          </div>

          <div className="bg-white overflow-hidden shadow rounded-lg">
            <div className="p-5">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <div className="w-8 h-8 bg-purple-500 rounded-md flex items-center justify-center">
                    <span className="text-white text-sm font-bold">{categories.length - 1}</span>
                  </div>
                </div>
                <div className="ml-5 w-0 flex-1">
                  <dl>
                    <dt className="text-sm font-medium text-gray-500 truncate">Categories</dt>
                    <dd className="text-lg font-medium text-gray-900">Types</dd>
                  </dl>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Category Filter */}
        <div className="mb-6 bg-white shadow rounded-lg">
          <div className="px-4 py-5 sm:p-6">
            <div className="flex flex-wrap gap-2">
              {categories.map(category => (
                <button
                  key={category}
                  onClick={() => setSelectedCategory(category)}
                  className={`px-4 py-2 rounded-full text-sm font-medium transition-colors ${
                    selectedCategory === category
                      ? 'bg-blue-600 text-white'
                      : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                  }`}
                >
                  {category}
                </button>
              ))}
            </div>
          </div>
        </div>

        {/* Services Grid */}
        <div className="grid grid-cols-1 gap-6 lg:grid-cols-2 xl:grid-cols-3">
          {filteredServices.map((service, index) => {
            const IconComponent = iconMap[service.icon] || CogIcon;
            return (
              <motion.div
                key={service.id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.3, delay: index * 0.1 }}
                className="bg-white overflow-hidden shadow rounded-lg"
              >
                <div className="p-6">
                  <div className="flex items-center justify-between mb-4">
                    <div className="flex items-center space-x-3">
                      <div className="flex-shrink-0">
                        <div className="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center">
                          <IconComponent className="w-6 h-6 text-blue-600" />
                        </div>
                      </div>
                      <div>
                        <h3 className="text-lg font-medium text-gray-900">{service.name}</h3>
                        <p className="text-sm text-gray-500">{service.category}</p>
                      </div>
                    </div>
                    <div className="flex items-center space-x-1">
                      {service.isActive && (
                        <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                          Active
                        </span>
                      )}
                      {service.isFeatured && (
                        <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                          Featured
                        </span>
                      )}
                    </div>
                  </div>

                  <p className="text-sm text-gray-600 mb-4">
                    {service.shortDescription}
                  </p>

                  <div className="mb-4">
                    <div className="flex items-center justify-between mb-2">
                      <span className="text-sm font-medium text-gray-700">Features</span>
                      <span className="text-lg font-bold text-blue-600">
                        {formatPrice(service.pricing)}
                      </span>
                    </div>
                    <div className="flex flex-wrap gap-1">
                      {service.features.slice(0, 3).map((feature) => (
                        <span
                          key={feature}
                          className="inline-flex items-center px-2 py-1 rounded text-xs font-medium bg-gray-100 text-gray-800"
                        >
                          {feature}
                        </span>
                      ))}
                      {service.features.length > 3 && (
                        <span className="inline-flex items-center px-2 py-1 rounded text-xs font-medium bg-gray-100 text-gray-800">
                          +{service.features.length - 3} more
                        </span>
                      )}
                    </div>
                  </div>

                  <div className="flex items-center justify-between text-sm text-gray-500 mb-4">
                    <span>Order: {service.order}</span>
                    <span>Updated: {formatDate(service.updatedAt)}</span>
                  </div>

                  <div className="flex justify-end space-x-2">
                    <button
                      onClick={() => setSelectedService(service)}
                      className="text-gray-400 hover:text-blue-600 transition-colors"
                      title="View Details"
                    >
                      <EyeIcon className="h-5 w-5" />
                    </button>
                    <button
                      className="text-gray-400 hover:text-blue-600 transition-colors"
                      title="Edit"
                    >
                      <PencilIcon className="h-5 w-5" />
                    </button>
                    <button
                      onClick={() => handleToggleActive(service.id)}
                      className={`text-gray-400 hover:text-green-600 transition-colors ${
                        service.isActive ? 'text-green-600' : ''
                      }`}
                      title={service.isActive ? 'Deactivate' : 'Activate'}
                    >
                      <CogIcon className="h-5 w-5" />
                    </button>
                    <button
                      onClick={() => handleDelete(service.id)}
                      className="text-gray-400 hover:text-red-600 transition-colors"
                      title="Delete"
                    >
                      <TrashIcon className="h-5 w-5" />
                    </button>
                  </div>
                </div>
              </motion.div>
            );
          })}
        </div>

        {/* Empty State */}
        {filteredServices.length === 0 && (
          <div className="bg-white shadow rounded-lg">
            <div className="text-center py-12">
              <CogIcon className="mx-auto h-12 w-12 text-gray-400" />
              <h3 className="mt-2 text-sm font-medium text-gray-900">No services found</h3>
              <p className="mt-1 text-sm text-gray-500">
                {selectedCategory === 'All' 
                  ? 'Get started by creating your first service.'
                  : `No services found in the ${selectedCategory} category.`
                }
              </p>
              {selectedCategory === 'All' && (
                <div className="mt-6">
                  <button
                    onClick={() => setShowAddModal(true)}
                    className="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                  >
                    <PlusIcon className="-ml-1 mr-2 h-5 w-5" />
                    Add Service
                  </button>
                </div>
              )}
            </div>
          </div>
        )}

        {/* Service Detail Modal */}
        {selectedService && (
          <div className="fixed inset-0 z-50 overflow-y-auto">
            <div className="flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
              <div className="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" onClick={() => setSelectedService(null)} />
              
              <div className="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-2xl sm:w-full">
                <div className="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
                  <div className="sm:flex sm:items-start">
                    <div className="mt-3 text-center sm:mt-0 sm:text-left w-full">
                      <h3 className="text-lg leading-6 font-medium text-gray-900 mb-4">
                        {selectedService.name}
                      </h3>
                      
                      <div className="space-y-4">
                        <div>
                          <label className="block text-sm font-medium text-gray-700">Description</label>
                          <p className="mt-1 text-sm text-gray-900">{selectedService.description}</p>
                        </div>
                        
                        <div>
                          <label className="block text-sm font-medium text-gray-700">Category</label>
                          <p className="mt-1 text-sm text-gray-900">{selectedService.category}</p>
                        </div>
                        
                        <div>
                          <label className="block text-sm font-medium text-gray-700">Pricing</label>
                          <p className="mt-1 text-sm text-gray-900">{formatPrice(selectedService.pricing)}</p>
                        </div>
                        
                        <div>
                          <label className="block text-sm font-medium text-gray-700">Features</label>
                          <div className="mt-1 flex flex-wrap gap-1">
                            {selectedService.features.map((feature: string) => (
                              <span
                                key={feature}
                                className="inline-flex items-center px-2 py-1 rounded text-xs font-medium bg-blue-100 text-blue-800"
                              >
                                {feature}
                              </span>
                            ))}
                          </div>
                        </div>
                        
                        <div className="grid grid-cols-2 gap-4">
                          <div>
                            <label className="block text-sm font-medium text-gray-700">Status</label>
                            <p className="mt-1 text-sm text-gray-900">
                              {selectedService.isActive ? 'Active' : 'Inactive'}
                            </p>
                          </div>
                          <div>
                            <label className="block text-sm font-medium text-gray-700">Featured</label>
                            <p className="mt-1 text-sm text-gray-900">
                              {selectedService.isFeatured ? 'Yes' : 'No'}
                            </p>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
                <div className="bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
                  <button
                    type="button"
                    className="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-blue-600 text-base font-medium text-white hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 sm:ml-3 sm:w-auto sm:text-sm"
                  >
                    Edit Service
                  </button>
                  <button
                    type="button"
                    className="mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 sm:mt-0 sm:w-auto sm:text-sm"
                    onClick={() => setSelectedService(null)}
                  >
                    Close
                  </button>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
