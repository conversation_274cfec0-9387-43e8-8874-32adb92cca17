{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Project%20By%20AI/Technoloway/Technoloway%20%28Processing%29/technoloway-simple/src/components/header.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport Link from 'next/link';\nimport { usePathname } from 'next/navigation';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport { Bars3Icon, XMarkIcon } from '@heroicons/react/24/outline';\n\nconst navigation = [\n  { name: 'Home', href: '/' },\n  { name: 'About', href: '/about' },\n  { name: 'Services', href: '/services' },\n  { name: 'Projects', href: '/projects' },\n  { name: 'Technologies', href: '/technologies' },\n  { name: 'Contact', href: '/contact' },\n];\n\nexport function Header() {\n  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);\n  const pathname = usePathname();\n\n  const isActive = (href: string) => {\n    if (href === '/') {\n      return pathname === '/';\n    }\n    return pathname.startsWith(href);\n  };\n\n  return (\n    <header className=\"fixed inset-x-0 top-0 z-50 bg-white/80 backdrop-blur-md border-b border-gray-200\">\n      <nav className=\"container flex items-center justify-between py-4\" aria-label=\"Global\">\n        <div className=\"flex lg:flex-1\">\n          <Link href=\"/\" className=\"-m-1.5 p-1.5\">\n            <span className=\"sr-only\">Technoloway</span>\n            <div className=\"flex items-center space-x-2\">\n              <div className=\"h-8 w-8 bg-gradient-to-r from-blue-600 to-blue-400 rounded-lg flex items-center justify-center\">\n                <span className=\"text-white font-bold text-sm\">T</span>\n              </div>\n              <span className=\"text-xl font-bold text-gray-900\">Technoloway</span>\n            </div>\n          </Link>\n        </div>\n        \n        <div className=\"flex lg:hidden\">\n          <button\n            type=\"button\"\n            className=\"-m-2.5 inline-flex items-center justify-center rounded-md p-2.5 text-gray-700\"\n            onClick={() => setMobileMenuOpen(true)}\n          >\n            <span className=\"sr-only\">Open main menu</span>\n            <Bars3Icon className=\"h-6 w-6\" aria-hidden=\"true\" />\n          </button>\n        </div>\n        \n        <div className=\"hidden lg:flex lg:gap-x-8\">\n          {navigation.map((item) => (\n            <Link\n              key={item.name}\n              href={item.href}\n              className={`text-sm font-semibold leading-6 transition-colors hover:text-blue-600 ${\n                isActive(item.href) ? 'text-blue-600' : 'text-gray-900'\n              }`}\n            >\n              {item.name}\n            </Link>\n          ))}\n        </div>\n        \n        <div className=\"hidden lg:flex lg:flex-1 lg:justify-end\">\n          <Link href=\"/contact\" className=\"btn-primary\">\n            Get Started\n          </Link>\n        </div>\n      </nav>\n      \n      {/* Mobile menu */}\n      <AnimatePresence>\n        {mobileMenuOpen && (\n          <motion.div\n            initial={{ opacity: 0 }}\n            animate={{ opacity: 1 }}\n            exit={{ opacity: 0 }}\n            className=\"lg:hidden\"\n          >\n            <div className=\"fixed inset-0 z-50\" />\n            <motion.div\n              initial={{ x: '100%' }}\n              animate={{ x: 0 }}\n              exit={{ x: '100%' }}\n              transition={{ type: 'spring', damping: 25, stiffness: 200 }}\n              className=\"fixed inset-y-0 right-0 z-50 w-full overflow-y-auto bg-white px-6 py-6 sm:max-w-sm sm:ring-1 sm:ring-gray-900/10\"\n            >\n              <div className=\"flex items-center justify-between\">\n                <Link href=\"/\" className=\"-m-1.5 p-1.5\">\n                  <span className=\"sr-only\">Technoloway</span>\n                  <div className=\"flex items-center space-x-2\">\n                    <div className=\"h-8 w-8 bg-gradient-to-r from-blue-600 to-blue-400 rounded-lg flex items-center justify-center\">\n                      <span className=\"text-white font-bold text-sm\">T</span>\n                    </div>\n                    <span className=\"text-xl font-bold text-gray-900\">Technoloway</span>\n                  </div>\n                </Link>\n                <button\n                  type=\"button\"\n                  className=\"-m-2.5 rounded-md p-2.5 text-gray-700\"\n                  onClick={() => setMobileMenuOpen(false)}\n                >\n                  <span className=\"sr-only\">Close menu</span>\n                  <XMarkIcon className=\"h-6 w-6\" aria-hidden=\"true\" />\n                </button>\n              </div>\n              <div className=\"mt-6 flow-root\">\n                <div className=\"-my-6 divide-y divide-gray-500/10\">\n                  <div className=\"space-y-2 py-6\">\n                    {navigation.map((item) => (\n                      <Link\n                        key={item.name}\n                        href={item.href}\n                        className={`-mx-3 block rounded-lg px-3 py-2 text-base font-semibold leading-7 hover:bg-gray-50 ${\n                          isActive(item.href) ? 'text-blue-600' : 'text-gray-900'\n                        }`}\n                        onClick={() => setMobileMenuOpen(false)}\n                      >\n                        {item.name}\n                      </Link>\n                    ))}\n                  </div>\n                  <div className=\"py-6\">\n                    <Link\n                      href=\"/contact\"\n                      className=\"-mx-3 block rounded-lg px-3 py-2.5 text-base font-semibold leading-7 text-blue-600 hover:bg-gray-50\"\n                      onClick={() => setMobileMenuOpen(false)}\n                    >\n                      Get Started\n                    </Link>\n                  </div>\n                </div>\n              </div>\n            </motion.div>\n          </motion.div>\n        )}\n      </AnimatePresence>\n    </header>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AACA;AAAA;;;AANA;;;;;;AAQA,MAAM,aAAa;IACjB;QAAE,MAAM;QAAQ,MAAM;IAAI;IAC1B;QAAE,MAAM;QAAS,MAAM;IAAS;IAChC;QAAE,MAAM;QAAY,MAAM;IAAY;IACtC;QAAE,MAAM;QAAY,MAAM;IAAY;IACtC;QAAE,MAAM;QAAgB,MAAM;IAAgB;IAC9C;QAAE,MAAM;QAAW,MAAM;IAAW;CACrC;AAEM,SAAS;;IACd,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,WAAW,CAAA,GAAA,qIAAA,CAAA,cAAW,AAAD;IAE3B,MAAM,WAAW,CAAC;QAChB,IAAI,SAAS,KAAK;YAChB,OAAO,aAAa;QACtB;QACA,OAAO,SAAS,UAAU,CAAC;IAC7B;IAEA,qBACE,6LAAC;QAAO,WAAU;;0BAChB,6LAAC;gBAAI,WAAU;gBAAmD,cAAW;;kCAC3E,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC,+JAAA,CAAA,UAAI;4BAAC,MAAK;4BAAI,WAAU;;8CACvB,6LAAC;oCAAK,WAAU;8CAAU;;;;;;8CAC1B,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDAAK,WAAU;0DAA+B;;;;;;;;;;;sDAEjD,6LAAC;4CAAK,WAAU;sDAAkC;;;;;;;;;;;;;;;;;;;;;;;kCAKxD,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BACC,MAAK;4BACL,WAAU;4BACV,SAAS,IAAM,kBAAkB;;8CAEjC,6LAAC;oCAAK,WAAU;8CAAU;;;;;;8CAC1B,6LAAC,oNAAA,CAAA,YAAS;oCAAC,WAAU;oCAAU,eAAY;;;;;;;;;;;;;;;;;kCAI/C,6LAAC;wBAAI,WAAU;kCACZ,WAAW,GAAG,CAAC,CAAC,qBACf,6LAAC,+JAAA,CAAA,UAAI;gCAEH,MAAM,KAAK,IAAI;gCACf,WAAW,CAAC,sEAAsE,EAChF,SAAS,KAAK,IAAI,IAAI,kBAAkB,iBACxC;0CAED,KAAK,IAAI;+BANL,KAAK,IAAI;;;;;;;;;;kCAWpB,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC,+JAAA,CAAA,UAAI;4BAAC,MAAK;4BAAW,WAAU;sCAAc;;;;;;;;;;;;;;;;;0BAOlD,6LAAC,4LAAA,CAAA,kBAAe;0BACb,gCACC,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;oBAAE;oBACtB,SAAS;wBAAE,SAAS;oBAAE;oBACtB,MAAM;wBAAE,SAAS;oBAAE;oBACnB,WAAU;;sCAEV,6LAAC;4BAAI,WAAU;;;;;;sCACf,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,SAAS;gCAAE,GAAG;4BAAO;4BACrB,SAAS;gCAAE,GAAG;4BAAE;4BAChB,MAAM;gCAAE,GAAG;4BAAO;4BAClB,YAAY;gCAAE,MAAM;gCAAU,SAAS;gCAAI,WAAW;4BAAI;4BAC1D,WAAU;;8CAEV,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,+JAAA,CAAA,UAAI;4CAAC,MAAK;4CAAI,WAAU;;8DACvB,6LAAC;oDAAK,WAAU;8DAAU;;;;;;8DAC1B,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAI,WAAU;sEACb,cAAA,6LAAC;gEAAK,WAAU;0EAA+B;;;;;;;;;;;sEAEjD,6LAAC;4DAAK,WAAU;sEAAkC;;;;;;;;;;;;;;;;;;sDAGtD,6LAAC;4CACC,MAAK;4CACL,WAAU;4CACV,SAAS,IAAM,kBAAkB;;8DAEjC,6LAAC;oDAAK,WAAU;8DAAU;;;;;;8DAC1B,6LAAC,oNAAA,CAAA,YAAS;oDAAC,WAAU;oDAAU,eAAY;;;;;;;;;;;;;;;;;;8CAG/C,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;0DACZ,WAAW,GAAG,CAAC,CAAC,qBACf,6LAAC,+JAAA,CAAA,UAAI;wDAEH,MAAM,KAAK,IAAI;wDACf,WAAW,CAAC,oFAAoF,EAC9F,SAAS,KAAK,IAAI,IAAI,kBAAkB,iBACxC;wDACF,SAAS,IAAM,kBAAkB;kEAEhC,KAAK,IAAI;uDAPL,KAAK,IAAI;;;;;;;;;;0DAWpB,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC,+JAAA,CAAA,UAAI;oDACH,MAAK;oDACL,WAAU;oDACV,SAAS,IAAM,kBAAkB;8DAClC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAYrB;GA/HgB;;QAEG,qIAAA,CAAA,cAAW;;;KAFd", "debugId": null}}, {"offset": {"line": 412, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Project%20By%20AI/Technoloway/Technoloway%20%28Processing%29/technoloway-simple/src/components/footer.tsx"], "sourcesContent": ["import Link from 'next/link';\n\nexport function Footer() {\n  return (\n    <footer className=\"bg-gray-900 text-white\">\n      <div className=\"container py-16\">\n        <div className=\"grid grid-cols-1 md:grid-cols-4 gap-8\">\n          <div className=\"col-span-1 md:col-span-2\">\n            <div className=\"flex items-center space-x-2 mb-6\">\n              <div className=\"h-8 w-8 bg-gradient-to-r from-blue-600 to-blue-400 rounded-lg flex items-center justify-center\">\n                <span className=\"text-white font-bold text-sm\">T</span>\n              </div>\n              <span className=\"text-xl font-bold\">Technoloway</span>\n            </div>\n            <p className=\"text-gray-300 mb-6 max-w-md\">\n              Transforming ideas into digital reality. We build exceptional software \n              solutions that drive business growth and innovation.\n            </p>\n            <div className=\"space-y-2\">\n              <p className=\"text-gray-300\"><EMAIL></p>\n              <p className=\"text-gray-300\">+****************</p>\n              <p className=\"text-gray-300\">San Francisco, CA</p>\n            </div>\n          </div>\n          \n          <div>\n            <h3 className=\"text-lg font-semibold mb-4\">Services</h3>\n            <ul className=\"space-y-2 text-gray-300\">\n              <li><Link href=\"/#services\" className=\"hover:text-white transition-colors\">Web Development</Link></li>\n              <li><Link href=\"/#services\" className=\"hover:text-white transition-colors\">Mobile Apps</Link></li>\n              <li><Link href=\"/#services\" className=\"hover:text-white transition-colors\">Cloud Solutions</Link></li>\n              <li><Link href=\"/#services\" className=\"hover:text-white transition-colors\">API Development</Link></li>\n            </ul>\n          </div>\n          \n          <div>\n            <h3 className=\"text-lg font-semibold mb-4\">Company</h3>\n            <ul className=\"space-y-2 text-gray-300\">\n              <li><Link href=\"/team\" className=\"hover:text-white transition-colors\">About</Link></li>\n              <li><Link href=\"/team\" className=\"hover:text-white transition-colors\">Team</Link></li>\n              <li><Link href=\"/portfolio\" className=\"hover:text-white transition-colors\">Portfolio</Link></li>\n              <li><Link href=\"/blog\" className=\"hover:text-white transition-colors\">Blog</Link></li>\n              <li><Link href=\"/#contact\" className=\"hover:text-white transition-colors\">Contact</Link></li>\n            </ul>\n          </div>\n        </div>\n        \n        <div className=\"border-t border-gray-700 mt-12 pt-8 text-center\">\n          <p className=\"text-gray-400\">\n            &copy; {new Date().getFullYear()} Technoloway. All rights reserved.\n          </p>\n        </div>\n      </div>\n    </footer>\n  );\n}\n"], "names": [], "mappings": ";;;;AAAA;;;AAEO,SAAS;IACd,qBACE,6LAAC;QAAO,WAAU;kBAChB,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDAAK,WAAU;0DAA+B;;;;;;;;;;;sDAEjD,6LAAC;4CAAK,WAAU;sDAAoB;;;;;;;;;;;;8CAEtC,6LAAC;oCAAE,WAAU;8CAA8B;;;;;;8CAI3C,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAE,WAAU;sDAAgB;;;;;;sDAC7B,6LAAC;4CAAE,WAAU;sDAAgB;;;;;;sDAC7B,6LAAC;4CAAE,WAAU;sDAAgB;;;;;;;;;;;;;;;;;;sCAIjC,6LAAC;;8CACC,6LAAC;oCAAG,WAAU;8CAA6B;;;;;;8CAC3C,6LAAC;oCAAG,WAAU;;sDACZ,6LAAC;sDAAG,cAAA,6LAAC,+JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAa,WAAU;0DAAqC;;;;;;;;;;;sDAC3E,6LAAC;sDAAG,cAAA,6LAAC,+JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAa,WAAU;0DAAqC;;;;;;;;;;;sDAC3E,6LAAC;sDAAG,cAAA,6LAAC,+JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAa,WAAU;0DAAqC;;;;;;;;;;;sDAC3E,6LAAC;sDAAG,cAAA,6LAAC,+JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAa,WAAU;0DAAqC;;;;;;;;;;;;;;;;;;;;;;;sCAI/E,6LAAC;;8CACC,6LAAC;oCAAG,WAAU;8CAA6B;;;;;;8CAC3C,6LAAC;oCAAG,WAAU;;sDACZ,6LAAC;sDAAG,cAAA,6LAAC,+JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAQ,WAAU;0DAAqC;;;;;;;;;;;sDACtE,6LAAC;sDAAG,cAAA,6LAAC,+JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAQ,WAAU;0DAAqC;;;;;;;;;;;sDACtE,6LAAC;sDAAG,cAAA,6LAAC,+JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAa,WAAU;0DAAqC;;;;;;;;;;;sDAC3E,6LAAC;sDAAG,cAAA,6LAAC,+JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAQ,WAAU;0DAAqC;;;;;;;;;;;sDACtE,6LAAC;sDAAG,cAAA,6LAAC,+JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAY,WAAU;0DAAqC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAKhF,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAE,WAAU;;4BAAgB;4BACnB,IAAI,OAAO,WAAW;4BAAG;;;;;;;;;;;;;;;;;;;;;;;AAM7C;KArDgB", "debugId": null}}, {"offset": {"line": 744, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Project%20By%20AI/Technoloway/Technoloway%20%28Processing%29/technoloway-simple/src/app/technologies/page.tsx"], "sourcesContent": ["'use client';\n\nimport Link from 'next/link';\nimport { motion } from 'framer-motion';\nimport {\n  MagnifyingGlassIcon,\n  StarIcon,\n  ArrowTopRightOnSquareIcon,\n  CodeBracketIcon,\n  DevicePhoneMobileIcon,\n  CloudIcon,\n  CircleStackIcon,\n} from '@heroicons/react/24/outline';\nimport { Header } from '@/components/header';\nimport { Footer } from '@/components/footer';\nimport { useState } from 'react';\n\nconst technologies = [\n  {\n    id: 'react',\n    name: 'React',\n    description: 'A JavaScript library for building user interfaces with component-based architecture and virtual DOM.',\n    category: 'Frontend',\n    type: 'Framework',\n    proficiencyLevel: 'Expert',\n    yearsOfExperience: 5,\n    projectsUsed: 45,\n    logo: 'https://cdn.jsdelivr.net/gh/devicons/devicon/icons/react/react-original.svg',\n    website: 'https://reactjs.org',\n    documentation: 'https://reactjs.org/docs',\n    isActive: true,\n    isFeatured: true,\n    tags: ['JavaScript', 'UI', 'SPA', 'Component-based'],\n    useCases: ['Single Page Applications', 'Interactive UIs', 'Component Libraries', 'Progressive Web Apps'],\n    advantages: [\n      'Virtual DOM for optimal performance',\n      'Large ecosystem and community',\n      'Reusable component architecture',\n      'Strong developer tools'\n    ]\n  },\n  {\n    id: 'nextjs',\n    name: 'Next.js',\n    description: 'The React framework for production with server-side rendering, static site generation, and full-stack capabilities.',\n    category: 'Frontend',\n    type: 'Framework',\n    proficiencyLevel: 'Expert',\n    yearsOfExperience: 4,\n    projectsUsed: 32,\n    logo: 'https://cdn.jsdelivr.net/gh/devicons/devicon/icons/nextjs/nextjs-original.svg',\n    website: 'https://nextjs.org',\n    documentation: 'https://nextjs.org/docs',\n    isActive: true,\n    isFeatured: true,\n    tags: ['React', 'SSR', 'SSG', 'Full-stack'],\n    useCases: ['E-commerce Sites', 'Corporate Websites', 'Blogs', 'Web Applications'],\n    advantages: [\n      'Built-in SEO optimization',\n      'Automatic code splitting',\n      'API routes for backend logic',\n      'Excellent performance out of the box'\n    ]\n  },\n  {\n    id: 'nodejs',\n    name: 'Node.js',\n    description: 'JavaScript runtime built on Chrome\\'s V8 JavaScript engine for scalable server-side development.',\n    category: 'Backend',\n    type: 'Runtime',\n    proficiencyLevel: 'Expert',\n    yearsOfExperience: 6,\n    projectsUsed: 38,\n    logo: 'https://cdn.jsdelivr.net/gh/devicons/devicon/icons/nodejs/nodejs-original.svg',\n    website: 'https://nodejs.org',\n    documentation: 'https://nodejs.org/docs',\n    isActive: true,\n    isFeatured: true,\n    tags: ['JavaScript', 'Server', 'API', 'Microservices'],\n    useCases: ['REST APIs', 'Real-time Applications', 'Microservices', 'Command Line Tools'],\n    advantages: [\n      'Non-blocking I/O operations',\n      'Large package ecosystem (npm)',\n      'JavaScript everywhere',\n      'High performance for I/O intensive apps'\n    ]\n  },\n  {\n    id: 'typescript',\n    name: 'TypeScript',\n    description: 'Typed superset of JavaScript that compiles to plain JavaScript for better development experience.',\n    category: 'Language',\n    type: 'Programming Language',\n    proficiencyLevel: 'Expert',\n    yearsOfExperience: 4,\n    projectsUsed: 42,\n    logo: 'https://cdn.jsdelivr.net/gh/devicons/devicon/icons/typescript/typescript-original.svg',\n    website: 'https://www.typescriptlang.org',\n    documentation: 'https://www.typescriptlang.org/docs',\n    isActive: true,\n    isFeatured: true,\n    tags: ['JavaScript', 'Type Safety', 'Development'],\n    useCases: ['Large Applications', 'Team Development', 'Enterprise Software', 'Library Development'],\n    advantages: [\n      'Static type checking',\n      'Better IDE support and autocomplete',\n      'Easier refactoring and maintenance',\n      'Catches errors at compile time'\n    ]\n  },\n  {\n    id: 'python',\n    name: 'Python',\n    description: 'High-level programming language known for its simplicity and versatility in various domains.',\n    category: 'Language',\n    type: 'Programming Language',\n    proficiencyLevel: 'Advanced',\n    yearsOfExperience: 7,\n    projectsUsed: 28,\n    logo: 'https://cdn.jsdelivr.net/gh/devicons/devicon/icons/python/python-original.svg',\n    website: 'https://www.python.org',\n    documentation: 'https://docs.python.org',\n    isActive: true,\n    isFeatured: false,\n    tags: ['AI/ML', 'Data Science', 'Backend', 'Automation'],\n    useCases: ['Machine Learning', 'Data Analysis', 'Web Development', 'Automation Scripts'],\n    advantages: [\n      'Simple and readable syntax',\n      'Extensive library ecosystem',\n      'Great for rapid prototyping',\n      'Strong community support'\n    ]\n  },\n  {\n    id: 'postgresql',\n    name: 'PostgreSQL',\n    description: 'Advanced open-source relational database with strong standards compliance and extensibility.',\n    category: 'Database',\n    type: 'Database',\n    proficiencyLevel: 'Advanced',\n    yearsOfExperience: 5,\n    projectsUsed: 35,\n    logo: 'https://cdn.jsdelivr.net/gh/devicons/devicon/icons/postgresql/postgresql-original.svg',\n    website: 'https://www.postgresql.org',\n    documentation: 'https://www.postgresql.org/docs',\n    isActive: true,\n    isFeatured: false,\n    tags: ['SQL', 'ACID', 'Scalable', 'Open Source'],\n    useCases: ['Web Applications', 'Data Warehousing', 'Analytics', 'Enterprise Systems'],\n    advantages: [\n      'ACID compliance and reliability',\n      'Advanced SQL features',\n      'Extensible with custom functions',\n      'Excellent performance and scalability'\n    ]\n  },\n  {\n    id: 'aws',\n    name: 'AWS',\n    description: 'Amazon Web Services cloud computing platform offering a wide range of infrastructure services.',\n    category: 'Cloud',\n    type: 'Platform',\n    proficiencyLevel: 'Advanced',\n    yearsOfExperience: 4,\n    projectsUsed: 25,\n    logo: 'https://cdn.jsdelivr.net/gh/devicons/devicon/icons/amazonwebservices/amazonwebservices-original.svg',\n    website: 'https://aws.amazon.com',\n    documentation: 'https://docs.aws.amazon.com',\n    isActive: true,\n    isFeatured: true,\n    tags: ['Cloud', 'Infrastructure', 'Scalable', 'DevOps'],\n    useCases: ['Web Hosting', 'Data Storage', 'Machine Learning', 'Serverless Computing'],\n    advantages: [\n      'Global infrastructure and availability',\n      'Comprehensive service portfolio',\n      'Pay-as-you-use pricing model',\n      'Enterprise-grade security'\n    ]\n  },\n  {\n    id: 'docker',\n    name: 'Docker',\n    description: 'Platform for developing, shipping, and running applications using containerization technology.',\n    category: 'DevOps',\n    type: 'Tool',\n    proficiencyLevel: 'Advanced',\n    yearsOfExperience: 3,\n    projectsUsed: 30,\n    logo: 'https://cdn.jsdelivr.net/gh/devicons/devicon/icons/docker/docker-original.svg',\n    website: 'https://www.docker.com',\n    documentation: 'https://docs.docker.com',\n    isActive: true,\n    isFeatured: false,\n    tags: ['Containerization', 'DevOps', 'Deployment'],\n    useCases: ['Application Deployment', 'Development Environment', 'Microservices', 'CI/CD'],\n    advantages: [\n      'Consistent environments across platforms',\n      'Lightweight and efficient',\n      'Easy scaling and orchestration',\n      'Simplified deployment process'\n    ]\n  }\n];\n\nconst categories = ['All', 'Frontend', 'Backend', 'Language', 'Database', 'Cloud', 'DevOps'];\nconst proficiencyLevels = ['All', 'Beginner', 'Intermediate', 'Advanced', 'Expert'];\n\nconst getProficiencyColor = (level: string) => {\n  switch (level) {\n    case 'Expert': return 'bg-green-100 text-green-800';\n    case 'Advanced': return 'bg-blue-100 text-blue-800';\n    case 'Intermediate': return 'bg-yellow-100 text-yellow-800';\n    case 'Beginner': return 'bg-gray-100 text-gray-800';\n    default: return 'bg-gray-100 text-gray-800';\n  }\n};\n\nconst getCategoryIcon = (category: string) => {\n  switch (category) {\n    case 'Frontend': return CodeBracketIcon;\n    case 'Backend': return CloudIcon;\n    case 'Database': return CircleStackIcon;\n    case 'DevOps': return CloudIcon;\n    default: return CodeBracketIcon;\n  }\n};\n\nexport default function TechnologiesPage() {\n  const [searchTerm, setSearchTerm] = useState('');\n  const [selectedCategory, setSelectedCategory] = useState('All');\n  const [selectedProficiency, setSelectedProficiency] = useState('All');\n\n  const filteredTechnologies = technologies.filter(tech => {\n    const matchesSearch = tech.name.toLowerCase().includes(searchTerm.toLowerCase()) ||\n                         tech.description.toLowerCase().includes(searchTerm.toLowerCase()) ||\n                         tech.tags.some(tag => tag.toLowerCase().includes(searchTerm.toLowerCase()));\n    const matchesCategory = selectedCategory === 'All' || tech.category === selectedCategory;\n    const matchesProficiency = selectedProficiency === 'All' || tech.proficiencyLevel === selectedProficiency;\n    \n    return matchesSearch && matchesCategory && matchesProficiency;\n  });\n\n  return (\n    <div className=\"min-h-screen bg-white\">\n      <Header />\n\n      <main className=\"pt-20\">\n        {/* Hero Section */}\n        <section className=\"relative py-24 bg-gradient-to-br from-gray-50 via-white to-gray-100\">\n          <div className=\"container\">\n            <motion.div\n              initial={{ opacity: 0, y: 20 }}\n              animate={{ opacity: 1, y: 0 }}\n              transition={{ duration: 0.6 }}\n              className=\"text-center max-w-4xl mx-auto\"\n            >\n              <motion.h1\n                initial={{ opacity: 0, y: 20 }}\n                animate={{ opacity: 1, y: 0 }}\n                transition={{ duration: 0.6, delay: 0.1 }}\n                className=\"text-4xl font-bold tracking-tight text-gray-900 sm:text-6xl lg:text-7xl\"\n              >\n                Our <span className=\"gradient-text\">Technology Stack</span>\n              </motion.h1>\n\n              <motion.p\n                initial={{ opacity: 0, y: 20 }}\n                animate={{ opacity: 1, y: 0 }}\n                transition={{ duration: 0.6, delay: 0.2 }}\n                className=\"mt-6 text-lg leading-8 text-gray-600 sm:text-xl lg:text-2xl\"\n              >\n                We leverage cutting-edge technologies and proven frameworks to build\n                scalable, performant, and maintainable solutions for our clients.\n              </motion.p>\n            </motion.div>\n          </div>\n        </section>\n\n        {/* Stats Section */}\n        <section className=\"py-16 bg-white\">\n          <div className=\"container\">\n            <div className=\"grid grid-cols-2 gap-8 md:grid-cols-4\">\n              {[\n                { label: 'Technologies Mastered', value: '100+' },\n                { label: 'Years Combined Experience', value: '50+' },\n                { label: 'Projects Delivered', value: '500+' },\n                { label: 'Expert Level Skills', value: '25+' }\n              ].map((stat, index) => (\n                <motion.div\n                  key={stat.label}\n                  initial={{ opacity: 0, scale: 0.8 }}\n                  whileInView={{ opacity: 1, scale: 1 }}\n                  transition={{ duration: 0.5, delay: index * 0.1 }}\n                  viewport={{ once: true }}\n                  className=\"text-center\"\n                >\n                  <div className=\"text-3xl font-bold text-blue-600 sm:text-4xl\">\n                    {stat.value}\n                  </div>\n                  <div className=\"mt-2 text-sm text-gray-600\">\n                    {stat.label}\n                  </div>\n                </motion.div>\n              ))}\n            </div>\n          </div>\n        </section>\n\n        {/* Filters Section */}\n        <section className=\"py-8 bg-gray-50\">\n          <div className=\"container\">\n            <div className=\"flex flex-col lg:flex-row gap-4 items-center justify-between\">\n              {/* Search */}\n              <div className=\"relative flex-1 max-w-md\">\n                <div className=\"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\">\n                  <MagnifyingGlassIcon className=\"h-5 w-5 text-gray-400\" />\n                </div>\n                <input\n                  type=\"text\"\n                  placeholder=\"Search technologies...\"\n                  value={searchTerm}\n                  onChange={(e) => setSearchTerm(e.target.value)}\n                  className=\"block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-blue-500 focus:border-blue-500 sm:text-sm\"\n                />\n              </div>\n\n              {/* Filters */}\n              <div className=\"flex gap-4\">\n                <select\n                  value={selectedCategory}\n                  onChange={(e) => setSelectedCategory(e.target.value)}\n                  className=\"block pl-3 pr-10 py-2 text-base border border-gray-300 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm rounded-md\"\n                >\n                  {categories.map(category => (\n                    <option key={category} value={category}>{category}</option>\n                  ))}\n                </select>\n\n                <select\n                  value={selectedProficiency}\n                  onChange={(e) => setSelectedProficiency(e.target.value)}\n                  className=\"block pl-3 pr-10 py-2 text-base border border-gray-300 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm rounded-md\"\n                >\n                  {proficiencyLevels.map(level => (\n                    <option key={level} value={level}>{level}</option>\n                  ))}\n                </select>\n              </div>\n            </div>\n          </div>\n        </section>\n\n        {/* Technologies Grid */}\n        <section className=\"py-16 bg-white\">\n          <div className=\"container\">\n            <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6\">\n              {filteredTechnologies.map((tech, index) => {\n                const CategoryIcon = getCategoryIcon(tech.category);\n                return (\n                  <motion.div\n                    key={tech.id}\n                    initial={{ opacity: 0, y: 20 }}\n                    whileInView={{ opacity: 1, y: 0 }}\n                    transition={{ duration: 0.6, delay: index * 0.1 }}\n                    viewport={{ once: true }}\n                    className=\"group bg-white p-6 rounded-2xl border border-gray-200 hover:border-blue-300 hover:shadow-xl transition-all duration-300\"\n                  >\n                    <div className=\"flex items-center justify-between mb-4\">\n                      <div className=\"flex items-center space-x-3\">\n                        <div className=\"flex-shrink-0\">\n                          <img\n                            src={tech.logo}\n                            alt={tech.name}\n                            className=\"w-10 h-10 object-contain\"\n                          />\n                        </div>\n                        <div>\n                          <h3 className=\"text-lg font-medium text-gray-900\">{tech.name}</h3>\n                          <p className=\"text-sm text-gray-500\">{tech.category}</p>\n                        </div>\n                      </div>\n                      {tech.isFeatured && (\n                        <StarIcon className=\"w-5 h-5 text-yellow-400 fill-current\" />\n                      )}\n                    </div>\n\n                    <p className=\"text-sm text-gray-600 mb-4 line-clamp-2\">\n                      {tech.description}\n                    </p>\n\n                    <div className=\"space-y-2 mb-4\">\n                      <div className=\"flex items-center justify-between\">\n                        <span className=\"text-sm text-gray-500\">Proficiency</span>\n                        <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getProficiencyColor(tech.proficiencyLevel)}`}>\n                          {tech.proficiencyLevel}\n                        </span>\n                      </div>\n                      <div className=\"flex items-center justify-between\">\n                        <span className=\"text-sm text-gray-500\">Experience</span>\n                        <span className=\"text-sm font-medium text-gray-900\">{tech.yearsOfExperience} years</span>\n                      </div>\n                      <div className=\"flex items-center justify-between\">\n                        <span className=\"text-sm text-gray-500\">Projects</span>\n                        <span className=\"text-sm font-medium text-gray-900\">{tech.projectsUsed}</span>\n                      </div>\n                    </div>\n\n                    <div className=\"mb-4\">\n                      <div className=\"flex flex-wrap gap-1\">\n                        {tech.tags.slice(0, 3).map((tag) => (\n                          <span\n                            key={tag}\n                            className=\"inline-flex items-center px-2 py-1 rounded text-xs font-medium bg-gray-100 text-gray-800\"\n                          >\n                            {tag}\n                          </span>\n                        ))}\n                        {tech.tags.length > 3 && (\n                          <span className=\"inline-flex items-center px-2 py-1 rounded text-xs font-medium bg-gray-100 text-gray-800\">\n                            +{tech.tags.length - 3}\n                          </span>\n                        )}\n                      </div>\n                    </div>\n\n                    <div className=\"flex justify-between items-center\">\n                      <Link\n                        href={`/technologies/${tech.id}`}\n                        className=\"text-blue-600 hover:text-blue-700 font-medium text-sm group/link\"\n                      >\n                        Learn More\n                        <ArrowTopRightOnSquareIcon className=\"ml-1 h-4 w-4 inline transition-transform group-hover/link:translate-x-0.5 group-hover/link:-translate-y-0.5\" />\n                      </Link>\n                      <a\n                        href={tech.website}\n                        target=\"_blank\"\n                        rel=\"noopener noreferrer\"\n                        className=\"text-gray-400 hover:text-gray-600 transition-colors\"\n                        title=\"Official Website\"\n                      >\n                        <ArrowTopRightOnSquareIcon className=\"h-4 w-4\" />\n                      </a>\n                    </div>\n                  </motion.div>\n                );\n              })}\n            </div>\n\n            {/* Empty State */}\n            {filteredTechnologies.length === 0 && (\n              <div className=\"text-center py-12\">\n                <div className=\"text-gray-400 mb-4\">\n                  <CodeBracketIcon className=\"mx-auto h-12 w-12\" />\n                </div>\n                <h3 className=\"text-lg font-medium text-gray-900 mb-2\">No technologies found</h3>\n                <p className=\"text-gray-600\">\n                  Try adjusting your search criteria or filters.\n                </p>\n              </div>\n            )}\n          </div>\n        </section>\n\n        {/* Featured Technologies */}\n        <section className=\"py-16 bg-gray-50\">\n          <div className=\"container\">\n            <motion.div\n              initial={{ opacity: 0, y: 20 }}\n              whileInView={{ opacity: 1, y: 0 }}\n              transition={{ duration: 0.6 }}\n              viewport={{ once: true }}\n              className=\"text-center mb-12\"\n            >\n              <h2 className=\"text-3xl font-bold text-gray-900 mb-4\">\n                Our <span className=\"gradient-text\">Core Technologies</span>\n              </h2>\n              <p className=\"text-lg text-gray-600 max-w-3xl mx-auto\">\n                These are the technologies we specialize in and use most frequently\n                to deliver exceptional results for our clients.\n              </p>\n            </motion.div>\n\n            <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6\">\n              {technologies.filter(tech => tech.isFeatured).map((tech, index) => (\n                <motion.div\n                  key={tech.id}\n                  initial={{ opacity: 0, y: 20 }}\n                  whileInView={{ opacity: 1, y: 0 }}\n                  transition={{ duration: 0.6, delay: index * 0.1 }}\n                  viewport={{ once: true }}\n                  className=\"text-center group\"\n                >\n                  <div className=\"flex items-center justify-center w-20 h-20 bg-white rounded-2xl mx-auto mb-4 shadow-lg group-hover:shadow-xl transition-shadow\">\n                    <img\n                      src={tech.logo}\n                      alt={tech.name}\n                      className=\"w-12 h-12 object-contain\"\n                    />\n                  </div>\n                  <h3 className=\"text-lg font-semibold text-gray-900 mb-2\">\n                    {tech.name}\n                  </h3>\n                  <p className=\"text-sm text-gray-600 mb-2\">\n                    {tech.yearsOfExperience} years experience\n                  </p>\n                  <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getProficiencyColor(tech.proficiencyLevel)}`}>\n                    {tech.proficiencyLevel}\n                  </span>\n                </motion.div>\n              ))}\n            </div>\n          </div>\n        </section>\n\n        {/* CTA Section */}\n        <section className=\"py-24 bg-gradient-to-r from-blue-600 to-purple-600\">\n          <div className=\"container\">\n            <motion.div\n              initial={{ opacity: 0, y: 20 }}\n              whileInView={{ opacity: 1, y: 0 }}\n              transition={{ duration: 0.6 }}\n              viewport={{ once: true }}\n              className=\"text-center\"\n            >\n              <h2 className=\"text-3xl font-bold tracking-tight text-white sm:text-4xl\">\n                Need Help Choosing the Right Technology?\n              </h2>\n              <p className=\"mt-4 text-lg text-blue-100 max-w-3xl mx-auto\">\n                Our experts can help you select the perfect technology stack for your project.\n                Let's discuss your requirements and find the best solution.\n              </p>\n              <div className=\"mt-8 flex flex-col sm:flex-row gap-4 justify-center\">\n                <Link\n                  href=\"/contact\"\n                  className=\"inline-flex items-center px-8 py-3 border border-transparent text-base font-medium rounded-md text-blue-600 bg-white hover:bg-gray-50 transition-colors\"\n                >\n                  Get Technology Consultation\n                </Link>\n                <Link\n                  href=\"/services\"\n                  className=\"inline-flex items-center px-8 py-3 border-2 border-white text-base font-medium rounded-md text-white hover:bg-white hover:text-blue-600 transition-colors\"\n                >\n                  View Our Services\n                </Link>\n              </div>\n            </motion.div>\n          </div>\n        </section>\n      </main>\n\n      <Footer />\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AASA;AACA;AACA;;;AAfA;;;;;;;AAiBA,MAAM,eAAe;IACnB;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,UAAU;QACV,MAAM;QACN,kBAAkB;QAClB,mBAAmB;QACnB,cAAc;QACd,MAAM;QACN,SAAS;QACT,eAAe;QACf,UAAU;QACV,YAAY;QACZ,MAAM;YAAC;YAAc;YAAM;YAAO;SAAkB;QACpD,UAAU;YAAC;YAA4B;YAAmB;YAAuB;SAAuB;QACxG,YAAY;YACV;YACA;YACA;YACA;SACD;IACH;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,UAAU;QACV,MAAM;QACN,kBAAkB;QAClB,mBAAmB;QACnB,cAAc;QACd,MAAM;QACN,SAAS;QACT,eAAe;QACf,UAAU;QACV,YAAY;QACZ,MAAM;YAAC;YAAS;YAAO;YAAO;SAAa;QAC3C,UAAU;YAAC;YAAoB;YAAsB;YAAS;SAAmB;QACjF,YAAY;YACV;YACA;YACA;YACA;SACD;IACH;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,UAAU;QACV,MAAM;QACN,kBAAkB;QAClB,mBAAmB;QACnB,cAAc;QACd,MAAM;QACN,SAAS;QACT,eAAe;QACf,UAAU;QACV,YAAY;QACZ,MAAM;YAAC;YAAc;YAAU;YAAO;SAAgB;QACtD,UAAU;YAAC;YAAa;YAA0B;YAAiB;SAAqB;QACxF,YAAY;YACV;YACA;YACA;YACA;SACD;IACH;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,UAAU;QACV,MAAM;QACN,kBAAkB;QAClB,mBAAmB;QACnB,cAAc;QACd,MAAM;QACN,SAAS;QACT,eAAe;QACf,UAAU;QACV,YAAY;QACZ,MAAM;YAAC;YAAc;YAAe;SAAc;QAClD,UAAU;YAAC;YAAsB;YAAoB;YAAuB;SAAsB;QAClG,YAAY;YACV;YACA;YACA;YACA;SACD;IACH;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,UAAU;QACV,MAAM;QACN,kBAAkB;QAClB,mBAAmB;QACnB,cAAc;QACd,MAAM;QACN,SAAS;QACT,eAAe;QACf,UAAU;QACV,YAAY;QACZ,MAAM;YAAC;YAAS;YAAgB;YAAW;SAAa;QACxD,UAAU;YAAC;YAAoB;YAAiB;YAAmB;SAAqB;QACxF,YAAY;YACV;YACA;YACA;YACA;SACD;IACH;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,UAAU;QACV,MAAM;QACN,kBAAkB;QAClB,mBAAmB;QACnB,cAAc;QACd,MAAM;QACN,SAAS;QACT,eAAe;QACf,UAAU;QACV,YAAY;QACZ,MAAM;YAAC;YAAO;YAAQ;YAAY;SAAc;QAChD,UAAU;YAAC;YAAoB;YAAoB;YAAa;SAAqB;QACrF,YAAY;YACV;YACA;YACA;YACA;SACD;IACH;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,UAAU;QACV,MAAM;QACN,kBAAkB;QAClB,mBAAmB;QACnB,cAAc;QACd,MAAM;QACN,SAAS;QACT,eAAe;QACf,UAAU;QACV,YAAY;QACZ,MAAM;YAAC;YAAS;YAAkB;YAAY;SAAS;QACvD,UAAU;YAAC;YAAe;YAAgB;YAAoB;SAAuB;QACrF,YAAY;YACV;YACA;YACA;YACA;SACD;IACH;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,UAAU;QACV,MAAM;QACN,kBAAkB;QAClB,mBAAmB;QACnB,cAAc;QACd,MAAM;QACN,SAAS;QACT,eAAe;QACf,UAAU;QACV,YAAY;QACZ,MAAM;YAAC;YAAoB;YAAU;SAAa;QAClD,UAAU;YAAC;YAA0B;YAA2B;YAAiB;SAAQ;QACzF,YAAY;YACV;YACA;YACA;YACA;SACD;IACH;CACD;AAED,MAAM,aAAa;IAAC;IAAO;IAAY;IAAW;IAAY;IAAY;IAAS;CAAS;AAC5F,MAAM,oBAAoB;IAAC;IAAO;IAAY;IAAgB;IAAY;CAAS;AAEnF,MAAM,sBAAsB,CAAC;IAC3B,OAAQ;QACN,KAAK;YAAU,OAAO;QACtB,KAAK;YAAY,OAAO;QACxB,KAAK;YAAgB,OAAO;QAC5B,KAAK;YAAY,OAAO;QACxB;YAAS,OAAO;IAClB;AACF;AAEA,MAAM,kBAAkB,CAAC;IACvB,OAAQ;QACN,KAAK;YAAY,OAAO,gOAAA,CAAA,kBAAe;QACvC,KAAK;YAAW,OAAO,oNAAA,CAAA,YAAS;QAChC,KAAK;YAAY,OAAO,gOAAA,CAAA,kBAAe;QACvC,KAAK;YAAU,OAAO,oNAAA,CAAA,YAAS;QAC/B;YAAS,OAAO,gOAAA,CAAA,kBAAe;IACjC;AACF;AAEe,SAAS;;IACtB,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,CAAC,qBAAqB,uBAAuB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE/D,MAAM,uBAAuB,aAAa,MAAM,CAAC,CAAA;QAC/C,MAAM,gBAAgB,KAAK,IAAI,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW,OACxD,KAAK,WAAW,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW,OAC9D,KAAK,IAAI,CAAC,IAAI,CAAC,CAAA,MAAO,IAAI,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW;QAC5F,MAAM,kBAAkB,qBAAqB,SAAS,KAAK,QAAQ,KAAK;QACxE,MAAM,qBAAqB,wBAAwB,SAAS,KAAK,gBAAgB,KAAK;QAEtF,OAAO,iBAAiB,mBAAmB;IAC7C;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC,+HAAA,CAAA,SAAM;;;;;0BAEP,6LAAC;gBAAK,WAAU;;kCAEd,6LAAC;wBAAQ,WAAU;kCACjB,cAAA,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gCACT,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAG;gCAC7B,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAE;gCAC5B,YAAY;oCAAE,UAAU;gCAAI;gCAC5B,WAAU;;kDAEV,6LAAC,6LAAA,CAAA,SAAM,CAAC,EAAE;wCACR,SAAS;4CAAE,SAAS;4CAAG,GAAG;wCAAG;wCAC7B,SAAS;4CAAE,SAAS;4CAAG,GAAG;wCAAE;wCAC5B,YAAY;4CAAE,UAAU;4CAAK,OAAO;wCAAI;wCACxC,WAAU;;4CACX;0DACK,6LAAC;gDAAK,WAAU;0DAAgB;;;;;;;;;;;;kDAGtC,6LAAC,6LAAA,CAAA,SAAM,CAAC,CAAC;wCACP,SAAS;4CAAE,SAAS;4CAAG,GAAG;wCAAG;wCAC7B,SAAS;4CAAE,SAAS;4CAAG,GAAG;wCAAE;wCAC5B,YAAY;4CAAE,UAAU;4CAAK,OAAO;wCAAI;wCACxC,WAAU;kDACX;;;;;;;;;;;;;;;;;;;;;;kCASP,6LAAC;wBAAQ,WAAU;kCACjB,cAAA,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;0CACZ;oCACC;wCAAE,OAAO;wCAAyB,OAAO;oCAAO;oCAChD;wCAAE,OAAO;wCAA6B,OAAO;oCAAM;oCACnD;wCAAE,OAAO;wCAAsB,OAAO;oCAAO;oCAC7C;wCAAE,OAAO;wCAAuB,OAAO;oCAAM;iCAC9C,CAAC,GAAG,CAAC,CAAC,MAAM,sBACX,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wCAET,SAAS;4CAAE,SAAS;4CAAG,OAAO;wCAAI;wCAClC,aAAa;4CAAE,SAAS;4CAAG,OAAO;wCAAE;wCACpC,YAAY;4CAAE,UAAU;4CAAK,OAAO,QAAQ;wCAAI;wCAChD,UAAU;4CAAE,MAAM;wCAAK;wCACvB,WAAU;;0DAEV,6LAAC;gDAAI,WAAU;0DACZ,KAAK,KAAK;;;;;;0DAEb,6LAAC;gDAAI,WAAU;0DACZ,KAAK,KAAK;;;;;;;uCAXR,KAAK,KAAK;;;;;;;;;;;;;;;;;;;;kCAoBzB,6LAAC;wBAAQ,WAAU;kCACjB,cAAA,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;;kDAEb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC,wOAAA,CAAA,sBAAmB;oDAAC,WAAU;;;;;;;;;;;0DAEjC,6LAAC;gDACC,MAAK;gDACL,aAAY;gDACZ,OAAO;gDACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;gDAC7C,WAAU;;;;;;;;;;;;kDAKd,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDACC,OAAO;gDACP,UAAU,CAAC,IAAM,oBAAoB,EAAE,MAAM,CAAC,KAAK;gDACnD,WAAU;0DAET,WAAW,GAAG,CAAC,CAAA,yBACd,6LAAC;wDAAsB,OAAO;kEAAW;uDAA5B;;;;;;;;;;0DAIjB,6LAAC;gDACC,OAAO;gDACP,UAAU,CAAC,IAAM,uBAAuB,EAAE,MAAM,CAAC,KAAK;gDACtD,WAAU;0DAET,kBAAkB,GAAG,CAAC,CAAA,sBACrB,6LAAC;wDAAmB,OAAO;kEAAQ;uDAAtB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCASzB,6LAAC;wBAAQ,WAAU;kCACjB,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;8CACZ,qBAAqB,GAAG,CAAC,CAAC,MAAM;wCAC/B,MAAM,eAAe,gBAAgB,KAAK,QAAQ;wCAClD,qBACE,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4CAET,SAAS;gDAAE,SAAS;gDAAG,GAAG;4CAAG;4CAC7B,aAAa;gDAAE,SAAS;gDAAG,GAAG;4CAAE;4CAChC,YAAY;gDAAE,UAAU;gDAAK,OAAO,QAAQ;4CAAI;4CAChD,UAAU;gDAAE,MAAM;4CAAK;4CACvB,WAAU;;8DAEV,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAI,WAAU;8EACb,cAAA,6LAAC;wEACC,KAAK,KAAK,IAAI;wEACd,KAAK,KAAK,IAAI;wEACd,WAAU;;;;;;;;;;;8EAGd,6LAAC;;sFACC,6LAAC;4EAAG,WAAU;sFAAqC,KAAK,IAAI;;;;;;sFAC5D,6LAAC;4EAAE,WAAU;sFAAyB,KAAK,QAAQ;;;;;;;;;;;;;;;;;;wDAGtD,KAAK,UAAU,kBACd,6LAAC,kNAAA,CAAA,WAAQ;4DAAC,WAAU;;;;;;;;;;;;8DAIxB,6LAAC;oDAAE,WAAU;8DACV,KAAK,WAAW;;;;;;8DAGnB,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAK,WAAU;8EAAwB;;;;;;8EACxC,6LAAC;oEAAK,WAAW,CAAC,wEAAwE,EAAE,oBAAoB,KAAK,gBAAgB,GAAG;8EACrI,KAAK,gBAAgB;;;;;;;;;;;;sEAG1B,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAK,WAAU;8EAAwB;;;;;;8EACxC,6LAAC;oEAAK,WAAU;;wEAAqC,KAAK,iBAAiB;wEAAC;;;;;;;;;;;;;sEAE9E,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAK,WAAU;8EAAwB;;;;;;8EACxC,6LAAC;oEAAK,WAAU;8EAAqC,KAAK,YAAY;;;;;;;;;;;;;;;;;;8DAI1E,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC;wDAAI,WAAU;;4DACZ,KAAK,IAAI,CAAC,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,oBAC1B,6LAAC;oEAEC,WAAU;8EAET;mEAHI;;;;;4DAMR,KAAK,IAAI,CAAC,MAAM,GAAG,mBAClB,6LAAC;gEAAK,WAAU;;oEAA2F;oEACvG,KAAK,IAAI,CAAC,MAAM,GAAG;;;;;;;;;;;;;;;;;;8DAM7B,6LAAC;oDAAI,WAAU;;sEACb,6LAAC,+JAAA,CAAA,UAAI;4DACH,MAAM,CAAC,cAAc,EAAE,KAAK,EAAE,EAAE;4DAChC,WAAU;;gEACX;8EAEC,6LAAC,oPAAA,CAAA,4BAAyB;oEAAC,WAAU;;;;;;;;;;;;sEAEvC,6LAAC;4DACC,MAAM,KAAK,OAAO;4DAClB,QAAO;4DACP,KAAI;4DACJ,WAAU;4DACV,OAAM;sEAEN,cAAA,6LAAC,oPAAA,CAAA,4BAAyB;gEAAC,WAAU;;;;;;;;;;;;;;;;;;2CAhFpC,KAAK,EAAE;;;;;oCAqFlB;;;;;;gCAID,qBAAqB,MAAM,KAAK,mBAC/B,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC,gOAAA,CAAA,kBAAe;gDAAC,WAAU;;;;;;;;;;;sDAE7B,6LAAC;4CAAG,WAAU;sDAAyC;;;;;;sDACvD,6LAAC;4CAAE,WAAU;sDAAgB;;;;;;;;;;;;;;;;;;;;;;;kCASrC,6LAAC;wBAAQ,WAAU;kCACjB,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oCACT,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAG;oCAC7B,aAAa;wCAAE,SAAS;wCAAG,GAAG;oCAAE;oCAChC,YAAY;wCAAE,UAAU;oCAAI;oCAC5B,UAAU;wCAAE,MAAM;oCAAK;oCACvB,WAAU;;sDAEV,6LAAC;4CAAG,WAAU;;gDAAwC;8DAChD,6LAAC;oDAAK,WAAU;8DAAgB;;;;;;;;;;;;sDAEtC,6LAAC;4CAAE,WAAU;sDAA0C;;;;;;;;;;;;8CAMzD,6LAAC;oCAAI,WAAU;8CACZ,aAAa,MAAM,CAAC,CAAA,OAAQ,KAAK,UAAU,EAAE,GAAG,CAAC,CAAC,MAAM,sBACvD,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4CAET,SAAS;gDAAE,SAAS;gDAAG,GAAG;4CAAG;4CAC7B,aAAa;gDAAE,SAAS;gDAAG,GAAG;4CAAE;4CAChC,YAAY;gDAAE,UAAU;gDAAK,OAAO,QAAQ;4CAAI;4CAChD,UAAU;gDAAE,MAAM;4CAAK;4CACvB,WAAU;;8DAEV,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC;wDACC,KAAK,KAAK,IAAI;wDACd,KAAK,KAAK,IAAI;wDACd,WAAU;;;;;;;;;;;8DAGd,6LAAC;oDAAG,WAAU;8DACX,KAAK,IAAI;;;;;;8DAEZ,6LAAC;oDAAE,WAAU;;wDACV,KAAK,iBAAiB;wDAAC;;;;;;;8DAE1B,6LAAC;oDAAK,WAAW,CAAC,wEAAwE,EAAE,oBAAoB,KAAK,gBAAgB,GAAG;8DACrI,KAAK,gBAAgB;;;;;;;2CArBnB,KAAK,EAAE;;;;;;;;;;;;;;;;;;;;;kCA8BtB,6LAAC;wBAAQ,WAAU;kCACjB,cAAA,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gCACT,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAG;gCAC7B,aAAa;oCAAE,SAAS;oCAAG,GAAG;gCAAE;gCAChC,YAAY;oCAAE,UAAU;gCAAI;gCAC5B,UAAU;oCAAE,MAAM;gCAAK;gCACvB,WAAU;;kDAEV,6LAAC;wCAAG,WAAU;kDAA2D;;;;;;kDAGzE,6LAAC;wCAAE,WAAU;kDAA+C;;;;;;kDAI5D,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,+JAAA,CAAA,UAAI;gDACH,MAAK;gDACL,WAAU;0DACX;;;;;;0DAGD,6LAAC,+JAAA,CAAA,UAAI;gDACH,MAAK;gDACL,WAAU;0DACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BASX,6LAAC,+HAAA,CAAA,SAAM;;;;;;;;;;;AAGb;GAtUwB;KAAA", "debugId": null}}]}