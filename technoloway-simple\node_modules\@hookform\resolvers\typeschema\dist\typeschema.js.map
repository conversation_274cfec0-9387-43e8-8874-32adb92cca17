{"version": 3, "file": "typeschema.js", "sources": ["../src/typeschema.ts"], "sourcesContent": ["import { toNestErrors, validateFieldsNatively } from '@hookform/resolvers';\nimport { StandardSchemaV1 } from '@standard-schema/spec';\nimport {\n  FieldError,\n  FieldErrors,\n  FieldValues,\n  Resolver,\n  appendErrors,\n} from 'react-hook-form';\n\nconst parseErrorSchema = (\n  typeschemaErrors: readonly StandardSchemaV1.Issue[],\n  validateAllFieldCriteria: boolean,\n): FieldErrors => {\n  const schemaErrors = Object.assign([], typeschemaErrors);\n  const errors: Record<string, FieldError> = {};\n\n  for (; schemaErrors.length; ) {\n    const error = typeschemaErrors[0];\n\n    if (!error.path) {\n      continue;\n    }\n    const _path = error.path.join('.');\n\n    if (!errors[_path]) {\n      errors[_path] = { message: error.message, type: '' };\n    }\n\n    if (validateAllFieldCriteria) {\n      const types = errors[_path].types;\n      const messages = types && types[''];\n\n      errors[_path] = appendErrors(\n        _path,\n        validateAllFieldCriteria,\n        errors,\n        '',\n        messages\n          ? ([] as string[]).concat(messages as string[], error.message)\n          : error.message,\n      ) as FieldError;\n    }\n\n    schemaErrors.shift();\n  }\n\n  return errors;\n};\n\nexport function typeschemaResolver<Input extends FieldValues, Context, Output>(\n  schema: StandardSchemaV1<Input, Output>,\n  _schemaOptions?: never,\n  resolverOptions?: {\n    raw?: false;\n  },\n): Resolver<Input, Context, Output>;\n\nexport function typeschemaResolver<Input extends FieldValues, Context, Output>(\n  schema: StandardSchemaV1<Input, Output>,\n  _schemaOptions: never | undefined,\n  resolverOptions: {\n    raw: true;\n  },\n): Resolver<Input, Context, Input>;\n\n/**\n * Creates a resolver for react-hook-form using TypeSchema validation\n * @param {any} schema - The TypeSchema to validate against\n * @param {any} _ - Unused parameter\n * @param {Object} resolverOptions - Additional resolver configuration\n * @param {string} [resolverOptions.mode='async'] - Validation mode\n * @returns {Resolver} A resolver function compatible with react-hook-form\n * @example\n * const schema = z.object({\n *   name: z.string().required(),\n *   age: z.number().required(),\n * });\n *\n * useForm({\n *   resolver: typeschemaResolver(schema)\n * });\n */\nexport function typeschemaResolver<Input extends FieldValues, Context, Output>(\n  schema: StandardSchemaV1<Input, Output>,\n  _schemaOptions?: never,\n  resolverOptions: {\n    raw?: boolean;\n  } = {},\n): Resolver<Input, Context, Output | Input> {\n  return async (values, _, options) => {\n    let result = schema['~standard'].validate(values);\n    if (result instanceof Promise) {\n      result = await result;\n    }\n\n    if (result.issues) {\n      const errors = parseErrorSchema(\n        result.issues,\n        !options.shouldUseNativeValidation && options.criteriaMode === 'all',\n      );\n\n      return {\n        values: {},\n        errors: toNestErrors(errors, options),\n      };\n    }\n\n    options.shouldUseNativeValidation && validateFieldsNatively({}, options);\n\n    return {\n      values: resolverOptions.raw ? Object.assign({}, values) : result.value,\n      errors: {},\n    };\n  };\n}\n"], "names": ["schema", "_schemaOptions", "resolverOptions", "values", "_", "options", "_temp2", "result", "issues", "errors", "typeschemaErrors", "validateAllFieldCriteria", "schemaErrors", "Object", "assign", "length", "error", "path", "_path", "join", "message", "type", "types", "messages", "appendErrors", "concat", "shift", "parseErrorSchema", "shouldUseNativeValidation", "criteriaMode", "toNestErrors", "validateFieldsNatively", "raw", "value", "validate", "_temp", "Promise", "resolve", "then", "_result", "e", "reject"], "mappings": "6FAmFM,SACJA,EACAC,EACAC,GAIA,YAJAA,IAAAA,IAAAA,EAEI,IAEUC,SAAAA,EAAQC,EAAGC,GAAO,QAAIC,EAAA,WAMlC,GAAIC,EAAOC,OAAQ,CACjB,IAAMC,EAvFa,SACvBC,EACAC,GAKA,IAHA,IAAMC,EAAeC,OAAOC,OAAO,GAAIJ,GACjCD,EAAqC,GAEpCG,EAAaG,QAAU,CAC5B,IAAMC,EAAQN,EAAiB,GAE/B,GAAKM,EAAMC,KAAX,CAGA,IAAMC,EAAQF,EAAMC,KAAKE,KAAK,KAM9B,GAJKV,EAAOS,KACVT,EAAOS,GAAS,CAAEE,QAASJ,EAAMI,QAASC,KAAM,KAG9CV,EAA0B,CAC5B,IAAMW,EAAQb,EAAOS,GAAOI,MACtBC,EAAWD,GAASA,EAAM,IAEhCb,EAAOS,GAASM,EAAYA,aAC1BN,EACAP,EACAF,EACA,GACAc,EACK,GAAgBE,OAAOF,EAAsBP,EAAMI,SACpDJ,EAAMI,QAEd,CAEAR,EAAac,OAtBb,CAuBF,CAEA,OAAOjB,CACT,CAiDqBkB,CACbpB,EAAOC,QACNH,EAAQuB,2BAAsD,QAAzBvB,EAAQwB,cAGhD,MAAO,CACL1B,OAAQ,CAAE,EACVM,OAAQqB,EAAAA,aAAarB,EAAQJ,GAEjC,CAIA,OAFAA,EAAQuB,2BAA6BG,yBAAuB,CAAA,EAAI1B,GAEzD,CACLF,OAAQD,EAAgB8B,IAAMnB,OAAOC,OAAO,CAAA,EAAIX,GAAUI,EAAO0B,MACjExB,OAAQ,CAAA,EACR,EAtBEF,EAASP,EAAO,aAAakC,SAAS/B,GAAQgC,EAC9C5B,WAAAA,GAAAA,aAAkB6B,QAAOA,OAAAA,QAAAC,QACZ9B,GAAM+B,cAAAC,GAArBhC,EAAMgC,CAAgB,GADpBhC,UACoB6B,QAAAC,QAAAF,GAAAA,EAAAG,KAAAH,EAAAG,KAAAhC,GAAAA,IAqB1B,CAAC,MAAAkC,GAAAJ,OAAAA,QAAAK,OAAAD,EACH,CAAA,CAAA"}