{"version": 3, "sources": [], "sections": [{"offset": {"line": 39, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Project%20By%20AI/Technoloway/Technoloway%20%28Processing%29/technoloway-simple/src/components/header.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport Link from 'next/link';\nimport { usePathname } from 'next/navigation';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport { Bars3Icon, XMarkIcon } from '@heroicons/react/24/outline';\n\nconst navigation = [\n  { name: 'Home', href: '/' },\n  { name: 'Services', href: '/#services' },\n  { name: 'Portfolio', href: '/portfolio' },\n  { name: 'Team', href: '/team' },\n  { name: 'Blog', href: '/blog' },\n  { name: 'Contact', href: '/#contact' },\n];\n\nexport function Header() {\n  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);\n  const pathname = usePathname();\n\n  const isActive = (href: string) => {\n    if (href === '/') {\n      return pathname === '/';\n    }\n    return pathname.startsWith(href);\n  };\n\n  return (\n    <header className=\"fixed inset-x-0 top-0 z-50 bg-white/80 backdrop-blur-md border-b border-gray-200\">\n      <nav className=\"container flex items-center justify-between py-4\" aria-label=\"Global\">\n        <div className=\"flex lg:flex-1\">\n          <Link href=\"/\" className=\"-m-1.5 p-1.5\">\n            <span className=\"sr-only\">Technoloway</span>\n            <div className=\"flex items-center space-x-2\">\n              <div className=\"h-8 w-8 bg-gradient-to-r from-blue-600 to-blue-400 rounded-lg flex items-center justify-center\">\n                <span className=\"text-white font-bold text-sm\">T</span>\n              </div>\n              <span className=\"text-xl font-bold text-gray-900\">Technoloway</span>\n            </div>\n          </Link>\n        </div>\n        \n        <div className=\"flex lg:hidden\">\n          <button\n            type=\"button\"\n            className=\"-m-2.5 inline-flex items-center justify-center rounded-md p-2.5 text-gray-700\"\n            onClick={() => setMobileMenuOpen(true)}\n          >\n            <span className=\"sr-only\">Open main menu</span>\n            <Bars3Icon className=\"h-6 w-6\" aria-hidden=\"true\" />\n          </button>\n        </div>\n        \n        <div className=\"hidden lg:flex lg:gap-x-8\">\n          {navigation.map((item) => (\n            <Link\n              key={item.name}\n              href={item.href}\n              className={`text-sm font-semibold leading-6 transition-colors hover:text-blue-600 ${\n                isActive(item.href) ? 'text-blue-600' : 'text-gray-900'\n              }`}\n            >\n              {item.name}\n            </Link>\n          ))}\n        </div>\n        \n        <div className=\"hidden lg:flex lg:flex-1 lg:justify-end\">\n          <Link href=\"/#contact\" className=\"btn-primary\">\n            Get Started\n          </Link>\n        </div>\n      </nav>\n      \n      {/* Mobile menu */}\n      <AnimatePresence>\n        {mobileMenuOpen && (\n          <motion.div\n            initial={{ opacity: 0 }}\n            animate={{ opacity: 1 }}\n            exit={{ opacity: 0 }}\n            className=\"lg:hidden\"\n          >\n            <div className=\"fixed inset-0 z-50\" />\n            <motion.div\n              initial={{ x: '100%' }}\n              animate={{ x: 0 }}\n              exit={{ x: '100%' }}\n              transition={{ type: 'spring', damping: 25, stiffness: 200 }}\n              className=\"fixed inset-y-0 right-0 z-50 w-full overflow-y-auto bg-white px-6 py-6 sm:max-w-sm sm:ring-1 sm:ring-gray-900/10\"\n            >\n              <div className=\"flex items-center justify-between\">\n                <Link href=\"/\" className=\"-m-1.5 p-1.5\">\n                  <span className=\"sr-only\">Technoloway</span>\n                  <div className=\"flex items-center space-x-2\">\n                    <div className=\"h-8 w-8 bg-gradient-to-r from-blue-600 to-blue-400 rounded-lg flex items-center justify-center\">\n                      <span className=\"text-white font-bold text-sm\">T</span>\n                    </div>\n                    <span className=\"text-xl font-bold text-gray-900\">Technoloway</span>\n                  </div>\n                </Link>\n                <button\n                  type=\"button\"\n                  className=\"-m-2.5 rounded-md p-2.5 text-gray-700\"\n                  onClick={() => setMobileMenuOpen(false)}\n                >\n                  <span className=\"sr-only\">Close menu</span>\n                  <XMarkIcon className=\"h-6 w-6\" aria-hidden=\"true\" />\n                </button>\n              </div>\n              <div className=\"mt-6 flow-root\">\n                <div className=\"-my-6 divide-y divide-gray-500/10\">\n                  <div className=\"space-y-2 py-6\">\n                    {navigation.map((item) => (\n                      <Link\n                        key={item.name}\n                        href={item.href}\n                        className={`-mx-3 block rounded-lg px-3 py-2 text-base font-semibold leading-7 hover:bg-gray-50 ${\n                          isActive(item.href) ? 'text-blue-600' : 'text-gray-900'\n                        }`}\n                        onClick={() => setMobileMenuOpen(false)}\n                      >\n                        {item.name}\n                      </Link>\n                    ))}\n                  </div>\n                  <div className=\"py-6\">\n                    <Link\n                      href=\"/#contact\"\n                      className=\"-mx-3 block rounded-lg px-3 py-2.5 text-base font-semibold leading-7 text-blue-600 hover:bg-gray-50\"\n                      onClick={() => setMobileMenuOpen(false)}\n                    >\n                      Get Started\n                    </Link>\n                  </div>\n                </div>\n              </div>\n            </motion.div>\n          </motion.div>\n        )}\n      </AnimatePresence>\n    </header>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AACA;AAAA;AANA;;;;;;;AAQA,MAAM,aAAa;IACjB;QAAE,MAAM;QAAQ,MAAM;IAAI;IAC1B;QAAE,MAAM;QAAY,MAAM;IAAa;IACvC;QAAE,MAAM;QAAa,MAAM;IAAa;IACxC;QAAE,MAAM;QAAQ,MAAM;IAAQ;IAC9B;QAAE,MAAM;QAAQ,MAAM;IAAQ;IAC9B;QAAE,MAAM;QAAW,MAAM;IAAY;CACtC;AAEM,SAAS;IACd,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,WAAW,CAAA,GAAA,kIAAA,CAAA,cAAW,AAAD;IAE3B,MAAM,WAAW,CAAC;QAChB,IAAI,SAAS,KAAK;YAChB,OAAO,aAAa;QACtB;QACA,OAAO,SAAS,UAAU,CAAC;IAC7B;IAEA,qBACE,8OAAC;QAAO,WAAU;;0BAChB,8OAAC;gBAAI,WAAU;gBAAmD,cAAW;;kCAC3E,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC,4JAAA,CAAA,UAAI;4BAAC,MAAK;4BAAI,WAAU;;8CACvB,8OAAC;oCAAK,WAAU;8CAAU;;;;;;8CAC1B,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAK,WAAU;0DAA+B;;;;;;;;;;;sDAEjD,8OAAC;4CAAK,WAAU;sDAAkC;;;;;;;;;;;;;;;;;;;;;;;kCAKxD,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BACC,MAAK;4BACL,WAAU;4BACV,SAAS,IAAM,kBAAkB;;8CAEjC,8OAAC;oCAAK,WAAU;8CAAU;;;;;;8CAC1B,8OAAC,iNAAA,CAAA,YAAS;oCAAC,WAAU;oCAAU,eAAY;;;;;;;;;;;;;;;;;kCAI/C,8OAAC;wBAAI,WAAU;kCACZ,WAAW,GAAG,CAAC,CAAC,qBACf,8OAAC,4JAAA,CAAA,UAAI;gCAEH,MAAM,KAAK,IAAI;gCACf,WAAW,CAAC,sEAAsE,EAChF,SAAS,KAAK,IAAI,IAAI,kBAAkB,iBACxC;0CAED,KAAK,IAAI;+BANL,KAAK,IAAI;;;;;;;;;;kCAWpB,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC,4JAAA,CAAA,UAAI;4BAAC,MAAK;4BAAY,WAAU;sCAAc;;;;;;;;;;;;;;;;;0BAOnD,8OAAC,yLAAA,CAAA,kBAAe;0BACb,gCACC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;oBAAE;oBACtB,SAAS;wBAAE,SAAS;oBAAE;oBACtB,MAAM;wBAAE,SAAS;oBAAE;oBACnB,WAAU;;sCAEV,8OAAC;4BAAI,WAAU;;;;;;sCACf,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,SAAS;gCAAE,GAAG;4BAAO;4BACrB,SAAS;gCAAE,GAAG;4BAAE;4BAChB,MAAM;gCAAE,GAAG;4BAAO;4BAClB,YAAY;gCAAE,MAAM;gCAAU,SAAS;gCAAI,WAAW;4BAAI;4BAC1D,WAAU;;8CAEV,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,4JAAA,CAAA,UAAI;4CAAC,MAAK;4CAAI,WAAU;;8DACvB,8OAAC;oDAAK,WAAU;8DAAU;;;;;;8DAC1B,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;sEACb,cAAA,8OAAC;gEAAK,WAAU;0EAA+B;;;;;;;;;;;sEAEjD,8OAAC;4DAAK,WAAU;sEAAkC;;;;;;;;;;;;;;;;;;sDAGtD,8OAAC;4CACC,MAAK;4CACL,WAAU;4CACV,SAAS,IAAM,kBAAkB;;8DAEjC,8OAAC;oDAAK,WAAU;8DAAU;;;;;;8DAC1B,8OAAC,iNAAA,CAAA,YAAS;oDAAC,WAAU;oDAAU,eAAY;;;;;;;;;;;;;;;;;;8CAG/C,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;0DACZ,WAAW,GAAG,CAAC,CAAC,qBACf,8OAAC,4JAAA,CAAA,UAAI;wDAEH,MAAM,KAAK,IAAI;wDACf,WAAW,CAAC,oFAAoF,EAC9F,SAAS,KAAK,IAAI,IAAI,kBAAkB,iBACxC;wDACF,SAAS,IAAM,kBAAkB;kEAEhC,KAAK,IAAI;uDAPL,KAAK,IAAI;;;;;;;;;;0DAWpB,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC,4JAAA,CAAA,UAAI;oDACH,MAAK;oDACL,WAAU;oDACV,SAAS,IAAM,kBAAkB;8DAClC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAYrB", "debugId": null}}, {"offset": {"line": 431, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Project%20By%20AI/Technoloway/Technoloway%20%28Processing%29/technoloway-simple/src/components/footer.tsx"], "sourcesContent": ["import Link from 'next/link';\n\nexport function Footer() {\n  return (\n    <footer className=\"bg-gray-900 text-white\">\n      <div className=\"container py-16\">\n        <div className=\"grid grid-cols-1 md:grid-cols-4 gap-8\">\n          <div className=\"col-span-1 md:col-span-2\">\n            <div className=\"flex items-center space-x-2 mb-6\">\n              <div className=\"h-8 w-8 bg-gradient-to-r from-blue-600 to-blue-400 rounded-lg flex items-center justify-center\">\n                <span className=\"text-white font-bold text-sm\">T</span>\n              </div>\n              <span className=\"text-xl font-bold\">Technoloway</span>\n            </div>\n            <p className=\"text-gray-300 mb-6 max-w-md\">\n              Transforming ideas into digital reality. We build exceptional software \n              solutions that drive business growth and innovation.\n            </p>\n            <div className=\"space-y-2\">\n              <p className=\"text-gray-300\"><EMAIL></p>\n              <p className=\"text-gray-300\">+****************</p>\n              <p className=\"text-gray-300\">San Francisco, CA</p>\n            </div>\n          </div>\n          \n          <div>\n            <h3 className=\"text-lg font-semibold mb-4\">Services</h3>\n            <ul className=\"space-y-2 text-gray-300\">\n              <li><Link href=\"/#services\" className=\"hover:text-white transition-colors\">Web Development</Link></li>\n              <li><Link href=\"/#services\" className=\"hover:text-white transition-colors\">Mobile Apps</Link></li>\n              <li><Link href=\"/#services\" className=\"hover:text-white transition-colors\">Cloud Solutions</Link></li>\n              <li><Link href=\"/#services\" className=\"hover:text-white transition-colors\">API Development</Link></li>\n            </ul>\n          </div>\n          \n          <div>\n            <h3 className=\"text-lg font-semibold mb-4\">Company</h3>\n            <ul className=\"space-y-2 text-gray-300\">\n              <li><Link href=\"/team\" className=\"hover:text-white transition-colors\">About</Link></li>\n              <li><Link href=\"/team\" className=\"hover:text-white transition-colors\">Team</Link></li>\n              <li><Link href=\"/portfolio\" className=\"hover:text-white transition-colors\">Portfolio</Link></li>\n              <li><Link href=\"/blog\" className=\"hover:text-white transition-colors\">Blog</Link></li>\n              <li><Link href=\"/#contact\" className=\"hover:text-white transition-colors\">Contact</Link></li>\n            </ul>\n          </div>\n        </div>\n        \n        <div className=\"border-t border-gray-700 mt-12 pt-8 text-center\">\n          <p className=\"text-gray-400\">\n            &copy; {new Date().getFullYear()} Technoloway. All rights reserved.\n          </p>\n        </div>\n      </div>\n    </footer>\n  );\n}\n"], "names": [], "mappings": ";;;;AAAA;;;AAEO,SAAS;IACd,qBACE,8OAAC;QAAO,WAAU;kBAChB,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAK,WAAU;0DAA+B;;;;;;;;;;;sDAEjD,8OAAC;4CAAK,WAAU;sDAAoB;;;;;;;;;;;;8CAEtC,8OAAC;oCAAE,WAAU;8CAA8B;;;;;;8CAI3C,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAE,WAAU;sDAAgB;;;;;;sDAC7B,8OAAC;4CAAE,WAAU;sDAAgB;;;;;;sDAC7B,8OAAC;4CAAE,WAAU;sDAAgB;;;;;;;;;;;;;;;;;;sCAIjC,8OAAC;;8CACC,8OAAC;oCAAG,WAAU;8CAA6B;;;;;;8CAC3C,8OAAC;oCAAG,WAAU;;sDACZ,8OAAC;sDAAG,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAa,WAAU;0DAAqC;;;;;;;;;;;sDAC3E,8OAAC;sDAAG,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAa,WAAU;0DAAqC;;;;;;;;;;;sDAC3E,8OAAC;sDAAG,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAa,WAAU;0DAAqC;;;;;;;;;;;sDAC3E,8OAAC;sDAAG,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAa,WAAU;0DAAqC;;;;;;;;;;;;;;;;;;;;;;;sCAI/E,8OAAC;;8CACC,8OAAC;oCAAG,WAAU;8CAA6B;;;;;;8CAC3C,8OAAC;oCAAG,WAAU;;sDACZ,8OAAC;sDAAG,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAQ,WAAU;0DAAqC;;;;;;;;;;;sDACtE,8OAAC;sDAAG,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAQ,WAAU;0DAAqC;;;;;;;;;;;sDACtE,8OAAC;sDAAG,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAa,WAAU;0DAAqC;;;;;;;;;;;sDAC3E,8OAAC;sDAAG,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAQ,WAAU;0DAAqC;;;;;;;;;;;sDACtE,8OAAC;sDAAG,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAY,WAAU;0DAAqC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAKhF,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAE,WAAU;;4BAAgB;4BACnB,IAAI,OAAO,WAAW;4BAAG;;;;;;;;;;;;;;;;;;;;;;;AAM7C", "debugId": null}}, {"offset": {"line": 757, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Project%20By%20AI/Technoloway/Technoloway%20%28Processing%29/technoloway-simple/src/app/page.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport Link from 'next/link';\r\nimport { motion } from 'framer-motion';\r\nimport {\r\n  ArrowRightIcon,\r\n  PlayIcon,\r\n  CodeBracketIcon,\r\n  DevicePhoneMobileIcon,\r\n  CloudIcon,\r\n  CogIcon,\r\n  ShieldCheckIcon,\r\n  ChartBarIcon,\r\n  EnvelopeIcon,\r\n  PhoneIcon,\r\n  MapPinIcon\r\n} from '@heroicons/react/24/outline';\r\nimport { Header } from '@/components/header';\r\nimport { Footer } from '@/components/footer';\r\n\r\nconst stats = [\r\n  { name: 'Projects Delivered', value: '500+' },\r\n  { name: 'Happy Clients', value: '200+' },\r\n  { name: 'Years Experience', value: '10+' },\r\n  { name: 'Team Members', value: '50+' },\r\n];\r\n\r\nconst services = [\r\n  {\r\n    name: 'Web Development',\r\n    description: 'Modern, responsive web applications built with the latest technologies and best practices.',\r\n    icon: CodeBracketIcon,\r\n    features: ['React/Next.js', 'TypeScript', 'Tailwind CSS', 'API Integration'],\r\n  },\r\n  {\r\n    name: 'Mobile Development',\r\n    description: 'Native and cross-platform mobile applications for iOS and Android.',\r\n    icon: DevicePhoneMobileIcon,\r\n    features: ['React Native', 'Flutter', 'iOS/Android', 'App Store Deployment'],\r\n  },\r\n  {\r\n    name: 'Cloud Solutions',\r\n    description: 'Scalable cloud infrastructure and deployment solutions for modern applications.',\r\n    icon: CloudIcon,\r\n    features: ['AWS/Azure/GCP', 'Docker/Kubernetes', 'CI/CD Pipelines', 'Monitoring'],\r\n  },\r\n  {\r\n    name: 'API Development',\r\n    description: 'Robust and scalable APIs and microservices architecture.',\r\n    icon: CogIcon,\r\n    features: ['REST/GraphQL', 'Microservices', 'Database Design', 'Documentation'],\r\n  },\r\n  {\r\n    name: 'Security & Testing',\r\n    description: 'Comprehensive security audits and automated testing solutions.',\r\n    icon: ShieldCheckIcon,\r\n    features: ['Security Audits', 'Automated Testing', 'Code Review', 'Performance'],\r\n  },\r\n  {\r\n    name: 'Analytics & Insights',\r\n    description: 'Data-driven insights and analytics solutions for business growth.',\r\n    icon: ChartBarIcon,\r\n    features: ['Data Analytics', 'Business Intelligence', 'Reporting', 'Dashboards'],\r\n  },\r\n];\r\n\r\nconst testimonials = [\r\n  {\r\n    id: 1,\r\n    content: \"Technoloway transformed our outdated system into a modern, scalable platform. Their expertise in cloud architecture and attention to detail exceeded our expectations.\",\r\n    author: \"Sarah Mitchell\",\r\n    role: \"CTO\",\r\n    company: \"GreenTech Solutions\",\r\n    image: \"https://images.unsplash.com/photo-1494790108755-2616b612b786?w=100&h=100&fit=crop&crop=face\",\r\n    rating: 5,\r\n  },\r\n  {\r\n    id: 2,\r\n    content: \"The mobile app they developed for us has been a game-changer. User engagement increased by 85% and our revenue grew by 150% in just 6 months.\",\r\n    author: \"Michael Chen\",\r\n    role: \"CEO\",\r\n    company: \"MedTech Innovations\",\r\n    image: \"https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=100&h=100&fit=crop&crop=face\",\r\n    rating: 5,\r\n  },\r\n  {\r\n    id: 3,\r\n    content: \"Working with Technoloway was seamless. They delivered our e-commerce platform on time and within budget. The results speak for themselves - 200% increase in online sales.\",\r\n    author: \"Emily Rodriguez\",\r\n    role: \"Founder\",\r\n    company: \"EcoCommerce\",\r\n    image: \"https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=100&h=100&fit=crop&crop=face\",\r\n    rating: 5,\r\n  },\r\n  {\r\n    id: 4,\r\n    content: \"Their security audit identified critical vulnerabilities we didn't know existed. The team's expertise in cybersecurity gave us peace of mind and protected our business.\",\r\n    author: \"David Park\",\r\n    role: \"CISO\",\r\n    company: \"FinanceFlow Corp\",\r\n    image: \"https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=100&h=100&fit=crop&crop=face\",\r\n    rating: 5,\r\n  },\r\n];\r\n\r\nexport default function Home() {\r\n  return (\r\n    <div className=\"min-h-screen bg-white\">\r\n      <Header />\r\n\r\n      <main>\r\n        {/* Hero Section */}\r\n        <section className=\"relative min-h-screen flex items-center justify-center overflow-hidden bg-gradient-to-br from-gray-50 via-white to-gray-100\">\r\n          {/* Background decoration */}\r\n          <div className=\"absolute inset-0 overflow-hidden\">\r\n            <div className=\"absolute -top-40 -right-32 w-80 h-80 bg-blue-100 rounded-full mix-blend-multiply filter blur-xl opacity-70 animate-blob\" />\r\n            <div className=\"absolute -bottom-40 -left-32 w-80 h-80 bg-purple-100 rounded-full mix-blend-multiply filter blur-xl opacity-70 animate-blob animation-delay-2000\" />\r\n            <div className=\"absolute top-40 left-40 w-80 h-80 bg-pink-100 rounded-full mix-blend-multiply filter blur-xl opacity-70 animate-blob animation-delay-4000\" />\r\n          </div>\r\n\r\n          <div className=\"container relative z-10 pt-20\">\r\n            <div className=\"text-center\">\r\n              <motion.div\r\n                initial={{ opacity: 0, y: 20 }}\r\n                animate={{ opacity: 1, y: 0 }}\r\n                transition={{ duration: 0.6 }}\r\n                className=\"mx-auto max-w-4xl\"\r\n              >\r\n                <motion.div\r\n                  initial={{ opacity: 0, scale: 0.95 }}\r\n                  animate={{ opacity: 1, scale: 1 }}\r\n                  transition={{ duration: 0.5, delay: 0.1 }}\r\n                  className=\"mb-8\"\r\n                >\r\n                  <span className=\"inline-flex items-center rounded-full bg-blue-50 px-6 py-2 text-sm font-medium text-blue-700 ring-1 ring-inset ring-blue-700/10\">\r\n                    🚀 Building the Future of Software\r\n                  </span>\r\n                </motion.div>\r\n\r\n                <motion.h1\r\n                  initial={{ opacity: 0, y: 20 }}\r\n                  animate={{ opacity: 1, y: 0 }}\r\n                  transition={{ duration: 0.6, delay: 0.2 }}\r\n                  className=\"text-4xl font-bold tracking-tight text-gray-900 sm:text-6xl lg:text-7xl\"\r\n                >\r\n                  Transform Your Ideas Into{' '}\r\n                  <span className=\"gradient-text\">Digital Reality</span>\r\n                </motion.h1>\r\n\r\n                <motion.p\r\n                  initial={{ opacity: 0, y: 20 }}\r\n                  animate={{ opacity: 1, y: 0 }}\r\n                  transition={{ duration: 0.6, delay: 0.3 }}\r\n                  className=\"mt-6 text-lg leading-8 text-gray-600 sm:text-xl lg:text-2xl max-w-3xl mx-auto\"\r\n                >\r\n                  We craft exceptional software solutions that drive business growth.\r\n                  From web applications to mobile apps and enterprise systems,\r\n                  we turn your vision into powerful digital experiences.\r\n                </motion.p>\r\n\r\n                <motion.div\r\n                  initial={{ opacity: 0, y: 20 }}\r\n                  animate={{ opacity: 1, y: 0 }}\r\n                  transition={{ duration: 0.6, delay: 0.4 }}\r\n                  className=\"mt-10 flex flex-col sm:flex-row items-center justify-center gap-4\"\r\n                >\r\n                  <Link href=\"#contact\" className=\"btn-primary group\">\r\n                    Start Your Project\r\n                    <ArrowRightIcon className=\"ml-2 h-4 w-4 transition-transform group-hover:translate-x-1\" />\r\n                  </Link>\r\n\r\n                  <button className=\"btn-secondary group\">\r\n                    <PlayIcon className=\"mr-2 h-4 w-4\" />\r\n                    Watch Our Story\r\n                  </button>\r\n                </motion.div>\r\n\r\n                <motion.div\r\n                  initial={{ opacity: 0, y: 20 }}\r\n                  animate={{ opacity: 1, y: 0 }}\r\n                  transition={{ duration: 0.6, delay: 0.5 }}\r\n                  className=\"mt-16\"\r\n                >\r\n                  <p className=\"text-sm font-semibold text-gray-900 mb-8\">\r\n                    Trusted by industry leaders\r\n                  </p>\r\n                  <div className=\"grid grid-cols-2 gap-8 md:grid-cols-4\">\r\n                    {stats.map((stat, index) => (\r\n                      <motion.div\r\n                        key={stat.name}\r\n                        initial={{ opacity: 0, scale: 0.8 }}\r\n                        animate={{ opacity: 1, scale: 1 }}\r\n                        transition={{ duration: 0.5, delay: 0.6 + index * 0.1 }}\r\n                        className=\"text-center\"\r\n                      >\r\n                        <div className=\"text-3xl font-bold text-blue-600 sm:text-4xl\">\r\n                          {stat.value}\r\n                        </div>\r\n                        <div className=\"mt-2 text-sm text-gray-600\">\r\n                          {stat.name}\r\n                        </div>\r\n                      </motion.div>\r\n                    ))}\r\n                  </div>\r\n                </motion.div>\r\n              </motion.div>\r\n            </div>\r\n          </div>\r\n        </section>\r\n\r\n        {/* Services Section */}\r\n        <section id=\"services\" className=\"section-padding bg-white\">\r\n          <div className=\"container\">\r\n            <motion.div\r\n              initial={{ opacity: 0, y: 20 }}\r\n              whileInView={{ opacity: 1, y: 0 }}\r\n              transition={{ duration: 0.6 }}\r\n              viewport={{ once: true }}\r\n              className=\"text-center mb-16\"\r\n            >\r\n              <h2 className=\"text-3xl font-bold tracking-tight text-gray-900 sm:text-4xl lg:text-5xl\">\r\n                Our <span className=\"gradient-text\">Services</span>\r\n              </h2>\r\n              <p className=\"mt-4 text-lg text-gray-600 max-w-3xl mx-auto\">\r\n                We offer comprehensive software development services to help your business\r\n                thrive in the digital world. From concept to deployment, we've got you covered.\r\n              </p>\r\n            </motion.div>\r\n\r\n            <div className=\"grid grid-cols-1 gap-8 md:grid-cols-2 lg:grid-cols-3\">\r\n              {services.map((service, index) => (\r\n                <motion.div\r\n                  key={service.name}\r\n                  initial={{ opacity: 0, y: 20 }}\r\n                  whileInView={{ opacity: 1, y: 0 }}\r\n                  transition={{ duration: 0.6, delay: index * 0.1 }}\r\n                  viewport={{ once: true }}\r\n                  className=\"group relative bg-white p-8 rounded-2xl border border-gray-200 hover:border-blue-300 hover:shadow-lg transition-all duration-300\"\r\n                >\r\n                  <div className=\"flex items-center justify-center w-12 h-12 bg-blue-100 rounded-lg mb-6 group-hover:bg-blue-200 transition-colors\">\r\n                    <service.icon className=\"w-6 h-6 text-blue-600\" />\r\n                  </div>\r\n\r\n                  <h3 className=\"text-xl font-semibold text-gray-900 mb-3\">\r\n                    {service.name}\r\n                  </h3>\r\n\r\n                  <p className=\"text-gray-600 mb-6\">\r\n                    {service.description}\r\n                  </p>\r\n\r\n                  <ul className=\"space-y-2\">\r\n                    {service.features.map((feature) => (\r\n                      <li key={feature} className=\"flex items-center text-sm text-gray-500\">\r\n                        <div className=\"w-1.5 h-1.5 bg-blue-500 rounded-full mr-3\" />\r\n                        {feature}\r\n                      </li>\r\n                    ))}\r\n                  </ul>\r\n\r\n                  {/* Hover effect */}\r\n                  <div className=\"absolute inset-0 bg-gradient-to-r from-blue-50 to-purple-50 rounded-2xl opacity-0 group-hover:opacity-100 transition-opacity duration-300 -z-10\" />\r\n                </motion.div>\r\n              ))}\r\n            </div>\r\n\r\n            <motion.div\r\n              initial={{ opacity: 0, y: 20 }}\r\n              whileInView={{ opacity: 1, y: 0 }}\r\n              transition={{ duration: 0.6, delay: 0.8 }}\r\n              viewport={{ once: true }}\r\n              className=\"text-center mt-16\"\r\n            >\r\n              <p className=\"text-lg text-gray-600 mb-8\">\r\n                Need something custom? We'd love to discuss your unique requirements.\r\n              </p>\r\n              <Link href=\"#contact\" className=\"btn-primary\">\r\n                Get Custom Quote\r\n              </Link>\r\n            </motion.div>\r\n          </div>\r\n        </section>\r\n\r\n        {/* Testimonials Section */}\r\n        <section className=\"section-padding bg-gray-50\">\r\n          <div className=\"container\">\r\n            <motion.div\r\n              initial={{ opacity: 0, y: 20 }}\r\n              whileInView={{ opacity: 1, y: 0 }}\r\n              transition={{ duration: 0.6 }}\r\n              viewport={{ once: true }}\r\n              className=\"text-center mb-16\"\r\n            >\r\n              <h2 className=\"text-3xl font-bold tracking-tight text-gray-900 sm:text-4xl lg:text-5xl\">\r\n                What Our <span className=\"gradient-text\">Clients Say</span>\r\n              </h2>\r\n              <p className=\"mt-4 text-lg text-gray-600 max-w-3xl mx-auto\">\r\n                Don't just take our word for it. Here's what our clients have to say about\r\n                working with Technoloway and the results we've achieved together.\r\n              </p>\r\n            </motion.div>\r\n\r\n            <div className=\"grid grid-cols-1 md:grid-cols-2 gap-8\">\r\n              {testimonials.map((testimonial, index) => (\r\n                <motion.div\r\n                  key={testimonial.id}\r\n                  initial={{ opacity: 0, y: 20 }}\r\n                  whileInView={{ opacity: 1, y: 0 }}\r\n                  transition={{ duration: 0.6, delay: index * 0.1 }}\r\n                  viewport={{ once: true }}\r\n                  className=\"bg-white p-8 rounded-2xl shadow-lg border border-gray-200\"\r\n                >\r\n                  <div className=\"flex items-center mb-4\">\r\n                    {[...Array(testimonial.rating)].map((_, i) => (\r\n                      <svg\r\n                        key={i}\r\n                        className=\"w-5 h-5 text-yellow-400 fill-current\"\r\n                        viewBox=\"0 0 20 20\"\r\n                      >\r\n                        <path d=\"M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z\" />\r\n                      </svg>\r\n                    ))}\r\n                  </div>\r\n\r\n                  <blockquote className=\"text-gray-700 mb-6 text-lg leading-relaxed\">\r\n                    \"{testimonial.content}\"\r\n                  </blockquote>\r\n\r\n                  <div className=\"flex items-center\">\r\n                    <div className=\"relative w-12 h-12 mr-4\">\r\n                      <img\r\n                        src={testimonial.image}\r\n                        alt={testimonial.author}\r\n                        className=\"w-full h-full object-cover rounded-full\"\r\n                      />\r\n                    </div>\r\n                    <div>\r\n                      <div className=\"font-semibold text-gray-900\">\r\n                        {testimonial.author}\r\n                      </div>\r\n                      <div className=\"text-sm text-gray-600\">\r\n                        {testimonial.role} at {testimonial.company}\r\n                      </div>\r\n                    </div>\r\n                  </div>\r\n                </motion.div>\r\n              ))}\r\n            </div>\r\n\r\n            <motion.div\r\n              initial={{ opacity: 0, y: 20 }}\r\n              whileInView={{ opacity: 1, y: 0 }}\r\n              transition={{ duration: 0.6, delay: 0.6 }}\r\n              viewport={{ once: true }}\r\n              className=\"text-center mt-12\"\r\n            >\r\n              <p className=\"text-gray-600 mb-6\">\r\n                Join our growing list of satisfied clients\r\n              </p>\r\n              <Link href=\"/portfolio\" className=\"btn-secondary\">\r\n                View Case Studies\r\n              </Link>\r\n            </motion.div>\r\n          </div>\r\n        </section>\r\n\r\n        {/* Contact Section */}\r\n        <section id=\"contact\" className=\"section-padding bg-gray-50\">\r\n          <div className=\"container\">\r\n            <motion.div\r\n              initial={{ opacity: 0, y: 20 }}\r\n              whileInView={{ opacity: 1, y: 0 }}\r\n              transition={{ duration: 0.6 }}\r\n              viewport={{ once: true }}\r\n              className=\"text-center mb-16\"\r\n            >\r\n              <h2 className=\"text-3xl font-bold tracking-tight text-gray-900 sm:text-4xl lg:text-5xl\">\r\n                Let's Build Something <span className=\"gradient-text\">Amazing</span>\r\n              </h2>\r\n              <p className=\"mt-4 text-lg text-gray-600 max-w-3xl mx-auto\">\r\n                Ready to transform your ideas into reality? Get in touch with our team\r\n                and let's discuss how we can help your business grow.\r\n              </p>\r\n            </motion.div>\r\n\r\n            <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-12\">\r\n              {/* Contact Info */}\r\n              <motion.div\r\n                initial={{ opacity: 0, x: -20 }}\r\n                whileInView={{ opacity: 1, x: 0 }}\r\n                transition={{ duration: 0.6 }}\r\n                viewport={{ once: true }}\r\n                className=\"space-y-8\"\r\n              >\r\n                <div>\r\n                  <h3 className=\"text-2xl font-bold text-gray-900 mb-6\">Get in Touch</h3>\r\n                  <div className=\"space-y-6\">\r\n                    <div className=\"flex items-center space-x-4\">\r\n                      <div className=\"flex items-center justify-center w-12 h-12 bg-blue-100 rounded-lg\">\r\n                        <EnvelopeIcon className=\"w-6 h-6 text-blue-600\" />\r\n                      </div>\r\n                      <div>\r\n                        <p className=\"text-sm font-medium text-gray-500\">Email</p>\r\n                        <p className=\"text-lg text-gray-900\"><EMAIL></p>\r\n                      </div>\r\n                    </div>\r\n\r\n                    <div className=\"flex items-center space-x-4\">\r\n                      <div className=\"flex items-center justify-center w-12 h-12 bg-blue-100 rounded-lg\">\r\n                        <PhoneIcon className=\"w-6 h-6 text-blue-600\" />\r\n                      </div>\r\n                      <div>\r\n                        <p className=\"text-sm font-medium text-gray-500\">Phone</p>\r\n                        <p className=\"text-lg text-gray-900\">+****************</p>\r\n                      </div>\r\n                    </div>\r\n\r\n                    <div className=\"flex items-center space-x-4\">\r\n                      <div className=\"flex items-center justify-center w-12 h-12 bg-blue-100 rounded-lg\">\r\n                        <MapPinIcon className=\"w-6 h-6 text-blue-600\" />\r\n                      </div>\r\n                      <div>\r\n                        <p className=\"text-sm font-medium text-gray-500\">Location</p>\r\n                        <p className=\"text-lg text-gray-900\">San Francisco, CA</p>\r\n                      </div>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n              </motion.div>\r\n\r\n              {/* Contact Form */}\r\n              <motion.div\r\n                initial={{ opacity: 0, x: 20 }}\r\n                whileInView={{ opacity: 1, x: 0 }}\r\n                transition={{ duration: 0.6 }}\r\n                viewport={{ once: true }}\r\n                className=\"bg-white p-8 rounded-2xl shadow-lg\"\r\n              >\r\n                <form className=\"space-y-6\">\r\n                  <div className=\"grid grid-cols-1 sm:grid-cols-2 gap-6\">\r\n                    <div>\r\n                      <label htmlFor=\"firstName\" className=\"block text-sm font-medium text-gray-700 mb-2\">\r\n                        First Name\r\n                      </label>\r\n                      <input\r\n                        type=\"text\"\r\n                        id=\"firstName\"\r\n                        className=\"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-colors\"\r\n                        placeholder=\"John\"\r\n                      />\r\n                    </div>\r\n                    <div>\r\n                      <label htmlFor=\"lastName\" className=\"block text-sm font-medium text-gray-700 mb-2\">\r\n                        Last Name\r\n                      </label>\r\n                      <input\r\n                        type=\"text\"\r\n                        id=\"lastName\"\r\n                        className=\"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-colors\"\r\n                        placeholder=\"Doe\"\r\n                      />\r\n                    </div>\r\n                  </div>\r\n\r\n                  <div>\r\n                    <label htmlFor=\"email\" className=\"block text-sm font-medium text-gray-700 mb-2\">\r\n                      Email\r\n                    </label>\r\n                    <input\r\n                      type=\"email\"\r\n                      id=\"email\"\r\n                      className=\"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-colors\"\r\n                      placeholder=\"<EMAIL>\"\r\n                    />\r\n                  </div>\r\n\r\n                  <div>\r\n                    <label htmlFor=\"company\" className=\"block text-sm font-medium text-gray-700 mb-2\">\r\n                      Company\r\n                    </label>\r\n                    <input\r\n                      type=\"text\"\r\n                      id=\"company\"\r\n                      className=\"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-colors\"\r\n                      placeholder=\"Your Company\"\r\n                    />\r\n                  </div>\r\n\r\n                  <div>\r\n                    <label htmlFor=\"message\" className=\"block text-sm font-medium text-gray-700 mb-2\">\r\n                      Message\r\n                    </label>\r\n                    <textarea\r\n                      id=\"message\"\r\n                      rows={4}\r\n                      className=\"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-colors\"\r\n                      placeholder=\"Tell us about your project...\"\r\n                    />\r\n                  </div>\r\n\r\n                  <button type=\"submit\" className=\"w-full btn-primary\">\r\n                    Send Message\r\n                  </button>\r\n                </form>\r\n              </motion.div>\r\n            </div>\r\n          </div>\r\n        </section>\r\n      </main>\r\n      <Footer />\r\n    </div>\r\n  );\r\n}"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAaA;AACA;AAlBA;;;;;;;AAoBA,MAAM,QAAQ;IACZ;QAAE,MAAM;QAAsB,OAAO;IAAO;IAC5C;QAAE,MAAM;QAAiB,OAAO;IAAO;IACvC;QAAE,MAAM;QAAoB,OAAO;IAAM;IACzC;QAAE,MAAM;QAAgB,OAAO;IAAM;CACtC;AAED,MAAM,WAAW;IACf;QACE,MAAM;QACN,aAAa;QACb,MAAM,6NAAA,CAAA,kBAAe;QACrB,UAAU;YAAC;YAAiB;YAAc;YAAgB;SAAkB;IAC9E;IACA;QACE,MAAM;QACN,aAAa;QACb,MAAM,yOAAA,CAAA,wBAAqB;QAC3B,UAAU;YAAC;YAAgB;YAAW;YAAe;SAAuB;IAC9E;IACA;QACE,MAAM;QACN,aAAa;QACb,MAAM,iNAAA,CAAA,YAAS;QACf,UAAU;YAAC;YAAiB;YAAqB;YAAmB;SAAa;IACnF;IACA;QACE,MAAM;QACN,aAAa;QACb,MAAM,6MAAA,CAAA,UAAO;QACb,UAAU;YAAC;YAAgB;YAAiB;YAAmB;SAAgB;IACjF;IACA;QACE,MAAM;QACN,aAAa;QACb,MAAM,6NAAA,CAAA,kBAAe;QACrB,UAAU;YAAC;YAAmB;YAAqB;YAAe;SAAc;IAClF;IACA;QACE,MAAM;QACN,aAAa;QACb,MAAM,uNAAA,CAAA,eAAY;QAClB,UAAU;YAAC;YAAkB;YAAyB;YAAa;SAAa;IAClF;CACD;AAED,MAAM,eAAe;IACnB;QACE,IAAI;QACJ,SAAS;QACT,QAAQ;QACR,MAAM;QACN,SAAS;QACT,OAAO;QACP,QAAQ;IACV;IACA;QACE,IAAI;QACJ,SAAS;QACT,QAAQ;QACR,MAAM;QACN,SAAS;QACT,OAAO;QACP,QAAQ;IACV;IACA;QACE,IAAI;QACJ,SAAS;QACT,QAAQ;QACR,MAAM;QACN,SAAS;QACT,OAAO;QACP,QAAQ;IACV;IACA;QACE,IAAI;QACJ,SAAS;QACT,QAAQ;QACR,MAAM;QACN,SAAS;QACT,OAAO;QACP,QAAQ;IACV;CACD;AAEc,SAAS;IACtB,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC,4HAAA,CAAA,SAAM;;;;;0BAEP,8OAAC;;kCAEC,8OAAC;wBAAQ,WAAU;;0CAEjB,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;;;;;kDACf,8OAAC;wCAAI,WAAU;;;;;;kDACf,8OAAC;wCAAI,WAAU;;;;;;;;;;;;0CAGjB,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wCACT,SAAS;4CAAE,SAAS;4CAAG,GAAG;wCAAG;wCAC7B,SAAS;4CAAE,SAAS;4CAAG,GAAG;wCAAE;wCAC5B,YAAY;4CAAE,UAAU;wCAAI;wCAC5B,WAAU;;0DAEV,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gDACT,SAAS;oDAAE,SAAS;oDAAG,OAAO;gDAAK;gDACnC,SAAS;oDAAE,SAAS;oDAAG,OAAO;gDAAE;gDAChC,YAAY;oDAAE,UAAU;oDAAK,OAAO;gDAAI;gDACxC,WAAU;0DAEV,cAAA,8OAAC;oDAAK,WAAU;8DAAkI;;;;;;;;;;;0DAKpJ,8OAAC,0LAAA,CAAA,SAAM,CAAC,EAAE;gDACR,SAAS;oDAAE,SAAS;oDAAG,GAAG;gDAAG;gDAC7B,SAAS;oDAAE,SAAS;oDAAG,GAAG;gDAAE;gDAC5B,YAAY;oDAAE,UAAU;oDAAK,OAAO;gDAAI;gDACxC,WAAU;;oDACX;oDAC2B;kEAC1B,8OAAC;wDAAK,WAAU;kEAAgB;;;;;;;;;;;;0DAGlC,8OAAC,0LAAA,CAAA,SAAM,CAAC,CAAC;gDACP,SAAS;oDAAE,SAAS;oDAAG,GAAG;gDAAG;gDAC7B,SAAS;oDAAE,SAAS;oDAAG,GAAG;gDAAE;gDAC5B,YAAY;oDAAE,UAAU;oDAAK,OAAO;gDAAI;gDACxC,WAAU;0DACX;;;;;;0DAMD,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gDACT,SAAS;oDAAE,SAAS;oDAAG,GAAG;gDAAG;gDAC7B,SAAS;oDAAE,SAAS;oDAAG,GAAG;gDAAE;gDAC5B,YAAY;oDAAE,UAAU;oDAAK,OAAO;gDAAI;gDACxC,WAAU;;kEAEV,8OAAC,4JAAA,CAAA,UAAI;wDAAC,MAAK;wDAAW,WAAU;;4DAAoB;0EAElD,8OAAC,2NAAA,CAAA,iBAAc;gEAAC,WAAU;;;;;;;;;;;;kEAG5B,8OAAC;wDAAO,WAAU;;0EAChB,8OAAC,+MAAA,CAAA,WAAQ;gEAAC,WAAU;;;;;;4DAAiB;;;;;;;;;;;;;0DAKzC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gDACT,SAAS;oDAAE,SAAS;oDAAG,GAAG;gDAAG;gDAC7B,SAAS;oDAAE,SAAS;oDAAG,GAAG;gDAAE;gDAC5B,YAAY;oDAAE,UAAU;oDAAK,OAAO;gDAAI;gDACxC,WAAU;;kEAEV,8OAAC;wDAAE,WAAU;kEAA2C;;;;;;kEAGxD,8OAAC;wDAAI,WAAU;kEACZ,MAAM,GAAG,CAAC,CAAC,MAAM,sBAChB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gEAET,SAAS;oEAAE,SAAS;oEAAG,OAAO;gEAAI;gEAClC,SAAS;oEAAE,SAAS;oEAAG,OAAO;gEAAE;gEAChC,YAAY;oEAAE,UAAU;oEAAK,OAAO,MAAM,QAAQ;gEAAI;gEACtD,WAAU;;kFAEV,8OAAC;wEAAI,WAAU;kFACZ,KAAK,KAAK;;;;;;kFAEb,8OAAC;wEAAI,WAAU;kFACZ,KAAK,IAAI;;;;;;;+DAVP,KAAK,IAAI;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAsB9B,8OAAC;wBAAQ,IAAG;wBAAW,WAAU;kCAC/B,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oCACT,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAG;oCAC7B,aAAa;wCAAE,SAAS;wCAAG,GAAG;oCAAE;oCAChC,YAAY;wCAAE,UAAU;oCAAI;oCAC5B,UAAU;wCAAE,MAAM;oCAAK;oCACvB,WAAU;;sDAEV,8OAAC;4CAAG,WAAU;;gDAA0E;8DAClF,8OAAC;oDAAK,WAAU;8DAAgB;;;;;;;;;;;;sDAEtC,8OAAC;4CAAE,WAAU;sDAA+C;;;;;;;;;;;;8CAM9D,8OAAC;oCAAI,WAAU;8CACZ,SAAS,GAAG,CAAC,CAAC,SAAS,sBACtB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4CAET,SAAS;gDAAE,SAAS;gDAAG,GAAG;4CAAG;4CAC7B,aAAa;gDAAE,SAAS;gDAAG,GAAG;4CAAE;4CAChC,YAAY;gDAAE,UAAU;gDAAK,OAAO,QAAQ;4CAAI;4CAChD,UAAU;gDAAE,MAAM;4CAAK;4CACvB,WAAU;;8DAEV,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC,QAAQ,IAAI;wDAAC,WAAU;;;;;;;;;;;8DAG1B,8OAAC;oDAAG,WAAU;8DACX,QAAQ,IAAI;;;;;;8DAGf,8OAAC;oDAAE,WAAU;8DACV,QAAQ,WAAW;;;;;;8DAGtB,8OAAC;oDAAG,WAAU;8DACX,QAAQ,QAAQ,CAAC,GAAG,CAAC,CAAC,wBACrB,8OAAC;4DAAiB,WAAU;;8EAC1B,8OAAC;oEAAI,WAAU;;;;;;gEACd;;2DAFM;;;;;;;;;;8DAQb,8OAAC;oDAAI,WAAU;;;;;;;2CA7BV,QAAQ,IAAI;;;;;;;;;;8CAkCvB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oCACT,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAG;oCAC7B,aAAa;wCAAE,SAAS;wCAAG,GAAG;oCAAE;oCAChC,YAAY;wCAAE,UAAU;wCAAK,OAAO;oCAAI;oCACxC,UAAU;wCAAE,MAAM;oCAAK;oCACvB,WAAU;;sDAEV,8OAAC;4CAAE,WAAU;sDAA6B;;;;;;sDAG1C,8OAAC,4JAAA,CAAA,UAAI;4CAAC,MAAK;4CAAW,WAAU;sDAAc;;;;;;;;;;;;;;;;;;;;;;;kCAQpD,8OAAC;wBAAQ,WAAU;kCACjB,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oCACT,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAG;oCAC7B,aAAa;wCAAE,SAAS;wCAAG,GAAG;oCAAE;oCAChC,YAAY;wCAAE,UAAU;oCAAI;oCAC5B,UAAU;wCAAE,MAAM;oCAAK;oCACvB,WAAU;;sDAEV,8OAAC;4CAAG,WAAU;;gDAA0E;8DAC7E,8OAAC;oDAAK,WAAU;8DAAgB;;;;;;;;;;;;sDAE3C,8OAAC;4CAAE,WAAU;sDAA+C;;;;;;;;;;;;8CAM9D,8OAAC;oCAAI,WAAU;8CACZ,aAAa,GAAG,CAAC,CAAC,aAAa,sBAC9B,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4CAET,SAAS;gDAAE,SAAS;gDAAG,GAAG;4CAAG;4CAC7B,aAAa;gDAAE,SAAS;gDAAG,GAAG;4CAAE;4CAChC,YAAY;gDAAE,UAAU;gDAAK,OAAO,QAAQ;4CAAI;4CAChD,UAAU;gDAAE,MAAM;4CAAK;4CACvB,WAAU;;8DAEV,8OAAC;oDAAI,WAAU;8DACZ;2DAAI,MAAM,YAAY,MAAM;qDAAE,CAAC,GAAG,CAAC,CAAC,GAAG,kBACtC,8OAAC;4DAEC,WAAU;4DACV,SAAQ;sEAER,cAAA,8OAAC;gEAAK,GAAE;;;;;;2DAJH;;;;;;;;;;8DASX,8OAAC;oDAAW,WAAU;;wDAA6C;wDAC/D,YAAY,OAAO;wDAAC;;;;;;;8DAGxB,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;sEACb,cAAA,8OAAC;gEACC,KAAK,YAAY,KAAK;gEACtB,KAAK,YAAY,MAAM;gEACvB,WAAU;;;;;;;;;;;sEAGd,8OAAC;;8EACC,8OAAC;oEAAI,WAAU;8EACZ,YAAY,MAAM;;;;;;8EAErB,8OAAC;oEAAI,WAAU;;wEACZ,YAAY,IAAI;wEAAC;wEAAK,YAAY,OAAO;;;;;;;;;;;;;;;;;;;;2CApC3C,YAAY,EAAE;;;;;;;;;;8CA4CzB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oCACT,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAG;oCAC7B,aAAa;wCAAE,SAAS;wCAAG,GAAG;oCAAE;oCAChC,YAAY;wCAAE,UAAU;wCAAK,OAAO;oCAAI;oCACxC,UAAU;wCAAE,MAAM;oCAAK;oCACvB,WAAU;;sDAEV,8OAAC;4CAAE,WAAU;sDAAqB;;;;;;sDAGlC,8OAAC,4JAAA,CAAA,UAAI;4CAAC,MAAK;4CAAa,WAAU;sDAAgB;;;;;;;;;;;;;;;;;;;;;;;kCAQxD,8OAAC;wBAAQ,IAAG;wBAAU,WAAU;kCAC9B,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oCACT,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAG;oCAC7B,aAAa;wCAAE,SAAS;wCAAG,GAAG;oCAAE;oCAChC,YAAY;wCAAE,UAAU;oCAAI;oCAC5B,UAAU;wCAAE,MAAM;oCAAK;oCACvB,WAAU;;sDAEV,8OAAC;4CAAG,WAAU;;gDAA0E;8DAChE,8OAAC;oDAAK,WAAU;8DAAgB;;;;;;;;;;;;sDAExD,8OAAC;4CAAE,WAAU;sDAA+C;;;;;;;;;;;;8CAM9D,8OAAC;oCAAI,WAAU;;sDAEb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4CACT,SAAS;gDAAE,SAAS;gDAAG,GAAG,CAAC;4CAAG;4CAC9B,aAAa;gDAAE,SAAS;gDAAG,GAAG;4CAAE;4CAChC,YAAY;gDAAE,UAAU;4CAAI;4CAC5B,UAAU;gDAAE,MAAM;4CAAK;4CACvB,WAAU;sDAEV,cAAA,8OAAC;;kEACC,8OAAC;wDAAG,WAAU;kEAAwC;;;;;;kEACtD,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEAAI,WAAU;kFACb,cAAA,8OAAC,uNAAA,CAAA,eAAY;4EAAC,WAAU;;;;;;;;;;;kFAE1B,8OAAC;;0FACC,8OAAC;gFAAE,WAAU;0FAAoC;;;;;;0FACjD,8OAAC;gFAAE,WAAU;0FAAwB;;;;;;;;;;;;;;;;;;0EAIzC,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEAAI,WAAU;kFACb,cAAA,8OAAC,iNAAA,CAAA,YAAS;4EAAC,WAAU;;;;;;;;;;;kFAEvB,8OAAC;;0FACC,8OAAC;gFAAE,WAAU;0FAAoC;;;;;;0FACjD,8OAAC;gFAAE,WAAU;0FAAwB;;;;;;;;;;;;;;;;;;0EAIzC,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEAAI,WAAU;kFACb,cAAA,8OAAC,mNAAA,CAAA,aAAU;4EAAC,WAAU;;;;;;;;;;;kFAExB,8OAAC;;0FACC,8OAAC;gFAAE,WAAU;0FAAoC;;;;;;0FACjD,8OAAC;gFAAE,WAAU;0FAAwB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sDAQ/C,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4CACT,SAAS;gDAAE,SAAS;gDAAG,GAAG;4CAAG;4CAC7B,aAAa;gDAAE,SAAS;gDAAG,GAAG;4CAAE;4CAChC,YAAY;gDAAE,UAAU;4CAAI;4CAC5B,UAAU;gDAAE,MAAM;4CAAK;4CACvB,WAAU;sDAEV,cAAA,8OAAC;gDAAK,WAAU;;kEACd,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;;kFACC,8OAAC;wEAAM,SAAQ;wEAAY,WAAU;kFAA+C;;;;;;kFAGpF,8OAAC;wEACC,MAAK;wEACL,IAAG;wEACH,WAAU;wEACV,aAAY;;;;;;;;;;;;0EAGhB,8OAAC;;kFACC,8OAAC;wEAAM,SAAQ;wEAAW,WAAU;kFAA+C;;;;;;kFAGnF,8OAAC;wEACC,MAAK;wEACL,IAAG;wEACH,WAAU;wEACV,aAAY;;;;;;;;;;;;;;;;;;kEAKlB,8OAAC;;0EACC,8OAAC;gEAAM,SAAQ;gEAAQ,WAAU;0EAA+C;;;;;;0EAGhF,8OAAC;gEACC,MAAK;gEACL,IAAG;gEACH,WAAU;gEACV,aAAY;;;;;;;;;;;;kEAIhB,8OAAC;;0EACC,8OAAC;gEAAM,SAAQ;gEAAU,WAAU;0EAA+C;;;;;;0EAGlF,8OAAC;gEACC,MAAK;gEACL,IAAG;gEACH,WAAU;gEACV,aAAY;;;;;;;;;;;;kEAIhB,8OAAC;;0EACC,8OAAC;gEAAM,SAAQ;gEAAU,WAAU;0EAA+C;;;;;;0EAGlF,8OAAC;gEACC,IAAG;gEACH,MAAM;gEACN,WAAU;gEACV,aAAY;;;;;;;;;;;;kEAIhB,8OAAC;wDAAO,MAAK;wDAAS,WAAU;kEAAqB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BASjE,8OAAC,4HAAA,CAAA,SAAM;;;;;;;;;;;AAGb", "debugId": null}}]}