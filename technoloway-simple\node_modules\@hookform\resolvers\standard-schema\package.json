{"name": "@hookform/resolvers/standard-schema", "amdName": "hookformResolversStandardSchema", "version": "1.0.0", "private": true, "description": "React Hook Form validation resolver: standard-schema", "main": "dist/standard-schema.js", "module": "dist/standard-schema.module.js", "umd:main": "dist/standard-schema.umd.js", "source": "src/index.ts", "types": "dist/index.d.ts", "license": "MIT", "peerDependencies": {"react-hook-form": "^7.55.0", "@standard-schema/spec": "^1.0.0", "@standard-schema/utils": "^0.3.0", "@hookform/resolvers": "^2.0.0"}}