'use client';

import { useState } from 'react';
import { motion } from 'framer-motion';
import {
  PlusIcon,
  PencilIcon,
  TrashIcon,
  EyeIcon,
  MagnifyingGlassIcon,
  TagIcon,
  StarIcon,
} from '@heroicons/react/24/outline';

// Mock data - in real app, this would come from API
const technologies = [
  {
    id: 1,
    name: 'React',
    description: 'A JavaScript library for building user interfaces with component-based architecture.',
    category: 'Frontend',
    type: 'Framework',
    proficiencyLevel: 'Expert',
    yearsOfExperience: 5,
    projectsUsed: 45,
    logo: 'https://cdn.jsdelivr.net/gh/devicons/devicon/icons/react/react-original.svg',
    website: 'https://reactjs.org',
    documentation: 'https://reactjs.org/docs',
    isActive: true,
    isFeatured: true,
    tags: ['JavaScript', 'UI', 'SPA', 'Component-based'],
    createdAt: '2024-01-15T10:00:00Z',
    updatedAt: '2024-01-20T14:30:00Z',
  },
  {
    id: 2,
    name: 'Next.js',
    description: 'The React framework for production with server-side rendering and static site generation.',
    category: 'Frontend',
    type: 'Framework',
    proficiencyLevel: 'Expert',
    yearsOfExperience: 4,
    projectsUsed: 32,
    logo: 'https://cdn.jsdelivr.net/gh/devicons/devicon/icons/nextjs/nextjs-original.svg',
    website: 'https://nextjs.org',
    documentation: 'https://nextjs.org/docs',
    isActive: true,
    isFeatured: true,
    tags: ['React', 'SSR', 'SSG', 'Full-stack'],
    createdAt: '2024-01-12T11:00:00Z',
    updatedAt: '2024-01-18T16:45:00Z',
  },
  {
    id: 3,
    name: 'Node.js',
    description: 'JavaScript runtime built on Chrome\'s V8 JavaScript engine for server-side development.',
    category: 'Backend',
    type: 'Runtime',
    proficiencyLevel: 'Expert',
    yearsOfExperience: 6,
    projectsUsed: 38,
    logo: 'https://cdn.jsdelivr.net/gh/devicons/devicon/icons/nodejs/nodejs-original.svg',
    website: 'https://nodejs.org',
    documentation: 'https://nodejs.org/docs',
    isActive: true,
    isFeatured: true,
    tags: ['JavaScript', 'Server', 'API', 'Microservices'],
    createdAt: '2024-01-10T09:00:00Z',
    updatedAt: '2024-01-15T13:20:00Z',
  },
  {
    id: 4,
    name: 'TypeScript',
    description: 'Typed superset of JavaScript that compiles to plain JavaScript for better development experience.',
    category: 'Language',
    type: 'Programming Language',
    proficiencyLevel: 'Expert',
    yearsOfExperience: 4,
    projectsUsed: 42,
    logo: 'https://cdn.jsdelivr.net/gh/devicons/devicon/icons/typescript/typescript-original.svg',
    website: 'https://www.typescriptlang.org',
    documentation: 'https://www.typescriptlang.org/docs',
    isActive: true,
    isFeatured: true,
    tags: ['JavaScript', 'Type Safety', 'Development'],
    createdAt: '2024-01-08T14:00:00Z',
    updatedAt: '2024-01-12T10:15:00Z',
  },
  {
    id: 5,
    name: 'Python',
    description: 'High-level programming language known for its simplicity and versatility in various domains.',
    category: 'Language',
    type: 'Programming Language',
    proficiencyLevel: 'Advanced',
    yearsOfExperience: 7,
    projectsUsed: 28,
    logo: 'https://cdn.jsdelivr.net/gh/devicons/devicon/icons/python/python-original.svg',
    website: 'https://www.python.org',
    documentation: 'https://docs.python.org',
    isActive: true,
    isFeatured: false,
    tags: ['AI/ML', 'Data Science', 'Backend', 'Automation'],
    createdAt: '2024-01-05T16:00:00Z',
    updatedAt: '2024-01-10T12:30:00Z',
  },
  {
    id: 6,
    name: 'PostgreSQL',
    description: 'Advanced open-source relational database with strong standards compliance and extensibility.',
    category: 'Database',
    type: 'Database',
    proficiencyLevel: 'Advanced',
    yearsOfExperience: 5,
    projectsUsed: 35,
    logo: 'https://cdn.jsdelivr.net/gh/devicons/devicon/icons/postgresql/postgresql-original.svg',
    website: 'https://www.postgresql.org',
    documentation: 'https://www.postgresql.org/docs',
    isActive: true,
    isFeatured: false,
    tags: ['SQL', 'ACID', 'Scalable', 'Open Source'],
    createdAt: '2024-01-03T12:00:00Z',
    updatedAt: '2024-01-08T15:45:00Z',
  },
  {
    id: 7,
    name: 'AWS',
    description: 'Amazon Web Services cloud computing platform offering a wide range of infrastructure services.',
    category: 'Cloud',
    type: 'Platform',
    proficiencyLevel: 'Advanced',
    yearsOfExperience: 4,
    projectsUsed: 25,
    logo: 'https://cdn.jsdelivr.net/gh/devicons/devicon/icons/amazonwebservices/amazonwebservices-original.svg',
    website: 'https://aws.amazon.com',
    documentation: 'https://docs.aws.amazon.com',
    isActive: true,
    isFeatured: true,
    tags: ['Cloud', 'Infrastructure', 'Scalable', 'DevOps'],
    createdAt: '2024-01-01T10:00:00Z',
    updatedAt: '2024-01-05T11:20:00Z',
  },
  {
    id: 8,
    name: 'Docker',
    description: 'Platform for developing, shipping, and running applications using containerization technology.',
    category: 'DevOps',
    type: 'Tool',
    proficiencyLevel: 'Advanced',
    yearsOfExperience: 3,
    projectsUsed: 30,
    logo: 'https://cdn.jsdelivr.net/gh/devicons/devicon/icons/docker/docker-original.svg',
    website: 'https://www.docker.com',
    documentation: 'https://docs.docker.com',
    isActive: true,
    isFeatured: false,
    tags: ['Containerization', 'DevOps', 'Deployment'],
    createdAt: '2023-12-28T14:00:00Z',
    updatedAt: '2024-01-03T09:15:00Z',
  },
];

const categories = ['All', 'Frontend', 'Backend', 'Language', 'Database', 'Cloud', 'DevOps'];
const proficiencyLevels = ['All', 'Beginner', 'Intermediate', 'Advanced', 'Expert'];

const getProficiencyColor = (level: string) => {
  switch (level) {
    case 'Expert': return 'bg-green-100 text-green-800';
    case 'Advanced': return 'bg-blue-100 text-blue-800';
    case 'Intermediate': return 'bg-yellow-100 text-yellow-800';
    case 'Beginner': return 'bg-gray-100 text-gray-800';
    default: return 'bg-gray-100 text-gray-800';
  }
};

export default function TechnologiesPage() {
  const [techList, setTechList] = useState(technologies);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('All');
  const [selectedProficiency, setSelectedProficiency] = useState('All');
  const [selectedTech, setSelectedTech] = useState<any>(null);
  const [showAddModal, setShowAddModal] = useState(false);

  const filteredTechnologies = techList.filter(tech => {
    const matchesSearch = tech.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         tech.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         tech.tags.some(tag => tag.toLowerCase().includes(searchTerm.toLowerCase()));
    const matchesCategory = selectedCategory === 'All' || tech.category === selectedCategory;
    const matchesProficiency = selectedProficiency === 'All' || tech.proficiencyLevel === selectedProficiency;
    
    return matchesSearch && matchesCategory && matchesProficiency;
  });

  const handleToggleActive = (id: number) => {
    setTechList(prev => prev.map(tech => 
      tech.id === id 
        ? { ...tech, isActive: !tech.isActive }
        : tech
    ));
  };

  const handleToggleFeatured = (id: number) => {
    setTechList(prev => prev.map(tech => 
      tech.id === id 
        ? { ...tech, isFeatured: !tech.isFeatured }
        : tech
    ));
  };

  const handleDelete = (id: number) => {
    if (confirm('Are you sure you want to delete this technology?')) {
      setTechList(prev => prev.filter(tech => tech.id !== id));
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
    });
  };

  return (
    <div className="py-6">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 md:px-8">
        {/* Header */}
        <div className="md:flex md:items-center md:justify-between mb-8">
          <div className="flex-1 min-w-0">
            <h2 className="text-2xl font-bold leading-7 text-gray-900 sm:text-3xl sm:truncate">
              Technologies
            </h2>
            <p className="mt-1 text-sm text-gray-500">
              Manage your technology stack, skills, and expertise levels
            </p>
          </div>
          <div className="mt-4 flex md:mt-0 md:ml-4">
            <button
              onClick={() => setShowAddModal(true)}
              className="ml-3 inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
            >
              <PlusIcon className="-ml-1 mr-2 h-5 w-5" />
              Add Technology
            </button>
          </div>
        </div>

        {/* Stats */}
        <div className="mb-6 grid grid-cols-1 gap-5 sm:grid-cols-4">
          <div className="bg-white overflow-hidden shadow rounded-lg">
            <div className="p-5">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <div className="w-8 h-8 bg-blue-500 rounded-md flex items-center justify-center">
                    <span className="text-white text-sm font-bold">{techList.length}</span>
                  </div>
                </div>
                <div className="ml-5 w-0 flex-1">
                  <dl>
                    <dt className="text-sm font-medium text-gray-500 truncate">Total Technologies</dt>
                    <dd className="text-lg font-medium text-gray-900">In Stack</dd>
                  </dl>
                </div>
              </div>
            </div>
          </div>

          <div className="bg-white overflow-hidden shadow rounded-lg">
            <div className="p-5">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <div className="w-8 h-8 bg-green-500 rounded-md flex items-center justify-center">
                    <span className="text-white text-sm font-bold">
                      {techList.filter(t => t.proficiencyLevel === 'Expert').length}
                    </span>
                  </div>
                </div>
                <div className="ml-5 w-0 flex-1">
                  <dl>
                    <dt className="text-sm font-medium text-gray-500 truncate">Expert Level</dt>
                    <dd className="text-lg font-medium text-gray-900">Mastered</dd>
                  </dl>
                </div>
              </div>
            </div>
          </div>

          <div className="bg-white overflow-hidden shadow rounded-lg">
            <div className="p-5">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <div className="w-8 h-8 bg-yellow-500 rounded-md flex items-center justify-center">
                    <span className="text-white text-sm font-bold">
                      {techList.filter(t => t.isFeatured).length}
                    </span>
                  </div>
                </div>
                <div className="ml-5 w-0 flex-1">
                  <dl>
                    <dt className="text-sm font-medium text-gray-500 truncate">Featured</dt>
                    <dd className="text-lg font-medium text-gray-900">Highlighted</dd>
                  </dl>
                </div>
              </div>
            </div>
          </div>

          <div className="bg-white overflow-hidden shadow rounded-lg">
            <div className="p-5">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <div className="w-8 h-8 bg-purple-500 rounded-md flex items-center justify-center">
                    <span className="text-white text-sm font-bold">
                      {techList.reduce((sum, t) => sum + t.projectsUsed, 0)}
                    </span>
                  </div>
                </div>
                <div className="ml-5 w-0 flex-1">
                  <dl>
                    <dt className="text-sm font-medium text-gray-500 truncate">Projects Used</dt>
                    <dd className="text-lg font-medium text-gray-900">Total</dd>
                  </dl>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Filters */}
        <div className="mb-6 bg-white shadow rounded-lg">
          <div className="px-4 py-5 sm:p-6">
            <div className="grid grid-cols-1 gap-4 sm:grid-cols-3">
              {/* Search */}
              <div className="relative">
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <MagnifyingGlassIcon className="h-5 w-5 text-gray-400" />
                </div>
                <input
                  type="text"
                  placeholder="Search technologies..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                />
              </div>

              {/* Category Filter */}
              <div>
                <select
                  value={selectedCategory}
                  onChange={(e) => setSelectedCategory(e.target.value)}
                  className="block w-full pl-3 pr-10 py-2 text-base border border-gray-300 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm rounded-md"
                >
                  {categories.map(category => (
                    <option key={category} value={category}>{category}</option>
                  ))}
                </select>
              </div>

              {/* Proficiency Filter */}
              <div>
                <select
                  value={selectedProficiency}
                  onChange={(e) => setSelectedProficiency(e.target.value)}
                  className="block w-full pl-3 pr-10 py-2 text-base border border-gray-300 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm rounded-md"
                >
                  {proficiencyLevels.map(level => (
                    <option key={level} value={level}>{level}</option>
                  ))}
                </select>
              </div>
            </div>
          </div>
        </div>

        {/* Technologies Grid */}
        <div className="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4">
          {filteredTechnologies.map((tech, index) => (
            <motion.div
              key={tech.id}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.3, delay: index * 0.1 }}
              className="bg-white overflow-hidden shadow rounded-lg hover:shadow-lg transition-shadow"
            >
              <div className="p-6">
                <div className="flex items-center justify-between mb-4">
                  <div className="flex items-center space-x-3">
                    <div className="flex-shrink-0">
                      <img
                        src={tech.logo}
                        alt={tech.name}
                        className="w-10 h-10 object-contain"
                      />
                    </div>
                    <div>
                      <h3 className="text-lg font-medium text-gray-900">{tech.name}</h3>
                      <p className="text-sm text-gray-500">{tech.category}</p>
                    </div>
                  </div>
                  <div className="flex items-center space-x-1">
                    {tech.isFeatured && (
                      <StarIcon className="w-5 h-5 text-yellow-400 fill-current" />
                    )}
                  </div>
                </div>

                <p className="text-sm text-gray-600 mb-4 line-clamp-2">
                  {tech.description}
                </p>

                <div className="space-y-2 mb-4">
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-gray-500">Proficiency</span>
                    <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getProficiencyColor(tech.proficiencyLevel)}`}>
                      {tech.proficiencyLevel}
                    </span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-gray-500">Experience</span>
                    <span className="text-sm font-medium text-gray-900">{tech.yearsOfExperience} years</span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-gray-500">Projects</span>
                    <span className="text-sm font-medium text-gray-900">{tech.projectsUsed}</span>
                  </div>
                </div>

                <div className="mb-4">
                  <div className="flex flex-wrap gap-1">
                    {tech.tags.slice(0, 3).map((tag) => (
                      <span
                        key={tag}
                        className="inline-flex items-center px-2 py-1 rounded text-xs font-medium bg-gray-100 text-gray-800"
                      >
                        {tag}
                      </span>
                    ))}
                    {tech.tags.length > 3 && (
                      <span className="inline-flex items-center px-2 py-1 rounded text-xs font-medium bg-gray-100 text-gray-800">
                        +{tech.tags.length - 3}
                      </span>
                    )}
                  </div>
                </div>

                <div className="flex justify-end space-x-2">
                  <button
                    onClick={() => setSelectedTech(tech)}
                    className="text-gray-400 hover:text-blue-600 transition-colors"
                    title="View Details"
                  >
                    <EyeIcon className="h-5 w-5" />
                  </button>
                  <button
                    className="text-gray-400 hover:text-blue-600 transition-colors"
                    title="Edit"
                  >
                    <PencilIcon className="h-5 w-5" />
                  </button>
                  <button
                    onClick={() => handleToggleFeatured(tech.id)}
                    className={`text-gray-400 hover:text-yellow-600 transition-colors ${
                      tech.isFeatured ? 'text-yellow-600' : ''
                    }`}
                    title={tech.isFeatured ? 'Remove from Featured' : 'Add to Featured'}
                  >
                    <StarIcon className="h-5 w-5" />
                  </button>
                  <button
                    onClick={() => handleDelete(tech.id)}
                    className="text-gray-400 hover:text-red-600 transition-colors"
                    title="Delete"
                  >
                    <TrashIcon className="h-5 w-5" />
                  </button>
                </div>
              </div>
            </motion.div>
          ))}
        </div>

        {/* Empty State */}
        {filteredTechnologies.length === 0 && (
          <div className="bg-white shadow rounded-lg">
            <div className="text-center py-12">
              <TagIcon className="mx-auto h-12 w-12 text-gray-400" />
              <h3 className="mt-2 text-sm font-medium text-gray-900">No technologies found</h3>
              <p className="mt-1 text-sm text-gray-500">
                Try adjusting your search or filter criteria.
              </p>
            </div>
          </div>
        )}

        {/* Technology Detail Modal */}
        {selectedTech && (
          <div className="fixed inset-0 z-50 overflow-y-auto">
            <div className="flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
              <div className="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" onClick={() => setSelectedTech(null)} />
              
              <div className="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-2xl sm:w-full">
                <div className="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
                  <div className="sm:flex sm:items-start">
                    <div className="mt-3 text-center sm:mt-0 sm:text-left w-full">
                      <div className="flex items-center space-x-3 mb-4">
                        <img
                          src={selectedTech.logo}
                          alt={selectedTech.name}
                          className="w-12 h-12 object-contain"
                        />
                        <div>
                          <h3 className="text-lg leading-6 font-medium text-gray-900">
                            {selectedTech.name}
                          </h3>
                          <p className="text-sm text-gray-500">{selectedTech.category} • {selectedTech.type}</p>
                        </div>
                      </div>
                      
                      <div className="space-y-4">
                        <div>
                          <label className="block text-sm font-medium text-gray-700">Description</label>
                          <p className="mt-1 text-sm text-gray-900">{selectedTech.description}</p>
                        </div>
                        
                        <div className="grid grid-cols-2 gap-4">
                          <div>
                            <label className="block text-sm font-medium text-gray-700">Proficiency Level</label>
                            <span className={`mt-1 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getProficiencyColor(selectedTech.proficiencyLevel)}`}>
                              {selectedTech.proficiencyLevel}
                            </span>
                          </div>
                          <div>
                            <label className="block text-sm font-medium text-gray-700">Experience</label>
                            <p className="mt-1 text-sm text-gray-900">{selectedTech.yearsOfExperience} years</p>
                          </div>
                        </div>
                        
                        <div>
                          <label className="block text-sm font-medium text-gray-700">Projects Used</label>
                          <p className="mt-1 text-sm text-gray-900">{selectedTech.projectsUsed} projects</p>
                        </div>
                        
                        <div>
                          <label className="block text-sm font-medium text-gray-700">Tags</label>
                          <div className="mt-1 flex flex-wrap gap-1">
                            {selectedTech.tags.map((tag: string) => (
                              <span
                                key={tag}
                                className="inline-flex items-center px-2 py-1 rounded text-xs font-medium bg-blue-100 text-blue-800"
                              >
                                {tag}
                              </span>
                            ))}
                          </div>
                        </div>
                        
                        <div className="grid grid-cols-2 gap-4">
                          <div>
                            <label className="block text-sm font-medium text-gray-700">Website</label>
                            <a
                              href={selectedTech.website}
                              target="_blank"
                              rel="noopener noreferrer"
                              className="mt-1 text-sm text-blue-600 hover:text-blue-800"
                            >
                              {selectedTech.website}
                            </a>
                          </div>
                          <div>
                            <label className="block text-sm font-medium text-gray-700">Documentation</label>
                            <a
                              href={selectedTech.documentation}
                              target="_blank"
                              rel="noopener noreferrer"
                              className="mt-1 text-sm text-blue-600 hover:text-blue-800"
                            >
                              View Docs
                            </a>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
                <div className="bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
                  <button
                    type="button"
                    className="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-blue-600 text-base font-medium text-white hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 sm:ml-3 sm:w-auto sm:text-sm"
                  >
                    Edit Technology
                  </button>
                  <button
                    type="button"
                    className="mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 sm:mt-0 sm:w-auto sm:text-sm"
                    onClick={() => setSelectedTech(null)}
                  >
                    Close
                  </button>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
