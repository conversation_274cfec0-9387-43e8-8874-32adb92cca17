'use client';

import { useState } from 'react';
import { motion } from 'framer-motion';
import {
  PlusIcon,
  PencilIcon,
  TrashIcon,
  EyeIcon,
  DocumentTextIcon,
  CheckCircleIcon,
  XCircleIcon,
  ClockIcon,
} from '@heroicons/react/24/outline';

// Mock data - in real app, this would come from API
const aboutPages = [
  {
    id: 1,
    title: 'About Technoloway',
    slug: 'about',
    content: `<h2>Our Story</h2>
    <p>Founded in 2020, Technoloway has been at the forefront of software innovation, helping businesses transform their ideas into digital reality. We specialize in creating scalable, modern solutions that drive growth and efficiency.</p>
    
    <h3>Our Mission</h3>
    <p>To democratize cutting-edge technology and make it accessible to businesses of all sizes. We believe that every company deserves world-class software solutions.</p>
    
    <h3>Our Values</h3>
    <ul>
      <li>Innovation: We stay ahead of technology trends</li>
      <li>Quality: We deliver excellence in every project</li>
      <li>Partnership: We work as an extension of your team</li>
      <li>Transparency: We maintain open communication</li>
    </ul>`,
    metaTitle: 'About Technoloway - Leading Software Development Company',
    metaDescription: 'Learn about Technoloway\'s mission, values, and commitment to delivering exceptional software solutions for businesses worldwide.',
    isPublished: true,
    publishedAt: '2024-01-15T10:00:00Z',
    createdAt: '2024-01-10T09:00:00Z',
    updatedAt: '2024-01-20T14:30:00Z',
    author: 'Sarah Johnson',
  },
  {
    id: 2,
    title: 'Our Team',
    slug: 'team',
    content: `<h2>Meet Our Expert Team</h2>
    <p>Our diverse team of developers, designers, and strategists brings together decades of experience in software development, user experience design, and business strategy.</p>
    
    <h3>Leadership</h3>
    <p>Led by industry veterans with experience at top technology companies, our leadership team guides our vision and ensures we deliver exceptional results.</p>
    
    <h3>Development Team</h3>
    <p>Our developers are experts in modern technologies including React, Node.js, Python, and cloud platforms. They stay current with the latest trends and best practices.</p>`,
    metaTitle: 'Our Team - Expert Software Developers | Technoloway',
    metaDescription: 'Meet the talented team behind Technoloway. Our expert developers, designers, and strategists are passionate about creating exceptional software solutions.',
    isPublished: true,
    publishedAt: '2024-01-12T11:00:00Z',
    createdAt: '2024-01-08T10:00:00Z',
    updatedAt: '2024-01-18T16:45:00Z',
    author: 'Mike Chen',
  },
  {
    id: 3,
    title: 'Our Process',
    slug: 'process',
    content: `<h2>How We Work</h2>
    <p>Our proven development process ensures successful project delivery while maintaining transparency and collaboration throughout.</p>
    
    <h3>Discovery & Planning</h3>
    <p>We start by understanding your business goals, technical requirements, and user needs. This phase includes stakeholder interviews, technical analysis, and project planning.</p>
    
    <h3>Design & Development</h3>
    <p>Our iterative approach includes regular check-ins, demos, and feedback sessions to ensure we're building exactly what you need.</p>
    
    <h3>Testing & Deployment</h3>
    <p>Comprehensive testing and smooth deployment ensure your solution is ready for production use.</p>`,
    metaTitle: 'Our Development Process | Technoloway',
    metaDescription: 'Learn about Technoloway\'s proven software development process that ensures successful project delivery and client satisfaction.',
    isPublished: false,
    publishedAt: null,
    createdAt: '2024-01-05T14:00:00Z',
    updatedAt: '2024-01-15T13:20:00Z',
    author: 'Emily Davis',
  },
];

export default function AboutPagesPage() {
  const [pages, setPages] = useState(aboutPages);
  const [selectedPage, setSelectedPage] = useState<any>(null);
  const [showAddModal, setShowAddModal] = useState(false);

  const handleTogglePublished = (id: number) => {
    setPages(prev => prev.map(page => 
      page.id === id 
        ? { 
            ...page, 
            isPublished: !page.isPublished,
            publishedAt: !page.isPublished ? new Date().toISOString() : null
          }
        : page
    ));
  };

  const handleDelete = (id: number) => {
    if (confirm('Are you sure you want to delete this page?')) {
      setPages(prev => prev.filter(page => page.id !== id));
    }
  };

  const formatDate = (dateString: string | null) => {
    if (!dateString) return 'Not published';
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  const stripHtml = (html: string) => {
    return html.replace(/<[^>]*>/g, '').substring(0, 150) + '...';
  };

  return (
    <div className="py-6">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 md:px-8">
        {/* Header */}
        <div className="md:flex md:items-center md:justify-between mb-8">
          <div className="flex-1 min-w-0">
            <h2 className="text-2xl font-bold leading-7 text-gray-900 sm:text-3xl sm:truncate">
              About Pages
            </h2>
            <p className="mt-1 text-sm text-gray-500">
              Manage your company's about pages, team information, and process documentation
            </p>
          </div>
          <div className="mt-4 flex md:mt-0 md:ml-4">
            <button
              onClick={() => setShowAddModal(true)}
              className="ml-3 inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
            >
              <PlusIcon className="-ml-1 mr-2 h-5 w-5" />
              Add Page
            </button>
          </div>
        </div>

        {/* Stats */}
        <div className="mb-6 grid grid-cols-1 gap-5 sm:grid-cols-3">
          <div className="bg-white overflow-hidden shadow rounded-lg">
            <div className="p-5">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <div className="w-8 h-8 bg-blue-500 rounded-md flex items-center justify-center">
                    <span className="text-white text-sm font-bold">{pages.length}</span>
                  </div>
                </div>
                <div className="ml-5 w-0 flex-1">
                  <dl>
                    <dt className="text-sm font-medium text-gray-500 truncate">Total Pages</dt>
                    <dd className="text-lg font-medium text-gray-900">Created</dd>
                  </dl>
                </div>
              </div>
            </div>
          </div>

          <div className="bg-white overflow-hidden shadow rounded-lg">
            <div className="p-5">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <div className="w-8 h-8 bg-green-500 rounded-md flex items-center justify-center">
                    <span className="text-white text-sm font-bold">
                      {pages.filter(p => p.isPublished).length}
                    </span>
                  </div>
                </div>
                <div className="ml-5 w-0 flex-1">
                  <dl>
                    <dt className="text-sm font-medium text-gray-500 truncate">Published</dt>
                    <dd className="text-lg font-medium text-gray-900">Live</dd>
                  </dl>
                </div>
              </div>
            </div>
          </div>

          <div className="bg-white overflow-hidden shadow rounded-lg">
            <div className="p-5">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <div className="w-8 h-8 bg-yellow-500 rounded-md flex items-center justify-center">
                    <span className="text-white text-sm font-bold">
                      {pages.filter(p => !p.isPublished).length}
                    </span>
                  </div>
                </div>
                <div className="ml-5 w-0 flex-1">
                  <dl>
                    <dt className="text-sm font-medium text-gray-500 truncate">Drafts</dt>
                    <dd className="text-lg font-medium text-gray-900">Unpublished</dd>
                  </dl>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Pages List */}
        <div className="bg-white shadow overflow-hidden sm:rounded-md">
          <ul className="divide-y divide-gray-200">
            {pages.map((page, index) => (
              <motion.li
                key={page.id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.3, delay: index * 0.1 }}
              >
                <div className="px-4 py-4 sm:px-6">
                  <div className="flex items-center justify-between">
                    <div className="flex items-start space-x-4 flex-1">
                      <div className="flex-shrink-0">
                        <div className={`w-10 h-10 rounded-lg flex items-center justify-center ${
                          page.isPublished ? 'bg-green-100' : 'bg-gray-100'
                        }`}>
                          {page.isPublished ? (
                            <CheckCircleIcon className="w-6 h-6 text-green-600" />
                          ) : (
                            <ClockIcon className="w-6 h-6 text-gray-400" />
                          )}
                        </div>
                      </div>
                      
                      <div className="flex-1 min-w-0">
                        <div className="flex items-center space-x-2 mb-1">
                          <h3 className="text-lg font-medium text-gray-900 truncate">
                            {page.title}
                          </h3>
                          <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                            page.isPublished 
                              ? 'bg-green-100 text-green-800'
                              : 'bg-gray-100 text-gray-800'
                          }`}>
                            {page.isPublished ? 'Published' : 'Draft'}
                          </span>
                        </div>
                        
                        <p className="text-sm text-gray-600 mb-2">
                          /{page.slug}
                        </p>
                        
                        <p className="text-sm text-gray-600 mb-3">
                          {stripHtml(page.content)}
                        </p>
                        
                        <div className="flex items-center space-x-4 text-sm text-gray-500">
                          <span>Author: {page.author}</span>
                          <span>Created: {formatDate(page.createdAt)}</span>
                          <span>Updated: {formatDate(page.updatedAt)}</span>
                          {page.publishedAt && (
                            <span>Published: {formatDate(page.publishedAt)}</span>
                          )}
                        </div>
                      </div>
                    </div>
                    
                    <div className="flex items-center space-x-2 ml-4">
                      <button
                        onClick={() => setSelectedPage(page)}
                        className="text-gray-400 hover:text-blue-600 transition-colors"
                        title="View"
                      >
                        <EyeIcon className="h-5 w-5" />
                      </button>
                      <button
                        className="text-gray-400 hover:text-blue-600 transition-colors"
                        title="Edit"
                      >
                        <PencilIcon className="h-5 w-5" />
                      </button>
                      <button
                        onClick={() => handleTogglePublished(page.id)}
                        className={`text-gray-400 hover:text-green-600 transition-colors ${
                          page.isPublished ? 'text-green-600' : ''
                        }`}
                        title={page.isPublished ? 'Unpublish' : 'Publish'}
                      >
                        <CheckCircleIcon className="h-5 w-5" />
                      </button>
                      <button
                        onClick={() => handleDelete(page.id)}
                        className="text-gray-400 hover:text-red-600 transition-colors"
                        title="Delete"
                      >
                        <TrashIcon className="h-5 w-5" />
                      </button>
                    </div>
                  </div>
                </div>
              </motion.li>
            ))}
          </ul>
        </div>

        {/* Empty State */}
        {pages.length === 0 && (
          <div className="bg-white shadow rounded-lg">
            <div className="text-center py-12">
              <DocumentTextIcon className="mx-auto h-12 w-12 text-gray-400" />
              <h3 className="mt-2 text-sm font-medium text-gray-900">No about pages</h3>
              <p className="mt-1 text-sm text-gray-500">
                Get started by creating your first about page.
              </p>
              <div className="mt-6">
                <button
                  onClick={() => setShowAddModal(true)}
                  className="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                >
                  <PlusIcon className="-ml-1 mr-2 h-5 w-5" />
                  Add Page
                </button>
              </div>
            </div>
          </div>
        )}

        {/* Page Preview Modal */}
        {selectedPage && (
          <div className="fixed inset-0 z-50 overflow-y-auto">
            <div className="flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
              <div className="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" onClick={() => setSelectedPage(null)} />
              
              <div className="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-4xl sm:w-full">
                <div className="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
                  <div className="sm:flex sm:items-start">
                    <div className="mt-3 text-center sm:mt-0 sm:text-left w-full">
                      <h3 className="text-lg leading-6 font-medium text-gray-900 mb-4">
                        {selectedPage.title}
                      </h3>
                      
                      <div className="mb-4 p-4 bg-gray-50 rounded-lg">
                        <h4 className="text-sm font-medium text-gray-700 mb-2">SEO Information</h4>
                        <p className="text-sm text-gray-600 mb-1">
                          <strong>Meta Title:</strong> {selectedPage.metaTitle}
                        </p>
                        <p className="text-sm text-gray-600 mb-1">
                          <strong>Meta Description:</strong> {selectedPage.metaDescription}
                        </p>
                        <p className="text-sm text-gray-600">
                          <strong>URL:</strong> /{selectedPage.slug}
                        </p>
                      </div>
                      
                      <div className="prose max-w-none">
                        <div dangerouslySetInnerHTML={{ __html: selectedPage.content }} />
                      </div>
                    </div>
                  </div>
                </div>
                <div className="bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
                  <button
                    type="button"
                    className="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-blue-600 text-base font-medium text-white hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 sm:ml-3 sm:w-auto sm:text-sm"
                  >
                    Edit Page
                  </button>
                  <button
                    type="button"
                    className="mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 sm:mt-0 sm:w-auto sm:text-sm"
                    onClick={() => setSelectedPage(null)}
                  >
                    Close
                  </button>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
