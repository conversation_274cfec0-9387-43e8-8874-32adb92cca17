'use client';

import { useState } from 'react';
import { motion } from 'framer-motion';
import {
  PlusIcon,
  PencilIcon,
  TrashIcon,
  EyeIcon,
  PhotoIcon,
  PlayIcon,
  CheckCircleIcon,
  XCircleIcon,
} from '@heroicons/react/24/outline';

// Mock data - in real app, this would come from API
const heroSections = [
  {
    id: 1,
    title: 'Transform Your Ideas Into Digital Reality',
    subtitle: 'Leading Software Development Company',
    description: 'We build exceptional software solutions that drive business growth and innovation. From web applications to mobile apps, we deliver cutting-edge technology solutions.',
    primaryButtonText: 'Get Started',
    primaryButtonLink: '/contact',
    secondaryButtonText: 'View Portfolio',
    secondaryButtonLink: '/portfolio',
    backgroundImage: 'https://images.unsplash.com/photo-1451187580459-43490279c0fa?w=1920&h=1080&fit=crop',
    backgroundVideo: null,
    isActive: true,
    order: 1,
    createdAt: '2024-01-15T10:00:00Z',
    updatedAt: '2024-01-20T14:30:00Z',
  },
  {
    id: 2,
    title: 'Innovation Meets Excellence',
    subtitle: 'Your Technology Partner',
    description: 'Partner with us to leverage the latest technologies and methodologies. We specialize in scalable solutions that grow with your business.',
    primaryButtonText: 'Start Project',
    primaryButtonLink: '/contact',
    secondaryButtonText: 'Learn More',
    secondaryButtonLink: '/about',
    backgroundImage: 'https://images.unsplash.com/photo-1518709268805-4e9042af2176?w=1920&h=1080&fit=crop',
    backgroundVideo: null,
    isActive: false,
    order: 2,
    createdAt: '2024-01-10T09:00:00Z',
    updatedAt: '2024-01-18T16:45:00Z',
  },
  {
    id: 3,
    title: 'Building Tomorrow\'s Solutions Today',
    subtitle: 'Advanced Technology Solutions',
    description: 'From AI-powered applications to cloud-native architectures, we create solutions that prepare your business for the future.',
    primaryButtonText: 'Explore Services',
    primaryButtonLink: '/services',
    secondaryButtonText: 'Case Studies',
    secondaryButtonLink: '/portfolio',
    backgroundImage: 'https://images.unsplash.com/photo-1460925895917-afdab827c52f?w=1920&h=1080&fit=crop',
    backgroundVideo: null,
    isActive: false,
    order: 3,
    createdAt: '2024-01-05T11:30:00Z',
    updatedAt: '2024-01-15T13:20:00Z',
  },
];

export default function HeroSectionsPage() {
  const [sections, setSections] = useState(heroSections);
  const [showAddModal, setShowAddModal] = useState(false);
  const [editingSection, setEditingSection] = useState<any>(null);
  const [previewSection, setPreviewSection] = useState<any>(null);

  const handleToggleActive = (id: number) => {
    setSections(prev => prev.map(section => 
      section.id === id 
        ? { ...section, isActive: !section.isActive }
        : { ...section, isActive: false } // Only one can be active
    ));
  };

  const handleDelete = (id: number) => {
    if (confirm('Are you sure you want to delete this hero section?')) {
      setSections(prev => prev.filter(section => section.id !== id));
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  return (
    <div className="py-6">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 md:px-8">
        {/* Header */}
        <div className="md:flex md:items-center md:justify-between mb-8">
          <div className="flex-1 min-w-0">
            <h2 className="text-2xl font-bold leading-7 text-gray-900 sm:text-3xl sm:truncate">
              Hero Sections
            </h2>
            <p className="mt-1 text-sm text-gray-500">
              Manage homepage hero sections and call-to-action banners
            </p>
          </div>
          <div className="mt-4 flex md:mt-0 md:ml-4">
            <button
              onClick={() => setShowAddModal(true)}
              className="ml-3 inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
            >
              <PlusIcon className="-ml-1 mr-2 h-5 w-5" />
              Add Hero Section
            </button>
          </div>
        </div>

        {/* Stats */}
        <div className="mb-6 grid grid-cols-1 gap-5 sm:grid-cols-3">
          <div className="bg-white overflow-hidden shadow rounded-lg">
            <div className="p-5">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <div className="w-8 h-8 bg-blue-500 rounded-md flex items-center justify-center">
                    <span className="text-white text-sm font-bold">{sections.length}</span>
                  </div>
                </div>
                <div className="ml-5 w-0 flex-1">
                  <dl>
                    <dt className="text-sm font-medium text-gray-500 truncate">Total Sections</dt>
                    <dd className="text-lg font-medium text-gray-900">Created</dd>
                  </dl>
                </div>
              </div>
            </div>
          </div>

          <div className="bg-white overflow-hidden shadow rounded-lg">
            <div className="p-5">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <div className="w-8 h-8 bg-green-500 rounded-md flex items-center justify-center">
                    <span className="text-white text-sm font-bold">
                      {sections.filter(s => s.isActive).length}
                    </span>
                  </div>
                </div>
                <div className="ml-5 w-0 flex-1">
                  <dl>
                    <dt className="text-sm font-medium text-gray-500 truncate">Active</dt>
                    <dd className="text-lg font-medium text-gray-900">Live</dd>
                  </dl>
                </div>
              </div>
            </div>
          </div>

          <div className="bg-white overflow-hidden shadow rounded-lg">
            <div className="p-5">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <div className="w-8 h-8 bg-gray-500 rounded-md flex items-center justify-center">
                    <span className="text-white text-sm font-bold">
                      {sections.filter(s => !s.isActive).length}
                    </span>
                  </div>
                </div>
                <div className="ml-5 w-0 flex-1">
                  <dl>
                    <dt className="text-sm font-medium text-gray-500 truncate">Inactive</dt>
                    <dd className="text-lg font-medium text-gray-900">Draft</dd>
                  </dl>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Hero Sections List */}
        <div className="space-y-6">
          {sections.map((section, index) => (
            <motion.div
              key={section.id}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.3, delay: index * 0.1 }}
              className="bg-white shadow rounded-lg overflow-hidden"
            >
              <div className="relative h-48 bg-gray-200">
                <img
                  src={section.backgroundImage}
                  alt={section.title}
                  className="w-full h-full object-cover"
                />
                <div className="absolute inset-0 bg-black bg-opacity-40 flex items-center justify-center">
                  <div className="text-center text-white px-6">
                    <h3 className="text-2xl font-bold mb-2">{section.title}</h3>
                    <p className="text-lg mb-1">{section.subtitle}</p>
                    <p className="text-sm opacity-90">{section.description}</p>
                  </div>
                </div>
                <div className="absolute top-4 right-4">
                  {section.isActive ? (
                    <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                      <CheckCircleIcon className="w-4 h-4 mr-1" />
                      Active
                    </span>
                  ) : (
                    <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                      <XCircleIcon className="w-4 h-4 mr-1" />
                      Inactive
                    </span>
                  )}
                </div>
              </div>

              <div className="p-6">
                <div className="flex items-center justify-between mb-4">
                  <div>
                    <h4 className="text-lg font-medium text-gray-900">{section.title}</h4>
                    <p className="text-sm text-gray-500">Order: {section.order}</p>
                  </div>
                  <div className="flex items-center space-x-2">
                    <button
                      onClick={() => setPreviewSection(section)}
                      className="text-gray-400 hover:text-blue-600 transition-colors"
                      title="Preview"
                    >
                      <EyeIcon className="h-5 w-5" />
                    </button>
                    <button
                      onClick={() => setEditingSection(section)}
                      className="text-gray-400 hover:text-blue-600 transition-colors"
                      title="Edit"
                    >
                      <PencilIcon className="h-5 w-5" />
                    </button>
                    <button
                      onClick={() => handleToggleActive(section.id)}
                      className={`text-gray-400 hover:text-green-600 transition-colors ${
                        section.isActive ? 'text-green-600' : ''
                      }`}
                      title={section.isActive ? 'Deactivate' : 'Activate'}
                    >
                      <CheckCircleIcon className="h-5 w-5" />
                    </button>
                    <button
                      onClick={() => handleDelete(section.id)}
                      className="text-gray-400 hover:text-red-600 transition-colors"
                      title="Delete"
                    >
                      <TrashIcon className="h-5 w-5" />
                    </button>
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                  <div>
                    <p className="text-sm text-gray-500">Primary Button</p>
                    <p className="text-sm font-medium">{section.primaryButtonText} → {section.primaryButtonLink}</p>
                  </div>
                  <div>
                    <p className="text-sm text-gray-500">Secondary Button</p>
                    <p className="text-sm font-medium">{section.secondaryButtonText} → {section.secondaryButtonLink}</p>
                  </div>
                </div>

                <div className="flex items-center justify-between text-sm text-gray-500">
                  <span>Created: {formatDate(section.createdAt)}</span>
                  <span>Updated: {formatDate(section.updatedAt)}</span>
                </div>
              </div>
            </motion.div>
          ))}
        </div>

        {/* Empty State */}
        {sections.length === 0 && (
          <div className="bg-white shadow rounded-lg">
            <div className="text-center py-12">
              <PhotoIcon className="mx-auto h-12 w-12 text-gray-400" />
              <h3 className="mt-2 text-sm font-medium text-gray-900">No hero sections</h3>
              <p className="mt-1 text-sm text-gray-500">
                Get started by creating your first hero section.
              </p>
              <div className="mt-6">
                <button
                  onClick={() => setShowAddModal(true)}
                  className="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                >
                  <PlusIcon className="-ml-1 mr-2 h-5 w-5" />
                  Add Hero Section
                </button>
              </div>
            </div>
          </div>
        )}

        {/* Preview Modal */}
        {previewSection && (
          <div className="fixed inset-0 z-50 overflow-y-auto">
            <div className="flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
              <div className="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" onClick={() => setPreviewSection(null)} />
              
              <div className="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-4xl sm:w-full">
                <div className="relative h-96 bg-gray-200">
                  <img
                    src={previewSection.backgroundImage}
                    alt={previewSection.title}
                    className="w-full h-full object-cover"
                  />
                  <div className="absolute inset-0 bg-black bg-opacity-40 flex items-center justify-center">
                    <div className="text-center text-white px-6 max-w-4xl">
                      <h1 className="text-4xl md:text-6xl font-bold mb-4">{previewSection.title}</h1>
                      <p className="text-xl md:text-2xl mb-2">{previewSection.subtitle}</p>
                      <p className="text-lg mb-8 opacity-90">{previewSection.description}</p>
                      <div className="flex flex-col sm:flex-row gap-4 justify-center">
                        <button className="px-8 py-3 bg-blue-600 text-white rounded-lg font-medium hover:bg-blue-700 transition-colors">
                          {previewSection.primaryButtonText}
                        </button>
                        <button className="px-8 py-3 border-2 border-white text-white rounded-lg font-medium hover:bg-white hover:text-gray-900 transition-colors">
                          {previewSection.secondaryButtonText}
                        </button>
                      </div>
                    </div>
                  </div>
                </div>
                <div className="bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
                  <button
                    type="button"
                    className="mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 sm:mt-0 sm:w-auto sm:text-sm"
                    onClick={() => setPreviewSection(null)}
                  >
                    Close Preview
                  </button>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
