# 🚀 Technoloway Website Setup Guide

This guide will help you set up the Technoloway website project from scratch.

## 📋 Prerequisites

Before you begin, ensure you have the following installed:

- **Node.js 18+** - [Download here](https://nodejs.org/)
- **npm 9+** (comes with Node.js)
- **PostgreSQL** - [Download here](https://www.postgresql.org/download/)
- **Git** - [Download here](https://git-scm.com/)

## 🛠️ Quick Setup

### Option 1: Automated Setup (Recommended)

```bash
# Clone the repository
git clone <repository-url>
cd technoloway-website

# Run the setup script
chmod +x scripts/setup.sh
./scripts/setup.sh
```

### Option 2: Manual Setup

```bash
# 1. Clone and install dependencies
git clone <repository-url>
cd technoloway-website
npm install

# 2. Set up environment variables
cp apps/web/.env.example apps/web/.env.local

# 3. Generate Prisma client
npm run db:generate

# 4. Update environment variables (see below)
# 5. Set up database (see below)
# 6. Start development server
npm run dev
```

## 🔧 Environment Configuration

Edit `apps/web/.env.local` with your actual values:

### Required Variables

```env
# Database
DATABASE_URL="postgresql://username:password@localhost:5432/technoloway_db"

# Clerk Authentication
NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY="pk_test_your_key_here"
CLERK_SECRET_KEY="sk_test_your_secret_here"

# App Configuration
NEXT_PUBLIC_APP_URL="http://localhost:3000"
```

### Optional Variables

```env
# Email Configuration
SMTP_HOST="smtp.gmail.com"
SMTP_PORT="587"
SMTP_USER="<EMAIL>"
SMTP_PASSWORD="your-app-password"

# Analytics
NEXT_PUBLIC_GOOGLE_ANALYTICS_ID="G-XXXXXXXXXX"

# File Upload
NEXT_PUBLIC_CLOUDINARY_CLOUD_NAME="your-cloud-name"
```

## 🗄️ Database Setup

### Local PostgreSQL

1. **Install PostgreSQL** if you haven't already
2. **Create a database**:
   ```sql
   CREATE DATABASE technoloway_db;
   CREATE USER technoloway_user WITH PASSWORD 'your_password';
   GRANT ALL PRIVILEGES ON DATABASE technoloway_db TO technoloway_user;
   ```
3. **Update DATABASE_URL** in `.env.local`
4. **Push the schema**:
   ```bash
   npm run db:push
   ```

### Cloud Database (Railway/Supabase)

1. **Create a PostgreSQL database** on [Railway](https://railway.app/) or [Supabase](https://supabase.com/)
2. **Copy the connection string** to `DATABASE_URL` in `.env.local`
3. **Push the schema**:
   ```bash
   npm run db:push
   ```

## 🔐 Authentication Setup (Clerk)

1. **Create a Clerk account** at [clerk.com](https://clerk.com/)
2. **Create a new application**
3. **Copy the API keys** to your `.env.local`:
   - `NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY`
   - `CLERK_SECRET_KEY`
4. **Configure sign-in/sign-up URLs** in Clerk dashboard:
   - Sign-in URL: `/sign-in`
   - Sign-up URL: `/sign-up`
   - After sign-in URL: `/dashboard`
   - After sign-up URL: `/dashboard`

## 🚀 Development

### Start Development Server

```bash
npm run dev
```

The application will be available at `http://localhost:3000`

### Available Scripts

```bash
# Development
npm run dev              # Start development server
npm run build           # Build for production
npm run start           # Start production server

# Database
npm run db:generate     # Generate Prisma client
npm run db:push         # Push schema to database
npm run db:migrate      # Run database migrations
npm run db:studio       # Open Prisma Studio

# Testing
npm run test            # Run unit tests
npm run test:watch      # Run tests in watch mode
npm run test:e2e        # Run E2E tests
npm run test:e2e:ui     # Run E2E tests with UI

# Code Quality
npm run lint            # Lint all packages
npm run type-check      # Type check all packages
```

## 📁 Project Structure

```
technoloway-website/
├── apps/
│   └── web/                 # Next.js frontend
│       ├── src/
│       │   ├── app/         # App Router pages
│       │   ├── components/  # React components
│       │   └── lib/         # Utilities and configurations
│       ├── tests/           # Test files
│       └── public/          # Static assets
├── packages/
│   ├── ui/                  # Shared UI components
│   ├── database/            # Prisma schema & utilities
│   ├── api/                 # tRPC API routers
│   └── config/              # Shared configurations
└── scripts/                 # Setup and utility scripts
```

## 🧪 Testing

### Unit Tests

```bash
# Run all unit tests
npm run test

# Run tests in watch mode
npm run test:watch

# Run tests with coverage
npm run test -- --coverage
```

### E2E Tests

```bash
# Install Playwright browsers (first time only)
npx playwright install

# Run E2E tests
npm run test:e2e

# Run E2E tests with UI
npm run test:e2e:ui
```

## 🚀 Deployment

### Vercel (Recommended)

1. **Connect your repository** to [Vercel](https://vercel.com/)
2. **Set environment variables** in Vercel dashboard
3. **Deploy** - automatic on push to main branch

### Manual Deployment

```bash
# Build the application
npm run build

# Start production server
npm start
```

## 🔧 Customization

### Adding New Pages

1. Create a new file in `apps/web/src/app/`
2. Export a default React component
3. Add navigation links in `components/layout/header.tsx`

### Adding New API Routes

1. Create a new router in `packages/api/src/routers/`
2. Add it to the app router in `packages/api/src/root.ts`
3. Use it in your components with the tRPC client

### Styling

- **Tailwind CSS** - Utility-first CSS framework
- **Custom styles** - Add to `apps/web/src/app/globals.css`
- **Component styles** - Use Tailwind classes in components

## 🆘 Troubleshooting

### Common Issues

1. **Database connection errors**
   - Check your `DATABASE_URL` format
   - Ensure PostgreSQL is running
   - Verify database credentials

2. **Clerk authentication errors**
   - Check your API keys
   - Verify domain settings in Clerk dashboard
   - Ensure URLs are correctly configured

3. **Build errors**
   - Run `npm run type-check` to find TypeScript errors
   - Check for missing dependencies
   - Clear `.next` folder and rebuild

### Getting Help

- **Documentation**: Check the README.md
- **Issues**: Create an issue in the repository
- **Support**: Email <EMAIL>

## 📚 Next Steps

1. **Customize the content** in components and pages
2. **Add your branding** (logo, colors, fonts)
3. **Set up analytics** (Google Analytics, etc.)
4. **Configure email** for contact forms
5. **Add your content** (blog posts, case studies, etc.)
6. **Deploy to production**

Happy coding! 🎉
