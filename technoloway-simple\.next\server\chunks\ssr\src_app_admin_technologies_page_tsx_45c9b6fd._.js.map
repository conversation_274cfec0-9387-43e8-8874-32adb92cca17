{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Project%20By%20AI/Technoloway/Technoloway%20%28Processing%29/technoloway-simple/src/app/admin/technologies/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport { motion } from 'framer-motion';\nimport {\n  PlusIcon,\n  PencilIcon,\n  TrashIcon,\n  EyeIcon,\n  MagnifyingGlassIcon,\n  TagIcon,\n  StarIcon,\n} from '@heroicons/react/24/outline';\n\n// Mock data - in real app, this would come from API\nconst technologies = [\n  {\n    id: 1,\n    name: 'React',\n    description: 'A JavaScript library for building user interfaces with component-based architecture.',\n    category: 'Frontend',\n    type: 'Framework',\n    proficiencyLevel: 'Expert',\n    yearsOfExperience: 5,\n    projectsUsed: 45,\n    logo: 'https://cdn.jsdelivr.net/gh/devicons/devicon/icons/react/react-original.svg',\n    website: 'https://reactjs.org',\n    documentation: 'https://reactjs.org/docs',\n    isActive: true,\n    isFeatured: true,\n    tags: ['JavaScript', 'UI', 'SPA', 'Component-based'],\n    createdAt: '2024-01-15T10:00:00Z',\n    updatedAt: '2024-01-20T14:30:00Z',\n  },\n  {\n    id: 2,\n    name: 'Next.js',\n    description: 'The React framework for production with server-side rendering and static site generation.',\n    category: 'Frontend',\n    type: 'Framework',\n    proficiencyLevel: 'Expert',\n    yearsOfExperience: 4,\n    projectsUsed: 32,\n    logo: 'https://cdn.jsdelivr.net/gh/devicons/devicon/icons/nextjs/nextjs-original.svg',\n    website: 'https://nextjs.org',\n    documentation: 'https://nextjs.org/docs',\n    isActive: true,\n    isFeatured: true,\n    tags: ['React', 'SSR', 'SSG', 'Full-stack'],\n    createdAt: '2024-01-12T11:00:00Z',\n    updatedAt: '2024-01-18T16:45:00Z',\n  },\n  {\n    id: 3,\n    name: 'Node.js',\n    description: 'JavaScript runtime built on Chrome\\'s V8 JavaScript engine for server-side development.',\n    category: 'Backend',\n    type: 'Runtime',\n    proficiencyLevel: 'Expert',\n    yearsOfExperience: 6,\n    projectsUsed: 38,\n    logo: 'https://cdn.jsdelivr.net/gh/devicons/devicon/icons/nodejs/nodejs-original.svg',\n    website: 'https://nodejs.org',\n    documentation: 'https://nodejs.org/docs',\n    isActive: true,\n    isFeatured: true,\n    tags: ['JavaScript', 'Server', 'API', 'Microservices'],\n    createdAt: '2024-01-10T09:00:00Z',\n    updatedAt: '2024-01-15T13:20:00Z',\n  },\n  {\n    id: 4,\n    name: 'TypeScript',\n    description: 'Typed superset of JavaScript that compiles to plain JavaScript for better development experience.',\n    category: 'Language',\n    type: 'Programming Language',\n    proficiencyLevel: 'Expert',\n    yearsOfExperience: 4,\n    projectsUsed: 42,\n    logo: 'https://cdn.jsdelivr.net/gh/devicons/devicon/icons/typescript/typescript-original.svg',\n    website: 'https://www.typescriptlang.org',\n    documentation: 'https://www.typescriptlang.org/docs',\n    isActive: true,\n    isFeatured: true,\n    tags: ['JavaScript', 'Type Safety', 'Development'],\n    createdAt: '2024-01-08T14:00:00Z',\n    updatedAt: '2024-01-12T10:15:00Z',\n  },\n  {\n    id: 5,\n    name: 'Python',\n    description: 'High-level programming language known for its simplicity and versatility in various domains.',\n    category: 'Language',\n    type: 'Programming Language',\n    proficiencyLevel: 'Advanced',\n    yearsOfExperience: 7,\n    projectsUsed: 28,\n    logo: 'https://cdn.jsdelivr.net/gh/devicons/devicon/icons/python/python-original.svg',\n    website: 'https://www.python.org',\n    documentation: 'https://docs.python.org',\n    isActive: true,\n    isFeatured: false,\n    tags: ['AI/ML', 'Data Science', 'Backend', 'Automation'],\n    createdAt: '2024-01-05T16:00:00Z',\n    updatedAt: '2024-01-10T12:30:00Z',\n  },\n  {\n    id: 6,\n    name: 'PostgreSQL',\n    description: 'Advanced open-source relational database with strong standards compliance and extensibility.',\n    category: 'Database',\n    type: 'Database',\n    proficiencyLevel: 'Advanced',\n    yearsOfExperience: 5,\n    projectsUsed: 35,\n    logo: 'https://cdn.jsdelivr.net/gh/devicons/devicon/icons/postgresql/postgresql-original.svg',\n    website: 'https://www.postgresql.org',\n    documentation: 'https://www.postgresql.org/docs',\n    isActive: true,\n    isFeatured: false,\n    tags: ['SQL', 'ACID', 'Scalable', 'Open Source'],\n    createdAt: '2024-01-03T12:00:00Z',\n    updatedAt: '2024-01-08T15:45:00Z',\n  },\n  {\n    id: 7,\n    name: 'AWS',\n    description: 'Amazon Web Services cloud computing platform offering a wide range of infrastructure services.',\n    category: 'Cloud',\n    type: 'Platform',\n    proficiencyLevel: 'Advanced',\n    yearsOfExperience: 4,\n    projectsUsed: 25,\n    logo: 'https://cdn.jsdelivr.net/gh/devicons/devicon/icons/amazonwebservices/amazonwebservices-original.svg',\n    website: 'https://aws.amazon.com',\n    documentation: 'https://docs.aws.amazon.com',\n    isActive: true,\n    isFeatured: true,\n    tags: ['Cloud', 'Infrastructure', 'Scalable', 'DevOps'],\n    createdAt: '2024-01-01T10:00:00Z',\n    updatedAt: '2024-01-05T11:20:00Z',\n  },\n  {\n    id: 8,\n    name: 'Docker',\n    description: 'Platform for developing, shipping, and running applications using containerization technology.',\n    category: 'DevOps',\n    type: 'Tool',\n    proficiencyLevel: 'Advanced',\n    yearsOfExperience: 3,\n    projectsUsed: 30,\n    logo: 'https://cdn.jsdelivr.net/gh/devicons/devicon/icons/docker/docker-original.svg',\n    website: 'https://www.docker.com',\n    documentation: 'https://docs.docker.com',\n    isActive: true,\n    isFeatured: false,\n    tags: ['Containerization', 'DevOps', 'Deployment'],\n    createdAt: '2023-12-28T14:00:00Z',\n    updatedAt: '2024-01-03T09:15:00Z',\n  },\n];\n\nconst categories = ['All', 'Frontend', 'Backend', 'Language', 'Database', 'Cloud', 'DevOps'];\nconst proficiencyLevels = ['All', 'Beginner', 'Intermediate', 'Advanced', 'Expert'];\n\nconst getProficiencyColor = (level: string) => {\n  switch (level) {\n    case 'Expert': return 'bg-green-100 text-green-800';\n    case 'Advanced': return 'bg-blue-100 text-blue-800';\n    case 'Intermediate': return 'bg-yellow-100 text-yellow-800';\n    case 'Beginner': return 'bg-gray-100 text-gray-800';\n    default: return 'bg-gray-100 text-gray-800';\n  }\n};\n\nexport default function TechnologiesPage() {\n  const [techList, setTechList] = useState(technologies);\n  const [searchTerm, setSearchTerm] = useState('');\n  const [selectedCategory, setSelectedCategory] = useState('All');\n  const [selectedProficiency, setSelectedProficiency] = useState('All');\n  const [selectedTech, setSelectedTech] = useState<any>(null);\n  const [showAddModal, setShowAddModal] = useState(false);\n\n  const filteredTechnologies = techList.filter(tech => {\n    const matchesSearch = tech.name.toLowerCase().includes(searchTerm.toLowerCase()) ||\n                         tech.description.toLowerCase().includes(searchTerm.toLowerCase()) ||\n                         tech.tags.some(tag => tag.toLowerCase().includes(searchTerm.toLowerCase()));\n    const matchesCategory = selectedCategory === 'All' || tech.category === selectedCategory;\n    const matchesProficiency = selectedProficiency === 'All' || tech.proficiencyLevel === selectedProficiency;\n    \n    return matchesSearch && matchesCategory && matchesProficiency;\n  });\n\n  const handleToggleActive = (id: number) => {\n    setTechList(prev => prev.map(tech => \n      tech.id === id \n        ? { ...tech, isActive: !tech.isActive }\n        : tech\n    ));\n  };\n\n  const handleToggleFeatured = (id: number) => {\n    setTechList(prev => prev.map(tech => \n      tech.id === id \n        ? { ...tech, isFeatured: !tech.isFeatured }\n        : tech\n    ));\n  };\n\n  const handleDelete = (id: number) => {\n    if (confirm('Are you sure you want to delete this technology?')) {\n      setTechList(prev => prev.filter(tech => tech.id !== id));\n    }\n  };\n\n  const formatDate = (dateString: string) => {\n    return new Date(dateString).toLocaleDateString('en-US', {\n      year: 'numeric',\n      month: 'short',\n      day: 'numeric',\n    });\n  };\n\n  return (\n    <div className=\"py-6\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 md:px-8\">\n        {/* Header */}\n        <div className=\"md:flex md:items-center md:justify-between mb-8\">\n          <div className=\"flex-1 min-w-0\">\n            <h2 className=\"text-2xl font-bold leading-7 text-gray-900 sm:text-3xl sm:truncate\">\n              Technologies\n            </h2>\n            <p className=\"mt-1 text-sm text-gray-500\">\n              Manage your technology stack, skills, and expertise levels\n            </p>\n          </div>\n          <div className=\"mt-4 flex md:mt-0 md:ml-4\">\n            <button\n              onClick={() => setShowAddModal(true)}\n              className=\"ml-3 inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500\"\n            >\n              <PlusIcon className=\"-ml-1 mr-2 h-5 w-5\" />\n              Add Technology\n            </button>\n          </div>\n        </div>\n\n        {/* Stats */}\n        <div className=\"mb-6 grid grid-cols-1 gap-5 sm:grid-cols-4\">\n          <div className=\"bg-white overflow-hidden shadow rounded-lg\">\n            <div className=\"p-5\">\n              <div className=\"flex items-center\">\n                <div className=\"flex-shrink-0\">\n                  <div className=\"w-8 h-8 bg-blue-500 rounded-md flex items-center justify-center\">\n                    <span className=\"text-white text-sm font-bold\">{techList.length}</span>\n                  </div>\n                </div>\n                <div className=\"ml-5 w-0 flex-1\">\n                  <dl>\n                    <dt className=\"text-sm font-medium text-gray-500 truncate\">Total Technologies</dt>\n                    <dd className=\"text-lg font-medium text-gray-900\">In Stack</dd>\n                  </dl>\n                </div>\n              </div>\n            </div>\n          </div>\n\n          <div className=\"bg-white overflow-hidden shadow rounded-lg\">\n            <div className=\"p-5\">\n              <div className=\"flex items-center\">\n                <div className=\"flex-shrink-0\">\n                  <div className=\"w-8 h-8 bg-green-500 rounded-md flex items-center justify-center\">\n                    <span className=\"text-white text-sm font-bold\">\n                      {techList.filter(t => t.proficiencyLevel === 'Expert').length}\n                    </span>\n                  </div>\n                </div>\n                <div className=\"ml-5 w-0 flex-1\">\n                  <dl>\n                    <dt className=\"text-sm font-medium text-gray-500 truncate\">Expert Level</dt>\n                    <dd className=\"text-lg font-medium text-gray-900\">Mastered</dd>\n                  </dl>\n                </div>\n              </div>\n            </div>\n          </div>\n\n          <div className=\"bg-white overflow-hidden shadow rounded-lg\">\n            <div className=\"p-5\">\n              <div className=\"flex items-center\">\n                <div className=\"flex-shrink-0\">\n                  <div className=\"w-8 h-8 bg-yellow-500 rounded-md flex items-center justify-center\">\n                    <span className=\"text-white text-sm font-bold\">\n                      {techList.filter(t => t.isFeatured).length}\n                    </span>\n                  </div>\n                </div>\n                <div className=\"ml-5 w-0 flex-1\">\n                  <dl>\n                    <dt className=\"text-sm font-medium text-gray-500 truncate\">Featured</dt>\n                    <dd className=\"text-lg font-medium text-gray-900\">Highlighted</dd>\n                  </dl>\n                </div>\n              </div>\n            </div>\n          </div>\n\n          <div className=\"bg-white overflow-hidden shadow rounded-lg\">\n            <div className=\"p-5\">\n              <div className=\"flex items-center\">\n                <div className=\"flex-shrink-0\">\n                  <div className=\"w-8 h-8 bg-purple-500 rounded-md flex items-center justify-center\">\n                    <span className=\"text-white text-sm font-bold\">\n                      {techList.reduce((sum, t) => sum + t.projectsUsed, 0)}\n                    </span>\n                  </div>\n                </div>\n                <div className=\"ml-5 w-0 flex-1\">\n                  <dl>\n                    <dt className=\"text-sm font-medium text-gray-500 truncate\">Projects Used</dt>\n                    <dd className=\"text-lg font-medium text-gray-900\">Total</dd>\n                  </dl>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        {/* Filters */}\n        <div className=\"mb-6 bg-white shadow rounded-lg\">\n          <div className=\"px-4 py-5 sm:p-6\">\n            <div className=\"grid grid-cols-1 gap-4 sm:grid-cols-3\">\n              {/* Search */}\n              <div className=\"relative\">\n                <div className=\"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\">\n                  <MagnifyingGlassIcon className=\"h-5 w-5 text-gray-400\" />\n                </div>\n                <input\n                  type=\"text\"\n                  placeholder=\"Search technologies...\"\n                  value={searchTerm}\n                  onChange={(e) => setSearchTerm(e.target.value)}\n                  className=\"block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-blue-500 focus:border-blue-500 sm:text-sm\"\n                />\n              </div>\n\n              {/* Category Filter */}\n              <div>\n                <select\n                  value={selectedCategory}\n                  onChange={(e) => setSelectedCategory(e.target.value)}\n                  className=\"block w-full pl-3 pr-10 py-2 text-base border border-gray-300 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm rounded-md\"\n                >\n                  {categories.map(category => (\n                    <option key={category} value={category}>{category}</option>\n                  ))}\n                </select>\n              </div>\n\n              {/* Proficiency Filter */}\n              <div>\n                <select\n                  value={selectedProficiency}\n                  onChange={(e) => setSelectedProficiency(e.target.value)}\n                  className=\"block w-full pl-3 pr-10 py-2 text-base border border-gray-300 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm rounded-md\"\n                >\n                  {proficiencyLevels.map(level => (\n                    <option key={level} value={level}>{level}</option>\n                  ))}\n                </select>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        {/* Technologies Grid */}\n        <div className=\"grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4\">\n          {filteredTechnologies.map((tech, index) => (\n            <motion.div\n              key={tech.id}\n              initial={{ opacity: 0, y: 20 }}\n              animate={{ opacity: 1, y: 0 }}\n              transition={{ duration: 0.3, delay: index * 0.1 }}\n              className=\"bg-white overflow-hidden shadow rounded-lg hover:shadow-lg transition-shadow\"\n            >\n              <div className=\"p-6\">\n                <div className=\"flex items-center justify-between mb-4\">\n                  <div className=\"flex items-center space-x-3\">\n                    <div className=\"flex-shrink-0\">\n                      <img\n                        src={tech.logo}\n                        alt={tech.name}\n                        className=\"w-10 h-10 object-contain\"\n                      />\n                    </div>\n                    <div>\n                      <h3 className=\"text-lg font-medium text-gray-900\">{tech.name}</h3>\n                      <p className=\"text-sm text-gray-500\">{tech.category}</p>\n                    </div>\n                  </div>\n                  <div className=\"flex items-center space-x-1\">\n                    {tech.isFeatured && (\n                      <StarIcon className=\"w-5 h-5 text-yellow-400 fill-current\" />\n                    )}\n                  </div>\n                </div>\n\n                <p className=\"text-sm text-gray-600 mb-4 line-clamp-2\">\n                  {tech.description}\n                </p>\n\n                <div className=\"space-y-2 mb-4\">\n                  <div className=\"flex items-center justify-between\">\n                    <span className=\"text-sm text-gray-500\">Proficiency</span>\n                    <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getProficiencyColor(tech.proficiencyLevel)}`}>\n                      {tech.proficiencyLevel}\n                    </span>\n                  </div>\n                  <div className=\"flex items-center justify-between\">\n                    <span className=\"text-sm text-gray-500\">Experience</span>\n                    <span className=\"text-sm font-medium text-gray-900\">{tech.yearsOfExperience} years</span>\n                  </div>\n                  <div className=\"flex items-center justify-between\">\n                    <span className=\"text-sm text-gray-500\">Projects</span>\n                    <span className=\"text-sm font-medium text-gray-900\">{tech.projectsUsed}</span>\n                  </div>\n                </div>\n\n                <div className=\"mb-4\">\n                  <div className=\"flex flex-wrap gap-1\">\n                    {tech.tags.slice(0, 3).map((tag) => (\n                      <span\n                        key={tag}\n                        className=\"inline-flex items-center px-2 py-1 rounded text-xs font-medium bg-gray-100 text-gray-800\"\n                      >\n                        {tag}\n                      </span>\n                    ))}\n                    {tech.tags.length > 3 && (\n                      <span className=\"inline-flex items-center px-2 py-1 rounded text-xs font-medium bg-gray-100 text-gray-800\">\n                        +{tech.tags.length - 3}\n                      </span>\n                    )}\n                  </div>\n                </div>\n\n                <div className=\"flex justify-end space-x-2\">\n                  <button\n                    onClick={() => setSelectedTech(tech)}\n                    className=\"text-gray-400 hover:text-blue-600 transition-colors\"\n                    title=\"View Details\"\n                  >\n                    <EyeIcon className=\"h-5 w-5\" />\n                  </button>\n                  <button\n                    className=\"text-gray-400 hover:text-blue-600 transition-colors\"\n                    title=\"Edit\"\n                  >\n                    <PencilIcon className=\"h-5 w-5\" />\n                  </button>\n                  <button\n                    onClick={() => handleToggleFeatured(tech.id)}\n                    className={`text-gray-400 hover:text-yellow-600 transition-colors ${\n                      tech.isFeatured ? 'text-yellow-600' : ''\n                    }`}\n                    title={tech.isFeatured ? 'Remove from Featured' : 'Add to Featured'}\n                  >\n                    <StarIcon className=\"h-5 w-5\" />\n                  </button>\n                  <button\n                    onClick={() => handleDelete(tech.id)}\n                    className=\"text-gray-400 hover:text-red-600 transition-colors\"\n                    title=\"Delete\"\n                  >\n                    <TrashIcon className=\"h-5 w-5\" />\n                  </button>\n                </div>\n              </div>\n            </motion.div>\n          ))}\n        </div>\n\n        {/* Empty State */}\n        {filteredTechnologies.length === 0 && (\n          <div className=\"bg-white shadow rounded-lg\">\n            <div className=\"text-center py-12\">\n              <TagIcon className=\"mx-auto h-12 w-12 text-gray-400\" />\n              <h3 className=\"mt-2 text-sm font-medium text-gray-900\">No technologies found</h3>\n              <p className=\"mt-1 text-sm text-gray-500\">\n                Try adjusting your search or filter criteria.\n              </p>\n            </div>\n          </div>\n        )}\n\n        {/* Technology Detail Modal */}\n        {selectedTech && (\n          <div className=\"fixed inset-0 z-50 overflow-y-auto\">\n            <div className=\"flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0\">\n              <div className=\"fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity\" onClick={() => setSelectedTech(null)} />\n              \n              <div className=\"inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-2xl sm:w-full\">\n                <div className=\"bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4\">\n                  <div className=\"sm:flex sm:items-start\">\n                    <div className=\"mt-3 text-center sm:mt-0 sm:text-left w-full\">\n                      <div className=\"flex items-center space-x-3 mb-4\">\n                        <img\n                          src={selectedTech.logo}\n                          alt={selectedTech.name}\n                          className=\"w-12 h-12 object-contain\"\n                        />\n                        <div>\n                          <h3 className=\"text-lg leading-6 font-medium text-gray-900\">\n                            {selectedTech.name}\n                          </h3>\n                          <p className=\"text-sm text-gray-500\">{selectedTech.category} • {selectedTech.type}</p>\n                        </div>\n                      </div>\n                      \n                      <div className=\"space-y-4\">\n                        <div>\n                          <label className=\"block text-sm font-medium text-gray-700\">Description</label>\n                          <p className=\"mt-1 text-sm text-gray-900\">{selectedTech.description}</p>\n                        </div>\n                        \n                        <div className=\"grid grid-cols-2 gap-4\">\n                          <div>\n                            <label className=\"block text-sm font-medium text-gray-700\">Proficiency Level</label>\n                            <span className={`mt-1 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getProficiencyColor(selectedTech.proficiencyLevel)}`}>\n                              {selectedTech.proficiencyLevel}\n                            </span>\n                          </div>\n                          <div>\n                            <label className=\"block text-sm font-medium text-gray-700\">Experience</label>\n                            <p className=\"mt-1 text-sm text-gray-900\">{selectedTech.yearsOfExperience} years</p>\n                          </div>\n                        </div>\n                        \n                        <div>\n                          <label className=\"block text-sm font-medium text-gray-700\">Projects Used</label>\n                          <p className=\"mt-1 text-sm text-gray-900\">{selectedTech.projectsUsed} projects</p>\n                        </div>\n                        \n                        <div>\n                          <label className=\"block text-sm font-medium text-gray-700\">Tags</label>\n                          <div className=\"mt-1 flex flex-wrap gap-1\">\n                            {selectedTech.tags.map((tag: string) => (\n                              <span\n                                key={tag}\n                                className=\"inline-flex items-center px-2 py-1 rounded text-xs font-medium bg-blue-100 text-blue-800\"\n                              >\n                                {tag}\n                              </span>\n                            ))}\n                          </div>\n                        </div>\n                        \n                        <div className=\"grid grid-cols-2 gap-4\">\n                          <div>\n                            <label className=\"block text-sm font-medium text-gray-700\">Website</label>\n                            <a\n                              href={selectedTech.website}\n                              target=\"_blank\"\n                              rel=\"noopener noreferrer\"\n                              className=\"mt-1 text-sm text-blue-600 hover:text-blue-800\"\n                            >\n                              {selectedTech.website}\n                            </a>\n                          </div>\n                          <div>\n                            <label className=\"block text-sm font-medium text-gray-700\">Documentation</label>\n                            <a\n                              href={selectedTech.documentation}\n                              target=\"_blank\"\n                              rel=\"noopener noreferrer\"\n                              className=\"mt-1 text-sm text-blue-600 hover:text-blue-800\"\n                            >\n                              View Docs\n                            </a>\n                          </div>\n                        </div>\n                      </div>\n                    </div>\n                  </div>\n                </div>\n                <div className=\"bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse\">\n                  <button\n                    type=\"button\"\n                    className=\"w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-blue-600 text-base font-medium text-white hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 sm:ml-3 sm:w-auto sm:text-sm\"\n                  >\n                    Edit Technology\n                  </button>\n                  <button\n                    type=\"button\"\n                    className=\"mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 sm:mt-0 sm:w-auto sm:text-sm\"\n                    onClick={() => setSelectedTech(null)}\n                  >\n                    Close\n                  </button>\n                </div>\n              </div>\n            </div>\n          </div>\n        )}\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAJA;;;;;AAcA,oDAAoD;AACpD,MAAM,eAAe;IACnB;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,UAAU;QACV,MAAM;QACN,kBAAkB;QAClB,mBAAmB;QACnB,cAAc;QACd,MAAM;QACN,SAAS;QACT,eAAe;QACf,UAAU;QACV,YAAY;QACZ,MAAM;YAAC;YAAc;YAAM;YAAO;SAAkB;QACpD,WAAW;QACX,WAAW;IACb;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,UAAU;QACV,MAAM;QACN,kBAAkB;QAClB,mBAAmB;QACnB,cAAc;QACd,MAAM;QACN,SAAS;QACT,eAAe;QACf,UAAU;QACV,YAAY;QACZ,MAAM;YAAC;YAAS;YAAO;YAAO;SAAa;QAC3C,WAAW;QACX,WAAW;IACb;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,UAAU;QACV,MAAM;QACN,kBAAkB;QAClB,mBAAmB;QACnB,cAAc;QACd,MAAM;QACN,SAAS;QACT,eAAe;QACf,UAAU;QACV,YAAY;QACZ,MAAM;YAAC;YAAc;YAAU;YAAO;SAAgB;QACtD,WAAW;QACX,WAAW;IACb;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,UAAU;QACV,MAAM;QACN,kBAAkB;QAClB,mBAAmB;QACnB,cAAc;QACd,MAAM;QACN,SAAS;QACT,eAAe;QACf,UAAU;QACV,YAAY;QACZ,MAAM;YAAC;YAAc;YAAe;SAAc;QAClD,WAAW;QACX,WAAW;IACb;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,UAAU;QACV,MAAM;QACN,kBAAkB;QAClB,mBAAmB;QACnB,cAAc;QACd,MAAM;QACN,SAAS;QACT,eAAe;QACf,UAAU;QACV,YAAY;QACZ,MAAM;YAAC;YAAS;YAAgB;YAAW;SAAa;QACxD,WAAW;QACX,WAAW;IACb;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,UAAU;QACV,MAAM;QACN,kBAAkB;QAClB,mBAAmB;QACnB,cAAc;QACd,MAAM;QACN,SAAS;QACT,eAAe;QACf,UAAU;QACV,YAAY;QACZ,MAAM;YAAC;YAAO;YAAQ;YAAY;SAAc;QAChD,WAAW;QACX,WAAW;IACb;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,UAAU;QACV,MAAM;QACN,kBAAkB;QAClB,mBAAmB;QACnB,cAAc;QACd,MAAM;QACN,SAAS;QACT,eAAe;QACf,UAAU;QACV,YAAY;QACZ,MAAM;YAAC;YAAS;YAAkB;YAAY;SAAS;QACvD,WAAW;QACX,WAAW;IACb;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,UAAU;QACV,MAAM;QACN,kBAAkB;QAClB,mBAAmB;QACnB,cAAc;QACd,MAAM;QACN,SAAS;QACT,eAAe;QACf,UAAU;QACV,YAAY;QACZ,MAAM;YAAC;YAAoB;YAAU;SAAa;QAClD,WAAW;QACX,WAAW;IACb;CACD;AAED,MAAM,aAAa;IAAC;IAAO;IAAY;IAAW;IAAY;IAAY;IAAS;CAAS;AAC5F,MAAM,oBAAoB;IAAC;IAAO;IAAY;IAAgB;IAAY;CAAS;AAEnF,MAAM,sBAAsB,CAAC;IAC3B,OAAQ;QACN,KAAK;YAAU,OAAO;QACtB,KAAK;YAAY,OAAO;QACxB,KAAK;YAAgB,OAAO;QAC5B,KAAK;YAAY,OAAO;QACxB;YAAS,OAAO;IAClB;AACF;AAEe,SAAS;IACtB,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,CAAC,qBAAqB,uBAAuB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/D,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAO;IACtD,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEjD,MAAM,uBAAuB,SAAS,MAAM,CAAC,CAAA;QAC3C,MAAM,gBAAgB,KAAK,IAAI,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW,OACxD,KAAK,WAAW,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW,OAC9D,KAAK,IAAI,CAAC,IAAI,CAAC,CAAA,MAAO,IAAI,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW;QAC5F,MAAM,kBAAkB,qBAAqB,SAAS,KAAK,QAAQ,KAAK;QACxE,MAAM,qBAAqB,wBAAwB,SAAS,KAAK,gBAAgB,KAAK;QAEtF,OAAO,iBAAiB,mBAAmB;IAC7C;IAEA,MAAM,qBAAqB,CAAC;QAC1B,YAAY,CAAA,OAAQ,KAAK,GAAG,CAAC,CAAA,OAC3B,KAAK,EAAE,KAAK,KACR;oBAAE,GAAG,IAAI;oBAAE,UAAU,CAAC,KAAK,QAAQ;gBAAC,IACpC;IAER;IAEA,MAAM,uBAAuB,CAAC;QAC5B,YAAY,CAAA,OAAQ,KAAK,GAAG,CAAC,CAAA,OAC3B,KAAK,EAAE,KAAK,KACR;oBAAE,GAAG,IAAI;oBAAE,YAAY,CAAC,KAAK,UAAU;gBAAC,IACxC;IAER;IAEA,MAAM,eAAe,CAAC;QACpB,IAAI,QAAQ,qDAAqD;YAC/D,YAAY,CAAA,OAAQ,KAAK,MAAM,CAAC,CAAA,OAAQ,KAAK,EAAE,KAAK;QACtD;IACF;IAEA,MAAM,aAAa,CAAC;QAClB,OAAO,IAAI,KAAK,YAAY,kBAAkB,CAAC,SAAS;YACtD,MAAM;YACN,OAAO;YACP,KAAK;QACP;IACF;IAEA,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;;8BAEb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAAqE;;;;;;8CAGnF,8OAAC;oCAAE,WAAU;8CAA6B;;;;;;;;;;;;sCAI5C,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCACC,SAAS,IAAM,gBAAgB;gCAC/B,WAAU;;kDAEV,8OAAC,+MAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;oCAAuB;;;;;;;;;;;;;;;;;;8BAOjD,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDAAK,WAAU;8DAAgC,SAAS,MAAM;;;;;;;;;;;;;;;;sDAGnE,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;;kEACC,8OAAC;wDAAG,WAAU;kEAA6C;;;;;;kEAC3D,8OAAC;wDAAG,WAAU;kEAAoC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAO5D,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDAAK,WAAU;8DACb,SAAS,MAAM,CAAC,CAAA,IAAK,EAAE,gBAAgB,KAAK,UAAU,MAAM;;;;;;;;;;;;;;;;sDAInE,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;;kEACC,8OAAC;wDAAG,WAAU;kEAA6C;;;;;;kEAC3D,8OAAC;wDAAG,WAAU;kEAAoC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAO5D,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDAAK,WAAU;8DACb,SAAS,MAAM,CAAC,CAAA,IAAK,EAAE,UAAU,EAAE,MAAM;;;;;;;;;;;;;;;;sDAIhD,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;;kEACC,8OAAC;wDAAG,WAAU;kEAA6C;;;;;;kEAC3D,8OAAC;wDAAG,WAAU;kEAAoC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAO5D,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDAAK,WAAU;8DACb,SAAS,MAAM,CAAC,CAAC,KAAK,IAAM,MAAM,EAAE,YAAY,EAAE;;;;;;;;;;;;;;;;sDAIzD,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;;kEACC,8OAAC;wDAAG,WAAU;kEAA6C;;;;;;kEAC3D,8OAAC;wDAAG,WAAU;kEAAoC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAS9D,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CAEb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC,qOAAA,CAAA,sBAAmB;gDAAC,WAAU;;;;;;;;;;;sDAEjC,8OAAC;4CACC,MAAK;4CACL,aAAY;4CACZ,OAAO;4CACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;4CAC7C,WAAU;;;;;;;;;;;;8CAKd,8OAAC;8CACC,cAAA,8OAAC;wCACC,OAAO;wCACP,UAAU,CAAC,IAAM,oBAAoB,EAAE,MAAM,CAAC,KAAK;wCACnD,WAAU;kDAET,WAAW,GAAG,CAAC,CAAA,yBACd,8OAAC;gDAAsB,OAAO;0DAAW;+CAA5B;;;;;;;;;;;;;;;8CAMnB,8OAAC;8CACC,cAAA,8OAAC;wCACC,OAAO;wCACP,UAAU,CAAC,IAAM,uBAAuB,EAAE,MAAM,CAAC,KAAK;wCACtD,WAAU;kDAET,kBAAkB,GAAG,CAAC,CAAA,sBACrB,8OAAC;gDAAmB,OAAO;0DAAQ;+CAAtB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BASzB,8OAAC;oBAAI,WAAU;8BACZ,qBAAqB,GAAG,CAAC,CAAC,MAAM,sBAC/B,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BAET,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAC5B,YAAY;gCAAE,UAAU;gCAAK,OAAO,QAAQ;4BAAI;4BAChD,WAAU;sCAEV,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC;4DACC,KAAK,KAAK,IAAI;4DACd,KAAK,KAAK,IAAI;4DACd,WAAU;;;;;;;;;;;kEAGd,8OAAC;;0EACC,8OAAC;gEAAG,WAAU;0EAAqC,KAAK,IAAI;;;;;;0EAC5D,8OAAC;gEAAE,WAAU;0EAAyB,KAAK,QAAQ;;;;;;;;;;;;;;;;;;0DAGvD,8OAAC;gDAAI,WAAU;0DACZ,KAAK,UAAU,kBACd,8OAAC,+MAAA,CAAA,WAAQ;oDAAC,WAAU;;;;;;;;;;;;;;;;;kDAK1B,8OAAC;wCAAE,WAAU;kDACV,KAAK,WAAW;;;;;;kDAGnB,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAK,WAAU;kEAAwB;;;;;;kEACxC,8OAAC;wDAAK,WAAW,CAAC,wEAAwE,EAAE,oBAAoB,KAAK,gBAAgB,GAAG;kEACrI,KAAK,gBAAgB;;;;;;;;;;;;0DAG1B,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAK,WAAU;kEAAwB;;;;;;kEACxC,8OAAC;wDAAK,WAAU;;4DAAqC,KAAK,iBAAiB;4DAAC;;;;;;;;;;;;;0DAE9E,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAK,WAAU;kEAAwB;;;;;;kEACxC,8OAAC;wDAAK,WAAU;kEAAqC,KAAK,YAAY;;;;;;;;;;;;;;;;;;kDAI1E,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAI,WAAU;;gDACZ,KAAK,IAAI,CAAC,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,oBAC1B,8OAAC;wDAEC,WAAU;kEAET;uDAHI;;;;;gDAMR,KAAK,IAAI,CAAC,MAAM,GAAG,mBAClB,8OAAC;oDAAK,WAAU;;wDAA2F;wDACvG,KAAK,IAAI,CAAC,MAAM,GAAG;;;;;;;;;;;;;;;;;;kDAM7B,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDACC,SAAS,IAAM,gBAAgB;gDAC/B,WAAU;gDACV,OAAM;0DAEN,cAAA,8OAAC,6MAAA,CAAA,UAAO;oDAAC,WAAU;;;;;;;;;;;0DAErB,8OAAC;gDACC,WAAU;gDACV,OAAM;0DAEN,cAAA,8OAAC,mNAAA,CAAA,aAAU;oDAAC,WAAU;;;;;;;;;;;0DAExB,8OAAC;gDACC,SAAS,IAAM,qBAAqB,KAAK,EAAE;gDAC3C,WAAW,CAAC,sDAAsD,EAChE,KAAK,UAAU,GAAG,oBAAoB,IACtC;gDACF,OAAO,KAAK,UAAU,GAAG,yBAAyB;0DAElD,cAAA,8OAAC,+MAAA,CAAA,WAAQ;oDAAC,WAAU;;;;;;;;;;;0DAEtB,8OAAC;gDACC,SAAS,IAAM,aAAa,KAAK,EAAE;gDACnC,WAAU;gDACV,OAAM;0DAEN,cAAA,8OAAC,iNAAA,CAAA,YAAS;oDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;2BA/FtB,KAAK,EAAE;;;;;;;;;;gBAwGjB,qBAAqB,MAAM,KAAK,mBAC/B,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,6MAAA,CAAA,UAAO;gCAAC,WAAU;;;;;;0CACnB,8OAAC;gCAAG,WAAU;0CAAyC;;;;;;0CACvD,8OAAC;gCAAE,WAAU;0CAA6B;;;;;;;;;;;;;;;;;gBAQ/C,8BACC,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;gCAA6D,SAAS,IAAM,gBAAgB;;;;;;0CAE3G,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEACC,KAAK,aAAa,IAAI;gEACtB,KAAK,aAAa,IAAI;gEACtB,WAAU;;;;;;0EAEZ,8OAAC;;kFACC,8OAAC;wEAAG,WAAU;kFACX,aAAa,IAAI;;;;;;kFAEpB,8OAAC;wEAAE,WAAU;;4EAAyB,aAAa,QAAQ;4EAAC;4EAAI,aAAa,IAAI;;;;;;;;;;;;;;;;;;;kEAIrF,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;;kFACC,8OAAC;wEAAM,WAAU;kFAA0C;;;;;;kFAC3D,8OAAC;wEAAE,WAAU;kFAA8B,aAAa,WAAW;;;;;;;;;;;;0EAGrE,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;;0FACC,8OAAC;gFAAM,WAAU;0FAA0C;;;;;;0FAC3D,8OAAC;gFAAK,WAAW,CAAC,6EAA6E,EAAE,oBAAoB,aAAa,gBAAgB,GAAG;0FAClJ,aAAa,gBAAgB;;;;;;;;;;;;kFAGlC,8OAAC;;0FACC,8OAAC;gFAAM,WAAU;0FAA0C;;;;;;0FAC3D,8OAAC;gFAAE,WAAU;;oFAA8B,aAAa,iBAAiB;oFAAC;;;;;;;;;;;;;;;;;;;0EAI9E,8OAAC;;kFACC,8OAAC;wEAAM,WAAU;kFAA0C;;;;;;kFAC3D,8OAAC;wEAAE,WAAU;;4EAA8B,aAAa,YAAY;4EAAC;;;;;;;;;;;;;0EAGvE,8OAAC;;kFACC,8OAAC;wEAAM,WAAU;kFAA0C;;;;;;kFAC3D,8OAAC;wEAAI,WAAU;kFACZ,aAAa,IAAI,CAAC,GAAG,CAAC,CAAC,oBACtB,8OAAC;gFAEC,WAAU;0FAET;+EAHI;;;;;;;;;;;;;;;;0EASb,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;;0FACC,8OAAC;gFAAM,WAAU;0FAA0C;;;;;;0FAC3D,8OAAC;gFACC,MAAM,aAAa,OAAO;gFAC1B,QAAO;gFACP,KAAI;gFACJ,WAAU;0FAET,aAAa,OAAO;;;;;;;;;;;;kFAGzB,8OAAC;;0FACC,8OAAC;gFAAM,WAAU;0FAA0C;;;;;;0FAC3D,8OAAC;gFACC,MAAM,aAAa,aAAa;gFAChC,QAAO;gFACP,KAAI;gFACJ,WAAU;0FACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kDASb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDACC,MAAK;gDACL,WAAU;0DACX;;;;;;0DAGD,8OAAC;gDACC,MAAK;gDACL,WAAU;gDACV,SAAS,IAAM,gBAAgB;0DAChC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAWnB", "debugId": null}}]}