{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Project%20By%20AI/Technoloway/Technoloway%20%28Processing%29/technoloway-simple/src/app/page.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport { useState } from 'react';\r\nimport Link from 'next/link';\r\nimport { motion } from 'framer-motion';\r\nimport {\r\n  ArrowRightIcon,\r\n  PlayIcon,\r\n  Bars3Icon,\r\n  XMarkIcon,\r\n  CodeBracketIcon,\r\n  DevicePhoneMobileIcon,\r\n  CloudIcon,\r\n  CogIcon,\r\n  ShieldCheckIcon,\r\n  ChartBarIcon,\r\n  EnvelopeIcon,\r\n  PhoneIcon,\r\n  MapPinIcon\r\n} from '@heroicons/react/24/outline';\r\n\r\nconst navigation = [\r\n  { name: 'Home', href: '#' },\r\n  { name: 'Services', href: '#services' },\r\n  { name: 'About', href: '#about' },\r\n  { name: 'Contact', href: '#contact' },\r\n];\r\n\r\nconst stats = [\r\n  { name: 'Projects Delivered', value: '500+' },\r\n  { name: 'Happy Clients', value: '200+' },\r\n  { name: 'Years Experience', value: '10+' },\r\n  { name: 'Team Members', value: '50+' },\r\n];\r\n\r\nconst services = [\r\n  {\r\n    name: 'Web Development',\r\n    description: 'Modern, responsive web applications built with the latest technologies and best practices.',\r\n    icon: CodeBracketIcon,\r\n    features: ['React/Next.js', 'TypeScript', 'Tailwind CSS', 'API Integration'],\r\n  },\r\n  {\r\n    name: 'Mobile Development',\r\n    description: 'Native and cross-platform mobile applications for iOS and Android.',\r\n    icon: DevicePhoneMobileIcon,\r\n    features: ['React Native', 'Flutter', 'iOS/Android', 'App Store Deployment'],\r\n  },\r\n  {\r\n    name: 'Cloud Solutions',\r\n    description: 'Scalable cloud infrastructure and deployment solutions for modern applications.',\r\n    icon: CloudIcon,\r\n    features: ['AWS/Azure/GCP', 'Docker/Kubernetes', 'CI/CD Pipelines', 'Monitoring'],\r\n  },\r\n  {\r\n    name: 'API Development',\r\n    description: 'Robust and scalable APIs and microservices architecture.',\r\n    icon: CogIcon,\r\n    features: ['REST/GraphQL', 'Microservices', 'Database Design', 'Documentation'],\r\n  },\r\n  {\r\n    name: 'Security & Testing',\r\n    description: 'Comprehensive security audits and automated testing solutions.',\r\n    icon: ShieldCheckIcon,\r\n    features: ['Security Audits', 'Automated Testing', 'Code Review', 'Performance'],\r\n  },\r\n  {\r\n    name: 'Analytics & Insights',\r\n    description: 'Data-driven insights and analytics solutions for business growth.',\r\n    icon: ChartBarIcon,\r\n    features: ['Data Analytics', 'Business Intelligence', 'Reporting', 'Dashboards'],\r\n  },\r\n];\r\n\r\nexport default function Home() {\r\n  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);\r\n\r\n  return (\r\n    <div className=\"min-h-screen bg-white\">\r\n      {/* Header */}\r\n      <header className=\"fixed inset-x-0 top-0 z-50 bg-white/80 backdrop-blur-md border-b border-gray-200\">\r\n        <nav className=\"container flex items-center justify-between py-4\" aria-label=\"Global\">\r\n          <div className=\"flex lg:flex-1\">\r\n            <Link href=\"#\" className=\"-m-1.5 p-1.5\">\r\n              <span className=\"sr-only\">Technoloway</span>\r\n              <div className=\"flex items-center space-x-2\">\r\n                <div className=\"h-8 w-8 bg-gradient-to-r from-blue-600 to-blue-400 rounded-lg flex items-center justify-center\">\r\n                  <span className=\"text-white font-bold text-sm\">T</span>\r\n                </div>\r\n                <span className=\"text-xl font-bold text-gray-900\">Technoloway</span>\r\n              </div>\r\n            </Link>\r\n          </div>\r\n\r\n          <div className=\"flex lg:hidden\">\r\n            <button\r\n              type=\"button\"\r\n              className=\"-m-2.5 inline-flex items-center justify-center rounded-md p-2.5 text-gray-700\"\r\n              onClick={() => setMobileMenuOpen(true)}\r\n            >\r\n              <span className=\"sr-only\">Open main menu</span>\r\n              <Bars3Icon className=\"h-6 w-6\" aria-hidden=\"true\" />\r\n            </button>\r\n          </div>\r\n\r\n          <div className=\"hidden lg:flex lg:gap-x-8\">\r\n            {navigation.map((item) => (\r\n              <Link\r\n                key={item.name}\r\n                href={item.href}\r\n                className=\"text-sm font-semibold leading-6 text-gray-900 hover:text-blue-600 transition-colors\"\r\n              >\r\n                {item.name}\r\n              </Link>\r\n            ))}\r\n          </div>\r\n\r\n          <div className=\"hidden lg:flex lg:flex-1 lg:justify-end\">\r\n            <Link href=\"#contact\" className=\"btn-primary\">\r\n              Get Started\r\n            </Link>\r\n          </div>\r\n        </nav>\r\n\r\n        {/* Mobile menu */}\r\n        {mobileMenuOpen && (\r\n          <div className=\"lg:hidden\">\r\n            <div className=\"fixed inset-0 z-50\" />\r\n            <div className=\"fixed inset-y-0 right-0 z-50 w-full overflow-y-auto bg-white px-6 py-6 sm:max-w-sm sm:ring-1 sm:ring-gray-900/10\">\r\n              <div className=\"flex items-center justify-between\">\r\n                <Link href=\"#\" className=\"-m-1.5 p-1.5\">\r\n                  <span className=\"sr-only\">Technoloway</span>\r\n                  <div className=\"flex items-center space-x-2\">\r\n                    <div className=\"h-8 w-8 bg-gradient-to-r from-blue-600 to-blue-400 rounded-lg flex items-center justify-center\">\r\n                      <span className=\"text-white font-bold text-sm\">T</span>\r\n                    </div>\r\n                    <span className=\"text-xl font-bold text-gray-900\">Technoloway</span>\r\n                  </div>\r\n                </Link>\r\n                <button\r\n                  type=\"button\"\r\n                  className=\"-m-2.5 rounded-md p-2.5 text-gray-700\"\r\n                  onClick={() => setMobileMenuOpen(false)}\r\n                >\r\n                  <span className=\"sr-only\">Close menu</span>\r\n                  <XMarkIcon className=\"h-6 w-6\" aria-hidden=\"true\" />\r\n                </button>\r\n              </div>\r\n              <div className=\"mt-6 flow-root\">\r\n                <div className=\"-my-6 divide-y divide-gray-500/10\">\r\n                  <div className=\"space-y-2 py-6\">\r\n                    {navigation.map((item) => (\r\n                      <Link\r\n                        key={item.name}\r\n                        href={item.href}\r\n                        className=\"-mx-3 block rounded-lg px-3 py-2 text-base font-semibold leading-7 text-gray-900 hover:bg-gray-50\"\r\n                        onClick={() => setMobileMenuOpen(false)}\r\n                      >\r\n                        {item.name}\r\n                      </Link>\r\n                    ))}\r\n                  </div>\r\n                  <div className=\"py-6\">\r\n                    <Link\r\n                      href=\"#contact\"\r\n                      className=\"-mx-3 block rounded-lg px-3 py-2.5 text-base font-semibold leading-7 text-blue-600 hover:bg-gray-50\"\r\n                      onClick={() => setMobileMenuOpen(false)}\r\n                    >\r\n                      Get Started\r\n                    </Link>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        )}\r\n      </header>\r\n\r\n      <main>\r\n        {/* Hero Section */}\r\n        <section className=\"relative min-h-screen flex items-center justify-center overflow-hidden bg-gradient-to-br from-gray-50 via-white to-gray-100\">\r\n          {/* Background decoration */}\r\n          <div className=\"absolute inset-0 overflow-hidden\">\r\n            <div className=\"absolute -top-40 -right-32 w-80 h-80 bg-blue-100 rounded-full mix-blend-multiply filter blur-xl opacity-70 animate-blob\" />\r\n            <div className=\"absolute -bottom-40 -left-32 w-80 h-80 bg-purple-100 rounded-full mix-blend-multiply filter blur-xl opacity-70 animate-blob animation-delay-2000\" />\r\n            <div className=\"absolute top-40 left-40 w-80 h-80 bg-pink-100 rounded-full mix-blend-multiply filter blur-xl opacity-70 animate-blob animation-delay-4000\" />\r\n          </div>\r\n\r\n          <div className=\"container relative z-10 pt-20\">\r\n            <div className=\"text-center\">\r\n              <motion.div\r\n                initial={{ opacity: 0, y: 20 }}\r\n                animate={{ opacity: 1, y: 0 }}\r\n                transition={{ duration: 0.6 }}\r\n                className=\"mx-auto max-w-4xl\"\r\n              >\r\n                <motion.div\r\n                  initial={{ opacity: 0, scale: 0.95 }}\r\n                  animate={{ opacity: 1, scale: 1 }}\r\n                  transition={{ duration: 0.5, delay: 0.1 }}\r\n                  className=\"mb-8\"\r\n                >\r\n                  <span className=\"inline-flex items-center rounded-full bg-blue-50 px-6 py-2 text-sm font-medium text-blue-700 ring-1 ring-inset ring-blue-700/10\">\r\n                    🚀 Building the Future of Software\r\n                  </span>\r\n                </motion.div>\r\n\r\n                <motion.h1\r\n                  initial={{ opacity: 0, y: 20 }}\r\n                  animate={{ opacity: 1, y: 0 }}\r\n                  transition={{ duration: 0.6, delay: 0.2 }}\r\n                  className=\"text-4xl font-bold tracking-tight text-gray-900 sm:text-6xl lg:text-7xl\"\r\n                >\r\n                  Transform Your Ideas Into{' '}\r\n                  <span className=\"gradient-text\">Digital Reality</span>\r\n                </motion.h1>\r\n\r\n                <motion.p\r\n                  initial={{ opacity: 0, y: 20 }}\r\n                  animate={{ opacity: 1, y: 0 }}\r\n                  transition={{ duration: 0.6, delay: 0.3 }}\r\n                  className=\"mt-6 text-lg leading-8 text-gray-600 sm:text-xl lg:text-2xl max-w-3xl mx-auto\"\r\n                >\r\n                  We craft exceptional software solutions that drive business growth.\r\n                  From web applications to mobile apps and enterprise systems,\r\n                  we turn your vision into powerful digital experiences.\r\n                </motion.p>\r\n\r\n                <motion.div\r\n                  initial={{ opacity: 0, y: 20 }}\r\n                  animate={{ opacity: 1, y: 0 }}\r\n                  transition={{ duration: 0.6, delay: 0.4 }}\r\n                  className=\"mt-10 flex flex-col sm:flex-row items-center justify-center gap-4\"\r\n                >\r\n                  <Link href=\"#contact\" className=\"btn-primary group\">\r\n                    Start Your Project\r\n                    <ArrowRightIcon className=\"ml-2 h-4 w-4 transition-transform group-hover:translate-x-1\" />\r\n                  </Link>\r\n\r\n                  <button className=\"btn-secondary group\">\r\n                    <PlayIcon className=\"mr-2 h-4 w-4\" />\r\n                    Watch Our Story\r\n                  </button>\r\n                </motion.div>\r\n\r\n                <motion.div\r\n                  initial={{ opacity: 0, y: 20 }}\r\n                  animate={{ opacity: 1, y: 0 }}\r\n                  transition={{ duration: 0.6, delay: 0.5 }}\r\n                  className=\"mt-16\"\r\n                >\r\n                  <p className=\"text-sm font-semibold text-gray-900 mb-8\">\r\n                    Trusted by industry leaders\r\n                  </p>\r\n                  <div className=\"grid grid-cols-2 gap-8 md:grid-cols-4\">\r\n                    {stats.map((stat, index) => (\r\n                      <motion.div\r\n                        key={stat.name}\r\n                        initial={{ opacity: 0, scale: 0.8 }}\r\n                        animate={{ opacity: 1, scale: 1 }}\r\n                        transition={{ duration: 0.5, delay: 0.6 + index * 0.1 }}\r\n                        className=\"text-center\"\r\n                      >\r\n                        <div className=\"text-3xl font-bold text-blue-600 sm:text-4xl\">\r\n                          {stat.value}\r\n                        </div>\r\n                        <div className=\"mt-2 text-sm text-gray-600\">\r\n                          {stat.name}\r\n                        </div>\r\n                      </motion.div>\r\n                    ))}\r\n                  </div>\r\n                </motion.div>\r\n              </motion.div>\r\n            </div>\r\n          </div>\r\n        </section>\r\n\r\n        {/* Services Section */}\r\n        <section id=\"services\" className=\"section-padding bg-white\">\r\n          <div className=\"container\">\r\n            <motion.div\r\n              initial={{ opacity: 0, y: 20 }}\r\n              whileInView={{ opacity: 1, y: 0 }}\r\n              transition={{ duration: 0.6 }}\r\n              viewport={{ once: true }}\r\n              className=\"text-center mb-16\"\r\n            >\r\n              <h2 className=\"text-3xl font-bold tracking-tight text-gray-900 sm:text-4xl lg:text-5xl\">\r\n                Our <span className=\"gradient-text\">Services</span>\r\n              </h2>\r\n              <p className=\"mt-4 text-lg text-gray-600 max-w-3xl mx-auto\">\r\n                We offer comprehensive software development services to help your business\r\n                thrive in the digital world. From concept to deployment, we've got you covered.\r\n              </p>\r\n            </motion.div>\r\n\r\n            <div className=\"grid grid-cols-1 gap-8 md:grid-cols-2 lg:grid-cols-3\">\r\n              {services.map((service, index) => (\r\n                <motion.div\r\n                  key={service.name}\r\n                  initial={{ opacity: 0, y: 20 }}\r\n                  whileInView={{ opacity: 1, y: 0 }}\r\n                  transition={{ duration: 0.6, delay: index * 0.1 }}\r\n                  viewport={{ once: true }}\r\n                  className=\"group relative bg-white p-8 rounded-2xl border border-gray-200 hover:border-blue-300 hover:shadow-lg transition-all duration-300\"\r\n                >\r\n                  <div className=\"flex items-center justify-center w-12 h-12 bg-blue-100 rounded-lg mb-6 group-hover:bg-blue-200 transition-colors\">\r\n                    <service.icon className=\"w-6 h-6 text-blue-600\" />\r\n                  </div>\r\n\r\n                  <h3 className=\"text-xl font-semibold text-gray-900 mb-3\">\r\n                    {service.name}\r\n                  </h3>\r\n\r\n                  <p className=\"text-gray-600 mb-6\">\r\n                    {service.description}\r\n                  </p>\r\n\r\n                  <ul className=\"space-y-2\">\r\n                    {service.features.map((feature) => (\r\n                      <li key={feature} className=\"flex items-center text-sm text-gray-500\">\r\n                        <div className=\"w-1.5 h-1.5 bg-blue-500 rounded-full mr-3\" />\r\n                        {feature}\r\n                      </li>\r\n                    ))}\r\n                  </ul>\r\n\r\n                  {/* Hover effect */}\r\n                  <div className=\"absolute inset-0 bg-gradient-to-r from-blue-50 to-purple-50 rounded-2xl opacity-0 group-hover:opacity-100 transition-opacity duration-300 -z-10\" />\r\n                </motion.div>\r\n              ))}\r\n            </div>\r\n\r\n            <motion.div\r\n              initial={{ opacity: 0, y: 20 }}\r\n              whileInView={{ opacity: 1, y: 0 }}\r\n              transition={{ duration: 0.6, delay: 0.8 }}\r\n              viewport={{ once: true }}\r\n              className=\"text-center mt-16\"\r\n            >\r\n              <p className=\"text-lg text-gray-600 mb-8\">\r\n                Need something custom? We'd love to discuss your unique requirements.\r\n              </p>\r\n              <Link href=\"#contact\" className=\"btn-primary\">\r\n                Get Custom Quote\r\n              </Link>\r\n            </motion.div>\r\n          </div>\r\n        </section>\r\n\r\n        {/* Contact Section */}\r\n        <section id=\"contact\" className=\"section-padding bg-gray-50\">\r\n          <div className=\"container\">\r\n            <motion.div\r\n              initial={{ opacity: 0, y: 20 }}\r\n              whileInView={{ opacity: 1, y: 0 }}\r\n              transition={{ duration: 0.6 }}\r\n              viewport={{ once: true }}\r\n              className=\"text-center mb-16\"\r\n            >\r\n              <h2 className=\"text-3xl font-bold tracking-tight text-gray-900 sm:text-4xl lg:text-5xl\">\r\n                Let's Build Something <span className=\"gradient-text\">Amazing</span>\r\n              </h2>\r\n              <p className=\"mt-4 text-lg text-gray-600 max-w-3xl mx-auto\">\r\n                Ready to transform your ideas into reality? Get in touch with our team\r\n                and let's discuss how we can help your business grow.\r\n              </p>\r\n            </motion.div>\r\n\r\n            <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-12\">\r\n              {/* Contact Info */}\r\n              <motion.div\r\n                initial={{ opacity: 0, x: -20 }}\r\n                whileInView={{ opacity: 1, x: 0 }}\r\n                transition={{ duration: 0.6 }}\r\n                viewport={{ once: true }}\r\n                className=\"space-y-8\"\r\n              >\r\n                <div>\r\n                  <h3 className=\"text-2xl font-bold text-gray-900 mb-6\">Get in Touch</h3>\r\n                  <div className=\"space-y-6\">\r\n                    <div className=\"flex items-center space-x-4\">\r\n                      <div className=\"flex items-center justify-center w-12 h-12 bg-blue-100 rounded-lg\">\r\n                        <EnvelopeIcon className=\"w-6 h-6 text-blue-600\" />\r\n                      </div>\r\n                      <div>\r\n                        <p className=\"text-sm font-medium text-gray-500\">Email</p>\r\n                        <p className=\"text-lg text-gray-900\"><EMAIL></p>\r\n                      </div>\r\n                    </div>\r\n\r\n                    <div className=\"flex items-center space-x-4\">\r\n                      <div className=\"flex items-center justify-center w-12 h-12 bg-blue-100 rounded-lg\">\r\n                        <PhoneIcon className=\"w-6 h-6 text-blue-600\" />\r\n                      </div>\r\n                      <div>\r\n                        <p className=\"text-sm font-medium text-gray-500\">Phone</p>\r\n                        <p className=\"text-lg text-gray-900\">+****************</p>\r\n                      </div>\r\n                    </div>\r\n\r\n                    <div className=\"flex items-center space-x-4\">\r\n                      <div className=\"flex items-center justify-center w-12 h-12 bg-blue-100 rounded-lg\">\r\n                        <MapPinIcon className=\"w-6 h-6 text-blue-600\" />\r\n                      </div>\r\n                      <div>\r\n                        <p className=\"text-sm font-medium text-gray-500\">Location</p>\r\n                        <p className=\"text-lg text-gray-900\">San Francisco, CA</p>\r\n                      </div>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n              </motion.div>\r\n\r\n              {/* Contact Form */}\r\n              <motion.div\r\n                initial={{ opacity: 0, x: 20 }}\r\n                whileInView={{ opacity: 1, x: 0 }}\r\n                transition={{ duration: 0.6 }}\r\n                viewport={{ once: true }}\r\n                className=\"bg-white p-8 rounded-2xl shadow-lg\"\r\n              >\r\n                <form className=\"space-y-6\">\r\n                  <div className=\"grid grid-cols-1 sm:grid-cols-2 gap-6\">\r\n                    <div>\r\n                      <label htmlFor=\"firstName\" className=\"block text-sm font-medium text-gray-700 mb-2\">\r\n                        First Name\r\n                      </label>\r\n                      <input\r\n                        type=\"text\"\r\n                        id=\"firstName\"\r\n                        className=\"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-colors\"\r\n                        placeholder=\"John\"\r\n                      />\r\n                    </div>\r\n                    <div>\r\n                      <label htmlFor=\"lastName\" className=\"block text-sm font-medium text-gray-700 mb-2\">\r\n                        Last Name\r\n                      </label>\r\n                      <input\r\n                        type=\"text\"\r\n                        id=\"lastName\"\r\n                        className=\"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-colors\"\r\n                        placeholder=\"Doe\"\r\n                      />\r\n                    </div>\r\n                  </div>\r\n\r\n                  <div>\r\n                    <label htmlFor=\"email\" className=\"block text-sm font-medium text-gray-700 mb-2\">\r\n                      Email\r\n                    </label>\r\n                    <input\r\n                      type=\"email\"\r\n                      id=\"email\"\r\n                      className=\"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-colors\"\r\n                      placeholder=\"<EMAIL>\"\r\n                    />\r\n                  </div>\r\n\r\n                  <div>\r\n                    <label htmlFor=\"company\" className=\"block text-sm font-medium text-gray-700 mb-2\">\r\n                      Company\r\n                    </label>\r\n                    <input\r\n                      type=\"text\"\r\n                      id=\"company\"\r\n                      className=\"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-colors\"\r\n                      placeholder=\"Your Company\"\r\n                    />\r\n                  </div>\r\n\r\n                  <div>\r\n                    <label htmlFor=\"message\" className=\"block text-sm font-medium text-gray-700 mb-2\">\r\n                      Message\r\n                    </label>\r\n                    <textarea\r\n                      id=\"message\"\r\n                      rows={4}\r\n                      className=\"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-colors\"\r\n                      placeholder=\"Tell us about your project...\"\r\n                    />\r\n                  </div>\r\n\r\n                  <button type=\"submit\" className=\"w-full btn-primary\">\r\n                    Send Message\r\n                  </button>\r\n                </form>\r\n              </motion.div>\r\n            </div>\r\n          </div>\r\n        </section>\r\n      </main>\r\n\r\n      {/* Footer */}\r\n      <footer className=\"bg-gray-900 text-white\">\r\n        <div className=\"container py-16\">\r\n          <div className=\"grid grid-cols-1 md:grid-cols-4 gap-8\">\r\n            <div className=\"col-span-1 md:col-span-2\">\r\n              <div className=\"flex items-center space-x-2 mb-6\">\r\n                <div className=\"h-8 w-8 bg-gradient-to-r from-blue-600 to-blue-400 rounded-lg flex items-center justify-center\">\r\n                  <span className=\"text-white font-bold text-sm\">T</span>\r\n                </div>\r\n                <span className=\"text-xl font-bold\">Technoloway</span>\r\n              </div>\r\n              <p className=\"text-gray-300 mb-6 max-w-md\">\r\n                Transforming ideas into digital reality. We build exceptional software\r\n                solutions that drive business growth and innovation.\r\n              </p>\r\n              <div className=\"space-y-2\">\r\n                <p className=\"text-gray-300\"><EMAIL></p>\r\n                <p className=\"text-gray-300\">+****************</p>\r\n                <p className=\"text-gray-300\">San Francisco, CA</p>\r\n              </div>\r\n            </div>\r\n\r\n            <div>\r\n              <h3 className=\"text-lg font-semibold mb-4\">Services</h3>\r\n              <ul className=\"space-y-2 text-gray-300\">\r\n                <li><Link href=\"#\" className=\"hover:text-white transition-colors\">Web Development</Link></li>\r\n                <li><Link href=\"#\" className=\"hover:text-white transition-colors\">Mobile Apps</Link></li>\r\n                <li><Link href=\"#\" className=\"hover:text-white transition-colors\">Cloud Solutions</Link></li>\r\n                <li><Link href=\"#\" className=\"hover:text-white transition-colors\">API Development</Link></li>\r\n              </ul>\r\n            </div>\r\n\r\n            <div>\r\n              <h3 className=\"text-lg font-semibold mb-4\">Company</h3>\r\n              <ul className=\"space-y-2 text-gray-300\">\r\n                <li><Link href=\"#about\" className=\"hover:text-white transition-colors\">About</Link></li>\r\n                <li><Link href=\"#\" className=\"hover:text-white transition-colors\">Careers</Link></li>\r\n                <li><Link href=\"#\" className=\"hover:text-white transition-colors\">Blog</Link></li>\r\n                <li><Link href=\"#contact\" className=\"hover:text-white transition-colors\">Contact</Link></li>\r\n              </ul>\r\n            </div>\r\n          </div>\r\n\r\n          <div className=\"border-t border-gray-700 mt-12 pt-8 text-center\">\r\n            <p className=\"text-gray-400\">\r\n              &copy; {new Date().getFullYear()} Technoloway. All rights reserved.\r\n            </p>\r\n          </div>\r\n        </div>\r\n      </footer>\r\n    </div>\r\n  );\r\n}"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;AALA;;;;;AAqBA,MAAM,aAAa;IACjB;QAAE,MAAM;QAAQ,MAAM;IAAI;IAC1B;QAAE,MAAM;QAAY,MAAM;IAAY;IACtC;QAAE,MAAM;QAAS,MAAM;IAAS;IAChC;QAAE,MAAM;QAAW,MAAM;IAAW;CACrC;AAED,MAAM,QAAQ;IACZ;QAAE,MAAM;QAAsB,OAAO;IAAO;IAC5C;QAAE,MAAM;QAAiB,OAAO;IAAO;IACvC;QAAE,MAAM;QAAoB,OAAO;IAAM;IACzC;QAAE,MAAM;QAAgB,OAAO;IAAM;CACtC;AAED,MAAM,WAAW;IACf;QACE,MAAM;QACN,aAAa;QACb,MAAM,gOAAA,CAAA,kBAAe;QACrB,UAAU;YAAC;YAAiB;YAAc;YAAgB;SAAkB;IAC9E;IACA;QACE,MAAM;QACN,aAAa;QACb,MAAM,4OAAA,CAAA,wBAAqB;QAC3B,UAAU;YAAC;YAAgB;YAAW;YAAe;SAAuB;IAC9E;IACA;QACE,MAAM;QACN,aAAa;QACb,MAAM,oNAAA,CAAA,YAAS;QACf,UAAU;YAAC;YAAiB;YAAqB;YAAmB;SAAa;IACnF;IACA;QACE,MAAM;QACN,aAAa;QACb,MAAM,gNAAA,CAAA,UAAO;QACb,UAAU;YAAC;YAAgB;YAAiB;YAAmB;SAAgB;IACjF;IACA;QACE,MAAM;QACN,aAAa;QACb,MAAM,gOAAA,CAAA,kBAAe;QACrB,UAAU;YAAC;YAAmB;YAAqB;YAAe;SAAc;IAClF;IACA;QACE,MAAM;QACN,aAAa;QACb,MAAM,0NAAA,CAAA,eAAY;QAClB,UAAU;YAAC;YAAkB;YAAyB;YAAa;SAAa;IAClF;CACD;AAEc,SAAS;;IACtB,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAErD,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAO,WAAU;;kCAChB,6LAAC;wBAAI,WAAU;wBAAmD,cAAW;;0CAC3E,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC,+JAAA,CAAA,UAAI;oCAAC,MAAK;oCAAI,WAAU;;sDACvB,6LAAC;4CAAK,WAAU;sDAAU;;;;;;sDAC1B,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC;wDAAK,WAAU;kEAA+B;;;;;;;;;;;8DAEjD,6LAAC;oDAAK,WAAU;8DAAkC;;;;;;;;;;;;;;;;;;;;;;;0CAKxD,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCACC,MAAK;oCACL,WAAU;oCACV,SAAS,IAAM,kBAAkB;;sDAEjC,6LAAC;4CAAK,WAAU;sDAAU;;;;;;sDAC1B,6LAAC,oNAAA,CAAA,YAAS;4CAAC,WAAU;4CAAU,eAAY;;;;;;;;;;;;;;;;;0CAI/C,6LAAC;gCAAI,WAAU;0CACZ,WAAW,GAAG,CAAC,CAAC,qBACf,6LAAC,+JAAA,CAAA,UAAI;wCAEH,MAAM,KAAK,IAAI;wCACf,WAAU;kDAET,KAAK,IAAI;uCAJL,KAAK,IAAI;;;;;;;;;;0CASpB,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC,+JAAA,CAAA,UAAI;oCAAC,MAAK;oCAAW,WAAU;8CAAc;;;;;;;;;;;;;;;;;oBAOjD,gCACC,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;;;;;0CACf,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,+JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAI,WAAU;;kEACvB,6LAAC;wDAAK,WAAU;kEAAU;;;;;;kEAC1B,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAI,WAAU;0EACb,cAAA,6LAAC;oEAAK,WAAU;8EAA+B;;;;;;;;;;;0EAEjD,6LAAC;gEAAK,WAAU;0EAAkC;;;;;;;;;;;;;;;;;;0DAGtD,6LAAC;gDACC,MAAK;gDACL,WAAU;gDACV,SAAS,IAAM,kBAAkB;;kEAEjC,6LAAC;wDAAK,WAAU;kEAAU;;;;;;kEAC1B,6LAAC,oNAAA,CAAA,YAAS;wDAAC,WAAU;wDAAU,eAAY;;;;;;;;;;;;;;;;;;kDAG/C,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;8DACZ,WAAW,GAAG,CAAC,CAAC,qBACf,6LAAC,+JAAA,CAAA,UAAI;4DAEH,MAAM,KAAK,IAAI;4DACf,WAAU;4DACV,SAAS,IAAM,kBAAkB;sEAEhC,KAAK,IAAI;2DALL,KAAK,IAAI;;;;;;;;;;8DASpB,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC,+JAAA,CAAA,UAAI;wDACH,MAAK;wDACL,WAAU;wDACV,SAAS,IAAM,kBAAkB;kEAClC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAWf,6LAAC;;kCAEC,6LAAC;wBAAQ,WAAU;;0CAEjB,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;;;;;kDACf,6LAAC;wCAAI,WAAU;;;;;;kDACf,6LAAC;wCAAI,WAAU;;;;;;;;;;;;0CAGjB,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wCACT,SAAS;4CAAE,SAAS;4CAAG,GAAG;wCAAG;wCAC7B,SAAS;4CAAE,SAAS;4CAAG,GAAG;wCAAE;wCAC5B,YAAY;4CAAE,UAAU;wCAAI;wCAC5B,WAAU;;0DAEV,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gDACT,SAAS;oDAAE,SAAS;oDAAG,OAAO;gDAAK;gDACnC,SAAS;oDAAE,SAAS;oDAAG,OAAO;gDAAE;gDAChC,YAAY;oDAAE,UAAU;oDAAK,OAAO;gDAAI;gDACxC,WAAU;0DAEV,cAAA,6LAAC;oDAAK,WAAU;8DAAkI;;;;;;;;;;;0DAKpJ,6LAAC,6LAAA,CAAA,SAAM,CAAC,EAAE;gDACR,SAAS;oDAAE,SAAS;oDAAG,GAAG;gDAAG;gDAC7B,SAAS;oDAAE,SAAS;oDAAG,GAAG;gDAAE;gDAC5B,YAAY;oDAAE,UAAU;oDAAK,OAAO;gDAAI;gDACxC,WAAU;;oDACX;oDAC2B;kEAC1B,6LAAC;wDAAK,WAAU;kEAAgB;;;;;;;;;;;;0DAGlC,6LAAC,6LAAA,CAAA,SAAM,CAAC,CAAC;gDACP,SAAS;oDAAE,SAAS;oDAAG,GAAG;gDAAG;gDAC7B,SAAS;oDAAE,SAAS;oDAAG,GAAG;gDAAE;gDAC5B,YAAY;oDAAE,UAAU;oDAAK,OAAO;gDAAI;gDACxC,WAAU;0DACX;;;;;;0DAMD,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gDACT,SAAS;oDAAE,SAAS;oDAAG,GAAG;gDAAG;gDAC7B,SAAS;oDAAE,SAAS;oDAAG,GAAG;gDAAE;gDAC5B,YAAY;oDAAE,UAAU;oDAAK,OAAO;gDAAI;gDACxC,WAAU;;kEAEV,6LAAC,+JAAA,CAAA,UAAI;wDAAC,MAAK;wDAAW,WAAU;;4DAAoB;0EAElD,6LAAC,8NAAA,CAAA,iBAAc;gEAAC,WAAU;;;;;;;;;;;;kEAG5B,6LAAC;wDAAO,WAAU;;0EAChB,6LAAC,kNAAA,CAAA,WAAQ;gEAAC,WAAU;;;;;;4DAAiB;;;;;;;;;;;;;0DAKzC,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gDACT,SAAS;oDAAE,SAAS;oDAAG,GAAG;gDAAG;gDAC7B,SAAS;oDAAE,SAAS;oDAAG,GAAG;gDAAE;gDAC5B,YAAY;oDAAE,UAAU;oDAAK,OAAO;gDAAI;gDACxC,WAAU;;kEAEV,6LAAC;wDAAE,WAAU;kEAA2C;;;;;;kEAGxD,6LAAC;wDAAI,WAAU;kEACZ,MAAM,GAAG,CAAC,CAAC,MAAM,sBAChB,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gEAET,SAAS;oEAAE,SAAS;oEAAG,OAAO;gEAAI;gEAClC,SAAS;oEAAE,SAAS;oEAAG,OAAO;gEAAE;gEAChC,YAAY;oEAAE,UAAU;oEAAK,OAAO,MAAM,QAAQ;gEAAI;gEACtD,WAAU;;kFAEV,6LAAC;wEAAI,WAAU;kFACZ,KAAK,KAAK;;;;;;kFAEb,6LAAC;wEAAI,WAAU;kFACZ,KAAK,IAAI;;;;;;;+DAVP,KAAK,IAAI;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAsB9B,6LAAC;wBAAQ,IAAG;wBAAW,WAAU;kCAC/B,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oCACT,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAG;oCAC7B,aAAa;wCAAE,SAAS;wCAAG,GAAG;oCAAE;oCAChC,YAAY;wCAAE,UAAU;oCAAI;oCAC5B,UAAU;wCAAE,MAAM;oCAAK;oCACvB,WAAU;;sDAEV,6LAAC;4CAAG,WAAU;;gDAA0E;8DAClF,6LAAC;oDAAK,WAAU;8DAAgB;;;;;;;;;;;;sDAEtC,6LAAC;4CAAE,WAAU;sDAA+C;;;;;;;;;;;;8CAM9D,6LAAC;oCAAI,WAAU;8CACZ,SAAS,GAAG,CAAC,CAAC,SAAS,sBACtB,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4CAET,SAAS;gDAAE,SAAS;gDAAG,GAAG;4CAAG;4CAC7B,aAAa;gDAAE,SAAS;gDAAG,GAAG;4CAAE;4CAChC,YAAY;gDAAE,UAAU;gDAAK,OAAO,QAAQ;4CAAI;4CAChD,UAAU;gDAAE,MAAM;4CAAK;4CACvB,WAAU;;8DAEV,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC,QAAQ,IAAI;wDAAC,WAAU;;;;;;;;;;;8DAG1B,6LAAC;oDAAG,WAAU;8DACX,QAAQ,IAAI;;;;;;8DAGf,6LAAC;oDAAE,WAAU;8DACV,QAAQ,WAAW;;;;;;8DAGtB,6LAAC;oDAAG,WAAU;8DACX,QAAQ,QAAQ,CAAC,GAAG,CAAC,CAAC,wBACrB,6LAAC;4DAAiB,WAAU;;8EAC1B,6LAAC;oEAAI,WAAU;;;;;;gEACd;;2DAFM;;;;;;;;;;8DAQb,6LAAC;oDAAI,WAAU;;;;;;;2CA7BV,QAAQ,IAAI;;;;;;;;;;8CAkCvB,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oCACT,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAG;oCAC7B,aAAa;wCAAE,SAAS;wCAAG,GAAG;oCAAE;oCAChC,YAAY;wCAAE,UAAU;wCAAK,OAAO;oCAAI;oCACxC,UAAU;wCAAE,MAAM;oCAAK;oCACvB,WAAU;;sDAEV,6LAAC;4CAAE,WAAU;sDAA6B;;;;;;sDAG1C,6LAAC,+JAAA,CAAA,UAAI;4CAAC,MAAK;4CAAW,WAAU;sDAAc;;;;;;;;;;;;;;;;;;;;;;;kCAQpD,6LAAC;wBAAQ,IAAG;wBAAU,WAAU;kCAC9B,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oCACT,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAG;oCAC7B,aAAa;wCAAE,SAAS;wCAAG,GAAG;oCAAE;oCAChC,YAAY;wCAAE,UAAU;oCAAI;oCAC5B,UAAU;wCAAE,MAAM;oCAAK;oCACvB,WAAU;;sDAEV,6LAAC;4CAAG,WAAU;;gDAA0E;8DAChE,6LAAC;oDAAK,WAAU;8DAAgB;;;;;;;;;;;;sDAExD,6LAAC;4CAAE,WAAU;sDAA+C;;;;;;;;;;;;8CAM9D,6LAAC;oCAAI,WAAU;;sDAEb,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4CACT,SAAS;gDAAE,SAAS;gDAAG,GAAG,CAAC;4CAAG;4CAC9B,aAAa;gDAAE,SAAS;gDAAG,GAAG;4CAAE;4CAChC,YAAY;gDAAE,UAAU;4CAAI;4CAC5B,UAAU;gDAAE,MAAM;4CAAK;4CACvB,WAAU;sDAEV,cAAA,6LAAC;;kEACC,6LAAC;wDAAG,WAAU;kEAAwC;;;;;;kEACtD,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;wEAAI,WAAU;kFACb,cAAA,6LAAC,0NAAA,CAAA,eAAY;4EAAC,WAAU;;;;;;;;;;;kFAE1B,6LAAC;;0FACC,6LAAC;gFAAE,WAAU;0FAAoC;;;;;;0FACjD,6LAAC;gFAAE,WAAU;0FAAwB;;;;;;;;;;;;;;;;;;0EAIzC,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;wEAAI,WAAU;kFACb,cAAA,6LAAC,oNAAA,CAAA,YAAS;4EAAC,WAAU;;;;;;;;;;;kFAEvB,6LAAC;;0FACC,6LAAC;gFAAE,WAAU;0FAAoC;;;;;;0FACjD,6LAAC;gFAAE,WAAU;0FAAwB;;;;;;;;;;;;;;;;;;0EAIzC,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;wEAAI,WAAU;kFACb,cAAA,6LAAC,sNAAA,CAAA,aAAU;4EAAC,WAAU;;;;;;;;;;;kFAExB,6LAAC;;0FACC,6LAAC;gFAAE,WAAU;0FAAoC;;;;;;0FACjD,6LAAC;gFAAE,WAAU;0FAAwB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sDAQ/C,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4CACT,SAAS;gDAAE,SAAS;gDAAG,GAAG;4CAAG;4CAC7B,aAAa;gDAAE,SAAS;gDAAG,GAAG;4CAAE;4CAChC,YAAY;gDAAE,UAAU;4CAAI;4CAC5B,UAAU;gDAAE,MAAM;4CAAK;4CACvB,WAAU;sDAEV,cAAA,6LAAC;gDAAK,WAAU;;kEACd,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;;kFACC,6LAAC;wEAAM,SAAQ;wEAAY,WAAU;kFAA+C;;;;;;kFAGpF,6LAAC;wEACC,MAAK;wEACL,IAAG;wEACH,WAAU;wEACV,aAAY;;;;;;;;;;;;0EAGhB,6LAAC;;kFACC,6LAAC;wEAAM,SAAQ;wEAAW,WAAU;kFAA+C;;;;;;kFAGnF,6LAAC;wEACC,MAAK;wEACL,IAAG;wEACH,WAAU;wEACV,aAAY;;;;;;;;;;;;;;;;;;kEAKlB,6LAAC;;0EACC,6LAAC;gEAAM,SAAQ;gEAAQ,WAAU;0EAA+C;;;;;;0EAGhF,6LAAC;gEACC,MAAK;gEACL,IAAG;gEACH,WAAU;gEACV,aAAY;;;;;;;;;;;;kEAIhB,6LAAC;;0EACC,6LAAC;gEAAM,SAAQ;gEAAU,WAAU;0EAA+C;;;;;;0EAGlF,6LAAC;gEACC,MAAK;gEACL,IAAG;gEACH,WAAU;gEACV,aAAY;;;;;;;;;;;;kEAIhB,6LAAC;;0EACC,6LAAC;gEAAM,SAAQ;gEAAU,WAAU;0EAA+C;;;;;;0EAGlF,6LAAC;gEACC,IAAG;gEACH,MAAM;gEACN,WAAU;gEACV,aAAY;;;;;;;;;;;;kEAIhB,6LAAC;wDAAO,MAAK;wDAAS,WAAU;kEAAqB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAWjE,6LAAC;gBAAO,WAAU;0BAChB,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC;wDAAK,WAAU;kEAA+B;;;;;;;;;;;8DAEjD,6LAAC;oDAAK,WAAU;8DAAoB;;;;;;;;;;;;sDAEtC,6LAAC;4CAAE,WAAU;sDAA8B;;;;;;sDAI3C,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAE,WAAU;8DAAgB;;;;;;8DAC7B,6LAAC;oDAAE,WAAU;8DAAgB;;;;;;8DAC7B,6LAAC;oDAAE,WAAU;8DAAgB;;;;;;;;;;;;;;;;;;8CAIjC,6LAAC;;sDACC,6LAAC;4CAAG,WAAU;sDAA6B;;;;;;sDAC3C,6LAAC;4CAAG,WAAU;;8DACZ,6LAAC;8DAAG,cAAA,6LAAC,+JAAA,CAAA,UAAI;wDAAC,MAAK;wDAAI,WAAU;kEAAqC;;;;;;;;;;;8DAClE,6LAAC;8DAAG,cAAA,6LAAC,+JAAA,CAAA,UAAI;wDAAC,MAAK;wDAAI,WAAU;kEAAqC;;;;;;;;;;;8DAClE,6LAAC;8DAAG,cAAA,6LAAC,+JAAA,CAAA,UAAI;wDAAC,MAAK;wDAAI,WAAU;kEAAqC;;;;;;;;;;;8DAClE,6LAAC;8DAAG,cAAA,6LAAC,+JAAA,CAAA,UAAI;wDAAC,MAAK;wDAAI,WAAU;kEAAqC;;;;;;;;;;;;;;;;;;;;;;;8CAItE,6LAAC;;sDACC,6LAAC;4CAAG,WAAU;sDAA6B;;;;;;sDAC3C,6LAAC;4CAAG,WAAU;;8DACZ,6LAAC;8DAAG,cAAA,6LAAC,+JAAA,CAAA,UAAI;wDAAC,MAAK;wDAAS,WAAU;kEAAqC;;;;;;;;;;;8DACvE,6LAAC;8DAAG,cAAA,6LAAC,+JAAA,CAAA,UAAI;wDAAC,MAAK;wDAAI,WAAU;kEAAqC;;;;;;;;;;;8DAClE,6LAAC;8DAAG,cAAA,6LAAC,+JAAA,CAAA,UAAI;wDAAC,MAAK;wDAAI,WAAU;kEAAqC;;;;;;;;;;;8DAClE,6LAAC;8DAAG,cAAA,6LAAC,+JAAA,CAAA,UAAI;wDAAC,MAAK;wDAAW,WAAU;kEAAqC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAK/E,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAE,WAAU;;oCAAgB;oCACnB,IAAI,OAAO,WAAW;oCAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAO/C;GAzdwB;KAAA", "debugId": null}}]}