import Link from 'next/link';

export function Footer() {
  return (
    <footer className="bg-gray-900 text-white">
      <div className="container py-16">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
          <div className="col-span-1 md:col-span-2">
            <div className="flex items-center space-x-2 mb-6">
              <div className="h-8 w-8 bg-gradient-to-r from-blue-600 to-blue-400 rounded-lg flex items-center justify-center">
                <span className="text-white font-bold text-sm">T</span>
              </div>
              <span className="text-xl font-bold">Technoloway</span>
            </div>
            <p className="text-gray-300 mb-6 max-w-md">
              Transforming ideas into digital reality. We build exceptional software 
              solutions that drive business growth and innovation.
            </p>
            <div className="space-y-2">
              <p className="text-gray-300"><EMAIL></p>
              <p className="text-gray-300">+****************</p>
              <p className="text-gray-300">San Francisco, CA</p>
            </div>
          </div>
          
          <div>
            <h3 className="text-lg font-semibold mb-4">Services</h3>
            <ul className="space-y-2 text-gray-300">
              <li><Link href="/#services" className="hover:text-white transition-colors">Web Development</Link></li>
              <li><Link href="/#services" className="hover:text-white transition-colors">Mobile Apps</Link></li>
              <li><Link href="/#services" className="hover:text-white transition-colors">Cloud Solutions</Link></li>
              <li><Link href="/#services" className="hover:text-white transition-colors">API Development</Link></li>
            </ul>
          </div>
          
          <div>
            <h3 className="text-lg font-semibold mb-4">Company</h3>
            <ul className="space-y-2 text-gray-300">
              <li><Link href="/team" className="hover:text-white transition-colors">About</Link></li>
              <li><Link href="/team" className="hover:text-white transition-colors">Team</Link></li>
              <li><Link href="/portfolio" className="hover:text-white transition-colors">Portfolio</Link></li>
              <li><Link href="/blog" className="hover:text-white transition-colors">Blog</Link></li>
              <li><Link href="/#contact" className="hover:text-white transition-colors">Contact</Link></li>
            </ul>
          </div>
        </div>
        
        <div className="border-t border-gray-700 mt-12 pt-8 text-center">
          <p className="text-gray-400">
            &copy; {new Date().getFullYear()} Technoloway. All rights reserved.
          </p>
        </div>
      </div>
    </footer>
  );
}
