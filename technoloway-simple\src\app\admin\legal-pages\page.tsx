'use client';

import { useState } from 'react';
import { motion } from 'framer-motion';
import {
  PlusIcon,
  PencilIcon,
  TrashIcon,
  EyeIcon,
  DocumentTextIcon,
  CheckCircleIcon,
  XCircleIcon,
  ShieldCheckIcon,
  ScaleIcon,
} from '@heroicons/react/24/outline';

// Mock data - in real app, this would come from API
const legalPages = [
  {
    id: 1,
    title: 'Privacy Policy',
    slug: 'privacy-policy',
    type: 'Privacy',
    content: `<h2>Privacy Policy</h2>
    <p>Last updated: January 15, 2024</p>
    
    <h3>Information We Collect</h3>
    <p>We collect information you provide directly to us, such as when you create an account, make a purchase, or contact us for support.</p>
    
    <h3>How We Use Your Information</h3>
    <p>We use the information we collect to provide, maintain, and improve our services, process transactions, and communicate with you.</p>
    
    <h3>Information Sharing</h3>
    <p>We do not sell, trade, or otherwise transfer your personal information to third parties without your consent, except as described in this policy.</p>
    
    <h3>Data Security</h3>
    <p>We implement appropriate security measures to protect your personal information against unauthorized access, alteration, disclosure, or destruction.</p>`,
    metaTitle: 'Privacy Policy - Technoloway',
    metaDescription: 'Learn how Technoloway collects, uses, and protects your personal information.',
    isPublished: true,
    isRequired: true,
    lastReviewed: '2024-01-15T10:00:00Z',
    nextReview: '2024-07-15T10:00:00Z',
    version: '2.1',
    publishedAt: '2024-01-15T10:00:00Z',
    createdAt: '2024-01-10T09:00:00Z',
    updatedAt: '2024-01-15T10:00:00Z',
    author: 'Legal Team',
  },
  {
    id: 2,
    title: 'Terms of Service',
    slug: 'terms-of-service',
    type: 'Terms',
    content: `<h2>Terms of Service</h2>
    <p>Last updated: January 12, 2024</p>
    
    <h3>Acceptance of Terms</h3>
    <p>By accessing and using our services, you accept and agree to be bound by the terms and provision of this agreement.</p>
    
    <h3>Use License</h3>
    <p>Permission is granted to temporarily download one copy of the materials on our website for personal, non-commercial transitory viewing only.</p>
    
    <h3>Disclaimer</h3>
    <p>The materials on our website are provided on an 'as is' basis. We make no warranties, expressed or implied, and hereby disclaim all other warranties.</p>
    
    <h3>Limitations</h3>
    <p>In no event shall our company or its suppliers be liable for any damages arising out of the use or inability to use the materials on our website.</p>`,
    metaTitle: 'Terms of Service - Technoloway',
    metaDescription: 'Read our terms of service and understand your rights and responsibilities when using our services.',
    isPublished: true,
    isRequired: true,
    lastReviewed: '2024-01-12T11:00:00Z',
    nextReview: '2024-07-12T11:00:00Z',
    version: '1.8',
    publishedAt: '2024-01-12T11:00:00Z',
    createdAt: '2024-01-08T10:00:00Z',
    updatedAt: '2024-01-12T11:00:00Z',
    author: 'Legal Team',
  },
  {
    id: 3,
    title: 'Cookie Policy',
    slug: 'cookie-policy',
    type: 'Privacy',
    content: `<h2>Cookie Policy</h2>
    <p>Last updated: January 10, 2024</p>
    
    <h3>What Are Cookies</h3>
    <p>Cookies are small text files that are placed on your computer or mobile device when you visit our website.</p>
    
    <h3>How We Use Cookies</h3>
    <p>We use cookies to improve your browsing experience, analyze site traffic, and personalize content.</p>
    
    <h3>Types of Cookies We Use</h3>
    <ul>
      <li>Essential cookies: Required for the website to function properly</li>
      <li>Analytics cookies: Help us understand how visitors interact with our website</li>
      <li>Marketing cookies: Used to track visitors across websites</li>
    </ul>
    
    <h3>Managing Cookies</h3>
    <p>You can control and/or delete cookies as you wish through your browser settings.</p>`,
    metaTitle: 'Cookie Policy - Technoloway',
    metaDescription: 'Learn about how we use cookies and how you can manage your cookie preferences.',
    isPublished: true,
    isRequired: false,
    lastReviewed: '2024-01-10T14:00:00Z',
    nextReview: '2024-07-10T14:00:00Z',
    version: '1.3',
    publishedAt: '2024-01-10T14:00:00Z',
    createdAt: '2024-01-05T16:00:00Z',
    updatedAt: '2024-01-10T14:00:00Z',
    author: 'Legal Team',
  },
  {
    id: 4,
    title: 'Data Processing Agreement',
    slug: 'data-processing-agreement',
    type: 'Compliance',
    content: `<h2>Data Processing Agreement</h2>
    <p>Last updated: January 08, 2024</p>
    
    <h3>Purpose and Scope</h3>
    <p>This Data Processing Agreement governs the processing of personal data by Technoloway on behalf of its clients.</p>
    
    <h3>Data Controller and Processor</h3>
    <p>The client acts as the data controller, and Technoloway acts as the data processor for the personal data processed under this agreement.</p>
    
    <h3>Processing Instructions</h3>
    <p>Technoloway will process personal data only on documented instructions from the client, including transfers to third countries.</p>
    
    <h3>Security Measures</h3>
    <p>We implement appropriate technical and organizational measures to ensure a level of security appropriate to the risk.</p>`,
    metaTitle: 'Data Processing Agreement - Technoloway',
    metaDescription: 'Our data processing agreement outlining how we handle personal data on behalf of our clients.',
    isPublished: false,
    isRequired: false,
    lastReviewed: '2024-01-08T12:00:00Z',
    nextReview: '2024-07-08T12:00:00Z',
    version: '1.0',
    publishedAt: null,
    createdAt: '2024-01-03T12:00:00Z',
    updatedAt: '2024-01-08T12:00:00Z',
    author: 'Legal Team',
  },
  {
    id: 5,
    title: 'Acceptable Use Policy',
    slug: 'acceptable-use-policy',
    type: 'Policy',
    content: `<h2>Acceptable Use Policy</h2>
    <p>Last updated: January 05, 2024</p>
    
    <h3>Permitted Uses</h3>
    <p>You may use our services for lawful purposes only and in accordance with these terms.</p>
    
    <h3>Prohibited Uses</h3>
    <p>You may not use our services to transmit, distribute, or store material that is unlawful, harmful, threatening, abusive, or otherwise objectionable.</p>
    
    <h3>Enforcement</h3>
    <p>We reserve the right to investigate and take appropriate action against anyone who violates this policy.</p>
    
    <h3>Reporting Violations</h3>
    <p>If you become aware of any violation of this policy, please report it to us immediately.</p>`,
    metaTitle: 'Acceptable Use Policy - Technoloway',
    metaDescription: 'Guidelines for acceptable use of our services and prohibited activities.',
    isPublished: true,
    isRequired: false,
    lastReviewed: '2024-01-05T15:00:00Z',
    nextReview: '2024-07-05T15:00:00Z',
    version: '1.2',
    publishedAt: '2024-01-05T15:00:00Z',
    createdAt: '2024-01-01T10:00:00Z',
    updatedAt: '2024-01-05T15:00:00Z',
    author: 'Legal Team',
  },
];

const pageTypes = ['All', 'Privacy', 'Terms', 'Compliance', 'Policy'];

export default function LegalPagesPage() {
  const [pages, setPages] = useState(legalPages);
  const [selectedType, setSelectedType] = useState('All');
  const [selectedPage, setSelectedPage] = useState<any>(null);
  const [showAddModal, setShowAddModal] = useState(false);

  const filteredPages = pages.filter(page => 
    selectedType === 'All' || page.type === selectedType
  );

  const handleTogglePublished = (id: number) => {
    setPages(prev => prev.map(page => 
      page.id === id 
        ? { 
            ...page, 
            isPublished: !page.isPublished,
            publishedAt: !page.isPublished ? new Date().toISOString() : null
          }
        : page
    ));
  };

  const handleDelete = (id: number) => {
    const page = pages.find(p => p.id === id);
    if (page?.isRequired) {
      alert('This page is required and cannot be deleted.');
      return;
    }
    if (confirm('Are you sure you want to delete this legal page?')) {
      setPages(prev => prev.filter(page => page.id !== id));
    }
  };

  const formatDate = (dateString: string | null) => {
    if (!dateString) return 'Not published';
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
    });
  };

  const isReviewDue = (nextReviewDate: string) => {
    return new Date(nextReviewDate) <= new Date();
  };

  const stripHtml = (html: string) => {
    return html.replace(/<[^>]*>/g, '').substring(0, 150) + '...';
  };

  return (
    <div className="py-6">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 md:px-8">
        {/* Header */}
        <div className="md:flex md:items-center md:justify-between mb-8">
          <div className="flex-1 min-w-0">
            <h2 className="text-2xl font-bold leading-7 text-gray-900 sm:text-3xl sm:truncate">
              Legal Pages
            </h2>
            <p className="mt-1 text-sm text-gray-500">
              Manage privacy policies, terms of service, and other legal documentation
            </p>
          </div>
          <div className="mt-4 flex md:mt-0 md:ml-4">
            <button
              onClick={() => setShowAddModal(true)}
              className="ml-3 inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
            >
              <PlusIcon className="-ml-1 mr-2 h-5 w-5" />
              Add Legal Page
            </button>
          </div>
        </div>

        {/* Stats */}
        <div className="mb-6 grid grid-cols-1 gap-5 sm:grid-cols-4">
          <div className="bg-white overflow-hidden shadow rounded-lg">
            <div className="p-5">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <div className="w-8 h-8 bg-blue-500 rounded-md flex items-center justify-center">
                    <span className="text-white text-sm font-bold">{pages.length}</span>
                  </div>
                </div>
                <div className="ml-5 w-0 flex-1">
                  <dl>
                    <dt className="text-sm font-medium text-gray-500 truncate">Total Pages</dt>
                    <dd className="text-lg font-medium text-gray-900">Created</dd>
                  </dl>
                </div>
              </div>
            </div>
          </div>

          <div className="bg-white overflow-hidden shadow rounded-lg">
            <div className="p-5">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <div className="w-8 h-8 bg-green-500 rounded-md flex items-center justify-center">
                    <span className="text-white text-sm font-bold">
                      {pages.filter(p => p.isPublished).length}
                    </span>
                  </div>
                </div>
                <div className="ml-5 w-0 flex-1">
                  <dl>
                    <dt className="text-sm font-medium text-gray-500 truncate">Published</dt>
                    <dd className="text-lg font-medium text-gray-900">Live</dd>
                  </dl>
                </div>
              </div>
            </div>
          </div>

          <div className="bg-white overflow-hidden shadow rounded-lg">
            <div className="p-5">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <div className="w-8 h-8 bg-red-500 rounded-md flex items-center justify-center">
                    <span className="text-white text-sm font-bold">
                      {pages.filter(p => isReviewDue(p.nextReview)).length}
                    </span>
                  </div>
                </div>
                <div className="ml-5 w-0 flex-1">
                  <dl>
                    <dt className="text-sm font-medium text-gray-500 truncate">Review Due</dt>
                    <dd className="text-lg font-medium text-gray-900">Needs Update</dd>
                  </dl>
                </div>
              </div>
            </div>
          </div>

          <div className="bg-white overflow-hidden shadow rounded-lg">
            <div className="p-5">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <div className="w-8 h-8 bg-yellow-500 rounded-md flex items-center justify-center">
                    <span className="text-white text-sm font-bold">
                      {pages.filter(p => p.isRequired).length}
                    </span>
                  </div>
                </div>
                <div className="ml-5 w-0 flex-1">
                  <dl>
                    <dt className="text-sm font-medium text-gray-500 truncate">Required</dt>
                    <dd className="text-lg font-medium text-gray-900">Essential</dd>
                  </dl>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Type Filter */}
        <div className="mb-6 bg-white shadow rounded-lg">
          <div className="px-4 py-5 sm:p-6">
            <div className="flex flex-wrap gap-2">
              {pageTypes.map(type => (
                <button
                  key={type}
                  onClick={() => setSelectedType(type)}
                  className={`px-4 py-2 rounded-full text-sm font-medium transition-colors ${
                    selectedType === type
                      ? 'bg-blue-600 text-white'
                      : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                  }`}
                >
                  {type}
                </button>
              ))}
            </div>
          </div>
        </div>

        {/* Legal Pages List */}
        <div className="bg-white shadow overflow-hidden sm:rounded-md">
          <ul className="divide-y divide-gray-200">
            {filteredPages.map((page, index) => (
              <motion.li
                key={page.id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.3, delay: index * 0.1 }}
              >
                <div className="px-4 py-4 sm:px-6">
                  <div className="flex items-center justify-between">
                    <div className="flex items-start space-x-4 flex-1">
                      <div className="flex-shrink-0">
                        <div className={`w-10 h-10 rounded-lg flex items-center justify-center ${
                          page.isPublished ? 'bg-green-100' : 'bg-gray-100'
                        }`}>
                          {page.type === 'Privacy' ? (
                            <ShieldCheckIcon className={`w-6 h-6 ${page.isPublished ? 'text-green-600' : 'text-gray-400'}`} />
                          ) : (
                            <ScaleIcon className={`w-6 h-6 ${page.isPublished ? 'text-green-600' : 'text-gray-400'}`} />
                          )}
                        </div>
                      </div>
                      
                      <div className="flex-1 min-w-0">
                        <div className="flex items-center space-x-2 mb-1">
                          <h3 className="text-lg font-medium text-gray-900 truncate">
                            {page.title}
                          </h3>
                          <div className="flex items-center space-x-1">
                            <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                              page.isPublished 
                                ? 'bg-green-100 text-green-800'
                                : 'bg-gray-100 text-gray-800'
                            }`}>
                              {page.isPublished ? 'Published' : 'Draft'}
                            </span>
                            {page.isRequired && (
                              <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                                Required
                              </span>
                            )}
                            {isReviewDue(page.nextReview) && (
                              <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                                Review Due
                              </span>
                            )}
                          </div>
                        </div>
                        
                        <div className="flex items-center space-x-4 text-sm text-gray-500 mb-2">
                          <span>Type: {page.type}</span>
                          <span>Version: {page.version}</span>
                          <span>URL: /{page.slug}</span>
                        </div>
                        
                        <p className="text-sm text-gray-600 mb-3">
                          {stripHtml(page.content)}
                        </p>
                        
                        <div className="flex items-center space-x-4 text-sm text-gray-500">
                          <span>Last Reviewed: {formatDate(page.lastReviewed)}</span>
                          <span>Next Review: {formatDate(page.nextReview)}</span>
                          <span>Updated: {formatDate(page.updatedAt)}</span>
                        </div>
                      </div>
                    </div>
                    
                    <div className="flex items-center space-x-2 ml-4">
                      <button
                        onClick={() => setSelectedPage(page)}
                        className="text-gray-400 hover:text-blue-600 transition-colors"
                        title="View"
                      >
                        <EyeIcon className="h-5 w-5" />
                      </button>
                      <button
                        className="text-gray-400 hover:text-blue-600 transition-colors"
                        title="Edit"
                      >
                        <PencilIcon className="h-5 w-5" />
                      </button>
                      <button
                        onClick={() => handleTogglePublished(page.id)}
                        className={`text-gray-400 hover:text-green-600 transition-colors ${
                          page.isPublished ? 'text-green-600' : ''
                        }`}
                        title={page.isPublished ? 'Unpublish' : 'Publish'}
                      >
                        <CheckCircleIcon className="h-5 w-5" />
                      </button>
                      <button
                        onClick={() => handleDelete(page.id)}
                        className={`text-gray-400 transition-colors ${
                          page.isRequired 
                            ? 'cursor-not-allowed opacity-50' 
                            : 'hover:text-red-600'
                        }`}
                        title={page.isRequired ? 'Required page cannot be deleted' : 'Delete'}
                        disabled={page.isRequired}
                      >
                        <TrashIcon className="h-5 w-5" />
                      </button>
                    </div>
                  </div>
                </div>
              </motion.li>
            ))}
          </ul>
        </div>

        {/* Empty State */}
        {filteredPages.length === 0 && (
          <div className="bg-white shadow rounded-lg">
            <div className="text-center py-12">
              <DocumentTextIcon className="mx-auto h-12 w-12 text-gray-400" />
              <h3 className="mt-2 text-sm font-medium text-gray-900">No legal pages found</h3>
              <p className="mt-1 text-sm text-gray-500">
                {selectedType === 'All' 
                  ? 'Get started by creating your first legal page.'
                  : `No legal pages found in the ${selectedType} category.`
                }
              </p>
            </div>
          </div>
        )}

        {/* Page Preview Modal */}
        {selectedPage && (
          <div className="fixed inset-0 z-50 overflow-y-auto">
            <div className="flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
              <div className="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" onClick={() => setSelectedPage(null)} />
              
              <div className="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-4xl sm:w-full">
                <div className="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
                  <div className="sm:flex sm:items-start">
                    <div className="mt-3 text-center sm:mt-0 sm:text-left w-full">
                      <h3 className="text-lg leading-6 font-medium text-gray-900 mb-4">
                        {selectedPage.title}
                      </h3>
                      
                      <div className="mb-4 p-4 bg-gray-50 rounded-lg">
                        <div className="grid grid-cols-2 gap-4 text-sm">
                          <div>
                            <strong>Type:</strong> {selectedPage.type}
                          </div>
                          <div>
                            <strong>Version:</strong> {selectedPage.version}
                          </div>
                          <div>
                            <strong>URL:</strong> /{selectedPage.slug}
                          </div>
                          <div>
                            <strong>Status:</strong> {selectedPage.isPublished ? 'Published' : 'Draft'}
                          </div>
                          <div>
                            <strong>Last Reviewed:</strong> {formatDate(selectedPage.lastReviewed)}
                          </div>
                          <div>
                            <strong>Next Review:</strong> {formatDate(selectedPage.nextReview)}
                          </div>
                        </div>
                      </div>
                      
                      <div className="prose max-w-none">
                        <div dangerouslySetInnerHTML={{ __html: selectedPage.content }} />
                      </div>
                    </div>
                  </div>
                </div>
                <div className="bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
                  <button
                    type="button"
                    className="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-blue-600 text-base font-medium text-white hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 sm:ml-3 sm:w-auto sm:text-sm"
                  >
                    Edit Page
                  </button>
                  <button
                    type="button"
                    className="mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 sm:mt-0 sm:w-auto sm:text-sm"
                    onClick={() => setSelectedPage(null)}
                  >
                    Close
                  </button>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
