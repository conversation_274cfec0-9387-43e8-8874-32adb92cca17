import{validateFieldsNatively as r,toNestErrors as e}from"@hookform/resolvers";import o from"ajv";import a from"ajv-errors";import{appendErrors as s}from"react-hook-form";var t=function(r,e){for(var o={},a=function(r){"required"===r.keyword&&(r.instancePath+="/"+r.params.missingProperty);var a=r.instancePath.substring(1).replace(/\//g,".");if(o[a]||(o[a]={message:r.message,type:r.keyword}),e){var t=o[a].types,i=t&&t[r.keyword];o[a]=s(a,e,o,r.keyword,i?[].concat(i,r.message||""):r.message)}},t=function(){var e=r[i];"errorMessage"===e.keyword?e.params.errors.forEach(function(r){r.message=e.message,a(r)}):a(e)},i=0;i<r.length;i+=1)t();return o},i=function(s,i,n){return void 0===n&&(n={}),function(m,c,v){try{var l=new o(Object.assign({},{allErrors:!0,validateSchema:!0},i));a(l);var u=l.compile(Object.assign({$async:n&&"async"===n.mode},s)),f=u(m);return v.shouldUseNativeValidation&&r({},v),Promise.resolve(f?{values:m,errors:{}}:{values:{},errors:e(t(u.errors,!v.shouldUseNativeValidation&&"all"===v.criteriaMode),v)})}catch(r){return Promise.reject(r)}}};export{i as ajvResolver};
//# sourceMappingURL=ajv.module.js.map
