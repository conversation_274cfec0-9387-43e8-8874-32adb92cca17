{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Project%20By%20AI/Technoloway/Technoloway%20%28Processing%29/technoloway-simple/src/app/admin/settings/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport { motion } from 'framer-motion';\nimport {\n  CogIcon,\n  BellIcon,\n  ShieldCheckIcon,\n  UserIcon,\n  GlobeAltIcon,\n  EnvelopeIcon,\n  KeyIcon,\n  PaintBrushIcon,\n  CloudIcon,\n  DocumentTextIcon,\n} from '@heroicons/react/24/outline';\n\nconst settingsSections = [\n  {\n    id: 'general',\n    name: 'General',\n    icon: CogIcon,\n    description: 'Basic site settings and configuration',\n  },\n  {\n    id: 'profile',\n    name: 'Profile',\n    icon: UserIcon,\n    description: 'Your personal account settings',\n  },\n  {\n    id: 'notifications',\n    name: 'Notifications',\n    icon: BellIcon,\n    description: 'Email and push notification preferences',\n  },\n  {\n    id: 'security',\n    name: 'Security',\n    icon: ShieldCheckIcon,\n    description: 'Password and security settings',\n  },\n  {\n    id: 'appearance',\n    name: 'Appearance',\n    icon: PaintBrushIcon,\n    description: 'Theme and display preferences',\n  },\n  {\n    id: 'integrations',\n    name: 'Integrations',\n    icon: CloudIcon,\n    description: 'Third-party services and APIs',\n  },\n];\n\nexport default function SettingsPage() {\n  const [activeSection, setActiveSection] = useState('general');\n  const [settings, setSettings] = useState({\n    // General Settings\n    siteName: 'Technoloway',\n    siteDescription: 'Leading software development company',\n    siteUrl: 'https://technoloway.com',\n    contactEmail: '<EMAIL>',\n    \n    // Profile Settings\n    firstName: 'Admin',\n    lastName: 'User',\n    email: '<EMAIL>',\n    phone: '+****************',\n    timezone: 'America/New_York',\n    \n    // Notification Settings\n    emailNotifications: true,\n    pushNotifications: true,\n    marketingEmails: false,\n    weeklyReports: true,\n    \n    // Security Settings\n    twoFactorAuth: false,\n    sessionTimeout: 30,\n    \n    // Appearance Settings\n    theme: 'light',\n    language: 'en',\n    \n    // Integration Settings\n    googleAnalytics: '',\n    mailchimpApi: '',\n    slackWebhook: '',\n  });\n\n  const handleSettingChange = (key: string, value: any) => {\n    setSettings(prev => ({\n      ...prev,\n      [key]: value\n    }));\n  };\n\n  const renderGeneralSettings = () => (\n    <div className=\"space-y-6\">\n      <div>\n        <label className=\"block text-sm font-medium text-gray-700\">Site Name</label>\n        <input\n          type=\"text\"\n          value={settings.siteName}\n          onChange={(e) => handleSettingChange('siteName', e.target.value)}\n          className=\"mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm\"\n        />\n      </div>\n      \n      <div>\n        <label className=\"block text-sm font-medium text-gray-700\">Site Description</label>\n        <textarea\n          value={settings.siteDescription}\n          onChange={(e) => handleSettingChange('siteDescription', e.target.value)}\n          rows={3}\n          className=\"mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm\"\n        />\n      </div>\n      \n      <div>\n        <label className=\"block text-sm font-medium text-gray-700\">Site URL</label>\n        <input\n          type=\"url\"\n          value={settings.siteUrl}\n          onChange={(e) => handleSettingChange('siteUrl', e.target.value)}\n          className=\"mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm\"\n        />\n      </div>\n      \n      <div>\n        <label className=\"block text-sm font-medium text-gray-700\">Contact Email</label>\n        <input\n          type=\"email\"\n          value={settings.contactEmail}\n          onChange={(e) => handleSettingChange('contactEmail', e.target.value)}\n          className=\"mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm\"\n        />\n      </div>\n    </div>\n  );\n\n  const renderProfileSettings = () => (\n    <div className=\"space-y-6\">\n      <div className=\"grid grid-cols-1 gap-6 sm:grid-cols-2\">\n        <div>\n          <label className=\"block text-sm font-medium text-gray-700\">First Name</label>\n          <input\n            type=\"text\"\n            value={settings.firstName}\n            onChange={(e) => handleSettingChange('firstName', e.target.value)}\n            className=\"mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm\"\n          />\n        </div>\n        \n        <div>\n          <label className=\"block text-sm font-medium text-gray-700\">Last Name</label>\n          <input\n            type=\"text\"\n            value={settings.lastName}\n            onChange={(e) => handleSettingChange('lastName', e.target.value)}\n            className=\"mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm\"\n          />\n        </div>\n      </div>\n      \n      <div>\n        <label className=\"block text-sm font-medium text-gray-700\">Email</label>\n        <input\n          type=\"email\"\n          value={settings.email}\n          onChange={(e) => handleSettingChange('email', e.target.value)}\n          className=\"mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm\"\n        />\n      </div>\n      \n      <div>\n        <label className=\"block text-sm font-medium text-gray-700\">Phone</label>\n        <input\n          type=\"tel\"\n          value={settings.phone}\n          onChange={(e) => handleSettingChange('phone', e.target.value)}\n          className=\"mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm\"\n        />\n      </div>\n      \n      <div>\n        <label className=\"block text-sm font-medium text-gray-700\">Timezone</label>\n        <select\n          value={settings.timezone}\n          onChange={(e) => handleSettingChange('timezone', e.target.value)}\n          className=\"mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm\"\n        >\n          <option value=\"America/New_York\">Eastern Time</option>\n          <option value=\"America/Chicago\">Central Time</option>\n          <option value=\"America/Denver\">Mountain Time</option>\n          <option value=\"America/Los_Angeles\">Pacific Time</option>\n        </select>\n      </div>\n    </div>\n  );\n\n  const renderNotificationSettings = () => (\n    <div className=\"space-y-6\">\n      <div className=\"space-y-4\">\n        <div className=\"flex items-center justify-between\">\n          <div>\n            <h4 className=\"text-sm font-medium text-gray-900\">Email Notifications</h4>\n            <p className=\"text-sm text-gray-500\">Receive notifications via email</p>\n          </div>\n          <button\n            type=\"button\"\n            onClick={() => handleSettingChange('emailNotifications', !settings.emailNotifications)}\n            className={`${\n              settings.emailNotifications ? 'bg-blue-600' : 'bg-gray-200'\n            } relative inline-flex h-6 w-11 flex-shrink-0 cursor-pointer rounded-full border-2 border-transparent transition-colors duration-200 ease-in-out focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2`}\n          >\n            <span\n              className={`${\n                settings.emailNotifications ? 'translate-x-5' : 'translate-x-0'\n              } pointer-events-none inline-block h-5 w-5 transform rounded-full bg-white shadow ring-0 transition duration-200 ease-in-out`}\n            />\n          </button>\n        </div>\n        \n        <div className=\"flex items-center justify-between\">\n          <div>\n            <h4 className=\"text-sm font-medium text-gray-900\">Push Notifications</h4>\n            <p className=\"text-sm text-gray-500\">Receive push notifications in browser</p>\n          </div>\n          <button\n            type=\"button\"\n            onClick={() => handleSettingChange('pushNotifications', !settings.pushNotifications)}\n            className={`${\n              settings.pushNotifications ? 'bg-blue-600' : 'bg-gray-200'\n            } relative inline-flex h-6 w-11 flex-shrink-0 cursor-pointer rounded-full border-2 border-transparent transition-colors duration-200 ease-in-out focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2`}\n          >\n            <span\n              className={`${\n                settings.pushNotifications ? 'translate-x-5' : 'translate-x-0'\n              } pointer-events-none inline-block h-5 w-5 transform rounded-full bg-white shadow ring-0 transition duration-200 ease-in-out`}\n            />\n          </button>\n        </div>\n        \n        <div className=\"flex items-center justify-between\">\n          <div>\n            <h4 className=\"text-sm font-medium text-gray-900\">Marketing Emails</h4>\n            <p className=\"text-sm text-gray-500\">Receive marketing and promotional emails</p>\n          </div>\n          <button\n            type=\"button\"\n            onClick={() => handleSettingChange('marketingEmails', !settings.marketingEmails)}\n            className={`${\n              settings.marketingEmails ? 'bg-blue-600' : 'bg-gray-200'\n            } relative inline-flex h-6 w-11 flex-shrink-0 cursor-pointer rounded-full border-2 border-transparent transition-colors duration-200 ease-in-out focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2`}\n          >\n            <span\n              className={`${\n                settings.marketingEmails ? 'translate-x-5' : 'translate-x-0'\n              } pointer-events-none inline-block h-5 w-5 transform rounded-full bg-white shadow ring-0 transition duration-200 ease-in-out`}\n            />\n          </button>\n        </div>\n        \n        <div className=\"flex items-center justify-between\">\n          <div>\n            <h4 className=\"text-sm font-medium text-gray-900\">Weekly Reports</h4>\n            <p className=\"text-sm text-gray-500\">Receive weekly analytics reports</p>\n          </div>\n          <button\n            type=\"button\"\n            onClick={() => handleSettingChange('weeklyReports', !settings.weeklyReports)}\n            className={`${\n              settings.weeklyReports ? 'bg-blue-600' : 'bg-gray-200'\n            } relative inline-flex h-6 w-11 flex-shrink-0 cursor-pointer rounded-full border-2 border-transparent transition-colors duration-200 ease-in-out focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2`}\n          >\n            <span\n              className={`${\n                settings.weeklyReports ? 'translate-x-5' : 'translate-x-0'\n              } pointer-events-none inline-block h-5 w-5 transform rounded-full bg-white shadow ring-0 transition duration-200 ease-in-out`}\n            />\n          </button>\n        </div>\n      </div>\n    </div>\n  );\n\n  const renderSecuritySettings = () => (\n    <div className=\"space-y-6\">\n      <div className=\"flex items-center justify-between\">\n        <div>\n          <h4 className=\"text-sm font-medium text-gray-900\">Two-Factor Authentication</h4>\n          <p className=\"text-sm text-gray-500\">Add an extra layer of security to your account</p>\n        </div>\n        <button\n          type=\"button\"\n          onClick={() => handleSettingChange('twoFactorAuth', !settings.twoFactorAuth)}\n          className={`${\n            settings.twoFactorAuth ? 'bg-blue-600' : 'bg-gray-200'\n          } relative inline-flex h-6 w-11 flex-shrink-0 cursor-pointer rounded-full border-2 border-transparent transition-colors duration-200 ease-in-out focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2`}\n        >\n          <span\n            className={`${\n              settings.twoFactorAuth ? 'translate-x-5' : 'translate-x-0'\n            } pointer-events-none inline-block h-5 w-5 transform rounded-full bg-white shadow ring-0 transition duration-200 ease-in-out`}\n          />\n        </button>\n      </div>\n      \n      <div>\n        <label className=\"block text-sm font-medium text-gray-700\">Session Timeout (minutes)</label>\n        <select\n          value={settings.sessionTimeout}\n          onChange={(e) => handleSettingChange('sessionTimeout', parseInt(e.target.value))}\n          className=\"mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm\"\n        >\n          <option value={15}>15 minutes</option>\n          <option value={30}>30 minutes</option>\n          <option value={60}>1 hour</option>\n          <option value={120}>2 hours</option>\n          <option value={480}>8 hours</option>\n        </select>\n      </div>\n      \n      <div className=\"space-y-4\">\n        <button className=\"w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500\">\n          Change Password\n        </button>\n        \n        <button className=\"w-full flex justify-center py-2 px-4 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500\">\n          Download Account Data\n        </button>\n      </div>\n    </div>\n  );\n\n  const renderContent = () => {\n    switch (activeSection) {\n      case 'general':\n        return renderGeneralSettings();\n      case 'profile':\n        return renderProfileSettings();\n      case 'notifications':\n        return renderNotificationSettings();\n      case 'security':\n        return renderSecuritySettings();\n      default:\n        return <div>Settings section not implemented yet.</div>;\n    }\n  };\n\n  return (\n    <div className=\"py-6\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 md:px-8\">\n        {/* Header */}\n        <div className=\"mb-8\">\n          <h2 className=\"text-2xl font-bold leading-7 text-gray-900 sm:text-3xl sm:truncate\">\n            Settings\n          </h2>\n          <p className=\"mt-1 text-sm text-gray-500\">\n            Manage your account settings and preferences\n          </p>\n        </div>\n\n        <div className=\"lg:grid lg:grid-cols-12 lg:gap-x-5\">\n          {/* Settings Navigation */}\n          <aside className=\"py-6 px-2 sm:px-6 lg:py-0 lg:px-0 lg:col-span-3\">\n            <nav className=\"space-y-1\">\n              {settingsSections.map((section) => {\n                const Icon = section.icon;\n                return (\n                  <button\n                    key={section.id}\n                    onClick={() => setActiveSection(section.id)}\n                    className={`${\n                      activeSection === section.id\n                        ? 'bg-blue-50 border-blue-500 text-blue-700'\n                        : 'border-transparent text-gray-900 hover:bg-gray-50 hover:text-gray-900'\n                    } group border-l-4 px-3 py-2 flex items-center text-sm font-medium w-full text-left`}\n                  >\n                    <Icon\n                      className={`${\n                        activeSection === section.id ? 'text-blue-500' : 'text-gray-400 group-hover:text-gray-500'\n                      } flex-shrink-0 -ml-1 mr-3 h-6 w-6`}\n                    />\n                    <span className=\"truncate\">{section.name}</span>\n                  </button>\n                );\n              })}\n            </nav>\n          </aside>\n\n          {/* Settings Content */}\n          <div className=\"space-y-6 sm:px-6 lg:px-0 lg:col-span-9\">\n            <motion.div\n              key={activeSection}\n              initial={{ opacity: 0, x: 20 }}\n              animate={{ opacity: 1, x: 0 }}\n              transition={{ duration: 0.3 }}\n              className=\"bg-white shadow rounded-lg\"\n            >\n              <div className=\"px-4 py-5 sm:p-6\">\n                <div className=\"mb-6\">\n                  <h3 className=\"text-lg leading-6 font-medium text-gray-900\">\n                    {settingsSections.find(s => s.id === activeSection)?.name}\n                  </h3>\n                  <p className=\"mt-1 text-sm text-gray-500\">\n                    {settingsSections.find(s => s.id === activeSection)?.description}\n                  </p>\n                </div>\n                \n                {renderContent()}\n                \n                <div className=\"mt-6 flex justify-end\">\n                  <button className=\"ml-3 inline-flex justify-center py-2 px-4 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500\">\n                    Save Changes\n                  </button>\n                </div>\n              </div>\n            </motion.div>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;;;AAJA;;;;AAiBA,MAAM,mBAAmB;IACvB;QACE,IAAI;QACJ,MAAM;QACN,MAAM,gNAAA,CAAA,UAAO;QACb,aAAa;IACf;IACA;QACE,IAAI;QACJ,MAAM;QACN,MAAM,kNAAA,CAAA,WAAQ;QACd,aAAa;IACf;IACA;QACE,IAAI;QACJ,MAAM;QACN,MAAM,kNAAA,CAAA,WAAQ;QACd,aAAa;IACf;IACA;QACE,IAAI;QACJ,MAAM;QACN,MAAM,gOAAA,CAAA,kBAAe;QACrB,aAAa;IACf;IACA;QACE,IAAI;QACJ,MAAM;QACN,MAAM,8NAAA,CAAA,iBAAc;QACpB,aAAa;IACf;IACA;QACE,IAAI;QACJ,MAAM;QACN,MAAM,oNAAA,CAAA,YAAS;QACf,aAAa;IACf;CACD;AAEc,SAAS;;IACtB,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACnD,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;QACvC,mBAAmB;QACnB,UAAU;QACV,iBAAiB;QACjB,SAAS;QACT,cAAc;QAEd,mBAAmB;QACnB,WAAW;QACX,UAAU;QACV,OAAO;QACP,OAAO;QACP,UAAU;QAEV,wBAAwB;QACxB,oBAAoB;QACpB,mBAAmB;QACnB,iBAAiB;QACjB,eAAe;QAEf,oBAAoB;QACpB,eAAe;QACf,gBAAgB;QAEhB,sBAAsB;QACtB,OAAO;QACP,UAAU;QAEV,uBAAuB;QACvB,iBAAiB;QACjB,cAAc;QACd,cAAc;IAChB;IAEA,MAAM,sBAAsB,CAAC,KAAa;QACxC,YAAY,CAAA,OAAQ,CAAC;gBACnB,GAAG,IAAI;gBACP,CAAC,IAAI,EAAE;YACT,CAAC;IACH;IAEA,MAAM,wBAAwB,kBAC5B,6LAAC;YAAI,WAAU;;8BACb,6LAAC;;sCACC,6LAAC;4BAAM,WAAU;sCAA0C;;;;;;sCAC3D,6LAAC;4BACC,MAAK;4BACL,OAAO,SAAS,QAAQ;4BACxB,UAAU,CAAC,IAAM,oBAAoB,YAAY,EAAE,MAAM,CAAC,KAAK;4BAC/D,WAAU;;;;;;;;;;;;8BAId,6LAAC;;sCACC,6LAAC;4BAAM,WAAU;sCAA0C;;;;;;sCAC3D,6LAAC;4BACC,OAAO,SAAS,eAAe;4BAC/B,UAAU,CAAC,IAAM,oBAAoB,mBAAmB,EAAE,MAAM,CAAC,KAAK;4BACtE,MAAM;4BACN,WAAU;;;;;;;;;;;;8BAId,6LAAC;;sCACC,6LAAC;4BAAM,WAAU;sCAA0C;;;;;;sCAC3D,6LAAC;4BACC,MAAK;4BACL,OAAO,SAAS,OAAO;4BACvB,UAAU,CAAC,IAAM,oBAAoB,WAAW,EAAE,MAAM,CAAC,KAAK;4BAC9D,WAAU;;;;;;;;;;;;8BAId,6LAAC;;sCACC,6LAAC;4BAAM,WAAU;sCAA0C;;;;;;sCAC3D,6LAAC;4BACC,MAAK;4BACL,OAAO,SAAS,YAAY;4BAC5B,UAAU,CAAC,IAAM,oBAAoB,gBAAgB,EAAE,MAAM,CAAC,KAAK;4BACnE,WAAU;;;;;;;;;;;;;;;;;;IAMlB,MAAM,wBAAwB,kBAC5B,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;;8CACC,6LAAC;oCAAM,WAAU;8CAA0C;;;;;;8CAC3D,6LAAC;oCACC,MAAK;oCACL,OAAO,SAAS,SAAS;oCACzB,UAAU,CAAC,IAAM,oBAAoB,aAAa,EAAE,MAAM,CAAC,KAAK;oCAChE,WAAU;;;;;;;;;;;;sCAId,6LAAC;;8CACC,6LAAC;oCAAM,WAAU;8CAA0C;;;;;;8CAC3D,6LAAC;oCACC,MAAK;oCACL,OAAO,SAAS,QAAQ;oCACxB,UAAU,CAAC,IAAM,oBAAoB,YAAY,EAAE,MAAM,CAAC,KAAK;oCAC/D,WAAU;;;;;;;;;;;;;;;;;;8BAKhB,6LAAC;;sCACC,6LAAC;4BAAM,WAAU;sCAA0C;;;;;;sCAC3D,6LAAC;4BACC,MAAK;4BACL,OAAO,SAAS,KAAK;4BACrB,UAAU,CAAC,IAAM,oBAAoB,SAAS,EAAE,MAAM,CAAC,KAAK;4BAC5D,WAAU;;;;;;;;;;;;8BAId,6LAAC;;sCACC,6LAAC;4BAAM,WAAU;sCAA0C;;;;;;sCAC3D,6LAAC;4BACC,MAAK;4BACL,OAAO,SAAS,KAAK;4BACrB,UAAU,CAAC,IAAM,oBAAoB,SAAS,EAAE,MAAM,CAAC,KAAK;4BAC5D,WAAU;;;;;;;;;;;;8BAId,6LAAC;;sCACC,6LAAC;4BAAM,WAAU;sCAA0C;;;;;;sCAC3D,6LAAC;4BACC,OAAO,SAAS,QAAQ;4BACxB,UAAU,CAAC,IAAM,oBAAoB,YAAY,EAAE,MAAM,CAAC,KAAK;4BAC/D,WAAU;;8CAEV,6LAAC;oCAAO,OAAM;8CAAmB;;;;;;8CACjC,6LAAC;oCAAO,OAAM;8CAAkB;;;;;;8CAChC,6LAAC;oCAAO,OAAM;8CAAiB;;;;;;8CAC/B,6LAAC;oCAAO,OAAM;8CAAsB;;;;;;;;;;;;;;;;;;;;;;;;IAM5C,MAAM,6BAA6B,kBACjC,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;;kDACC,6LAAC;wCAAG,WAAU;kDAAoC;;;;;;kDAClD,6LAAC;wCAAE,WAAU;kDAAwB;;;;;;;;;;;;0CAEvC,6LAAC;gCACC,MAAK;gCACL,SAAS,IAAM,oBAAoB,sBAAsB,CAAC,SAAS,kBAAkB;gCACrF,WAAW,GACT,SAAS,kBAAkB,GAAG,gBAAgB,cAC/C,uNAAuN,CAAC;0CAEzN,cAAA,6LAAC;oCACC,WAAW,GACT,SAAS,kBAAkB,GAAG,kBAAkB,gBACjD,2HAA2H,CAAC;;;;;;;;;;;;;;;;;kCAKnI,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;;kDACC,6LAAC;wCAAG,WAAU;kDAAoC;;;;;;kDAClD,6LAAC;wCAAE,WAAU;kDAAwB;;;;;;;;;;;;0CAEvC,6LAAC;gCACC,MAAK;gCACL,SAAS,IAAM,oBAAoB,qBAAqB,CAAC,SAAS,iBAAiB;gCACnF,WAAW,GACT,SAAS,iBAAiB,GAAG,gBAAgB,cAC9C,uNAAuN,CAAC;0CAEzN,cAAA,6LAAC;oCACC,WAAW,GACT,SAAS,iBAAiB,GAAG,kBAAkB,gBAChD,2HAA2H,CAAC;;;;;;;;;;;;;;;;;kCAKnI,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;;kDACC,6LAAC;wCAAG,WAAU;kDAAoC;;;;;;kDAClD,6LAAC;wCAAE,WAAU;kDAAwB;;;;;;;;;;;;0CAEvC,6LAAC;gCACC,MAAK;gCACL,SAAS,IAAM,oBAAoB,mBAAmB,CAAC,SAAS,eAAe;gCAC/E,WAAW,GACT,SAAS,eAAe,GAAG,gBAAgB,cAC5C,uNAAuN,CAAC;0CAEzN,cAAA,6LAAC;oCACC,WAAW,GACT,SAAS,eAAe,GAAG,kBAAkB,gBAC9C,2HAA2H,CAAC;;;;;;;;;;;;;;;;;kCAKnI,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;;kDACC,6LAAC;wCAAG,WAAU;kDAAoC;;;;;;kDAClD,6LAAC;wCAAE,WAAU;kDAAwB;;;;;;;;;;;;0CAEvC,6LAAC;gCACC,MAAK;gCACL,SAAS,IAAM,oBAAoB,iBAAiB,CAAC,SAAS,aAAa;gCAC3E,WAAW,GACT,SAAS,aAAa,GAAG,gBAAgB,cAC1C,uNAAuN,CAAC;0CAEzN,cAAA,6LAAC;oCACC,WAAW,GACT,SAAS,aAAa,GAAG,kBAAkB,gBAC5C,2HAA2H,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAQzI,MAAM,yBAAyB,kBAC7B,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;;8CACC,6LAAC;oCAAG,WAAU;8CAAoC;;;;;;8CAClD,6LAAC;oCAAE,WAAU;8CAAwB;;;;;;;;;;;;sCAEvC,6LAAC;4BACC,MAAK;4BACL,SAAS,IAAM,oBAAoB,iBAAiB,CAAC,SAAS,aAAa;4BAC3E,WAAW,GACT,SAAS,aAAa,GAAG,gBAAgB,cAC1C,uNAAuN,CAAC;sCAEzN,cAAA,6LAAC;gCACC,WAAW,GACT,SAAS,aAAa,GAAG,kBAAkB,gBAC5C,2HAA2H,CAAC;;;;;;;;;;;;;;;;;8BAKnI,6LAAC;;sCACC,6LAAC;4BAAM,WAAU;sCAA0C;;;;;;sCAC3D,6LAAC;4BACC,OAAO,SAAS,cAAc;4BAC9B,UAAU,CAAC,IAAM,oBAAoB,kBAAkB,SAAS,EAAE,MAAM,CAAC,KAAK;4BAC9E,WAAU;;8CAEV,6LAAC;oCAAO,OAAO;8CAAI;;;;;;8CACnB,6LAAC;oCAAO,OAAO;8CAAI;;;;;;8CACnB,6LAAC;oCAAO,OAAO;8CAAI;;;;;;8CACnB,6LAAC;oCAAO,OAAO;8CAAK;;;;;;8CACpB,6LAAC;oCAAO,OAAO;8CAAK;;;;;;;;;;;;;;;;;;8BAIxB,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAO,WAAU;sCAA2N;;;;;;sCAI7O,6LAAC;4BAAO,WAAU;sCAAuN;;;;;;;;;;;;;;;;;;IAO/O,MAAM,gBAAgB;QACpB,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,qBAAO,6LAAC;8BAAI;;;;;;QAChB;IACF;IAEA,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAI,WAAU;;8BAEb,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;sCAAqE;;;;;;sCAGnF,6LAAC;4BAAE,WAAU;sCAA6B;;;;;;;;;;;;8BAK5C,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC;4BAAM,WAAU;sCACf,cAAA,6LAAC;gCAAI,WAAU;0CACZ,iBAAiB,GAAG,CAAC,CAAC;oCACrB,MAAM,OAAO,QAAQ,IAAI;oCACzB,qBACE,6LAAC;wCAEC,SAAS,IAAM,iBAAiB,QAAQ,EAAE;wCAC1C,WAAW,GACT,kBAAkB,QAAQ,EAAE,GACxB,6CACA,wEACL,kFAAkF,CAAC;;0DAEpF,6LAAC;gDACC,WAAW,GACT,kBAAkB,QAAQ,EAAE,GAAG,kBAAkB,0CAClD,iCAAiC,CAAC;;;;;;0DAErC,6LAAC;gDAAK,WAAU;0DAAY,QAAQ,IAAI;;;;;;;uCAbnC,QAAQ,EAAE;;;;;gCAgBrB;;;;;;;;;;;sCAKJ,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gCAET,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAG;gCAC7B,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAE;gCAC5B,YAAY;oCAAE,UAAU;gCAAI;gCAC5B,WAAU;0CAEV,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAG,WAAU;8DACX,iBAAiB,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK,gBAAgB;;;;;;8DAEvD,6LAAC;oDAAE,WAAU;8DACV,iBAAiB,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK,gBAAgB;;;;;;;;;;;;wCAIxD;sDAED,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDAAO,WAAU;0DAAgO;;;;;;;;;;;;;;;;;+BAnBjP;;;;;;;;;;;;;;;;;;;;;;;;;;;AA8BnB;GAnXwB;KAAA", "debugId": null}}, {"offset": {"line": 957, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Project%20By%20AI/Technoloway/Technoloway%20%28Processing%29/technoloway-simple/node_modules/%40heroicons/react/24/outline/esm/ShieldCheckIcon.js"], "sourcesContent": ["import * as React from \"react\";\nfunction ShieldCheckIcon({\n  title,\n  titleId,\n  ...props\n}, svgRef) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    xmlns: \"http://www.w3.org/2000/svg\",\n    fill: \"none\",\n    viewBox: \"0 0 24 24\",\n    strokeWidth: 1.5,\n    stroke: \"currentColor\",\n    \"aria-hidden\": \"true\",\n    \"data-slot\": \"icon\",\n    ref: svgRef,\n    \"aria-labelledby\": titleId\n  }, props), title ? /*#__PURE__*/React.createElement(\"title\", {\n    id: titleId\n  }, title) : null, /*#__PURE__*/React.createElement(\"path\", {\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\",\n    d: \"M9 12.75 11.25 15 15 9.75m-3-7.036A11.959 11.959 0 0 1 3.598 6 11.99 11.99 0 0 0 3 9.749c0 5.592 3.824 10.29 9 11.623 5.176-1.332 9-6.03 9-11.622 0-1.31-.21-2.571-.598-3.751h-.152c-3.196 0-6.1-1.248-8.25-3.285Z\"\n  }));\n}\nconst ForwardRef = /*#__PURE__*/ React.forwardRef(ShieldCheckIcon);\nexport default ForwardRef;"], "names": [], "mappings": ";;;AAAA;;AACA,SAAS,gBAAgB,EACvB,KAAK,EACL,OAAO,EACP,GAAG,OACJ,EAAE,MAAM;IACP,OAAO,WAAW,GAAE,CAAA,GAAA,6JAAA,CAAA,gBAAmB,AAAD,EAAE,OAAO,OAAO,MAAM,CAAC;QAC3D,OAAO;QACP,MAAM;QACN,SAAS;QACT,aAAa;QACb,QAAQ;QACR,eAAe;QACf,aAAa;QACb,KAAK;QACL,mBAAmB;IACrB,GAAG,QAAQ,QAAQ,WAAW,GAAE,CAAA,GAAA,6JAAA,CAAA,gBAAmB,AAAD,EAAE,SAAS;QAC3D,IAAI;IACN,GAAG,SAAS,MAAM,WAAW,GAAE,CAAA,GAAA,6JAAA,CAAA,gBAAmB,AAAD,EAAE,QAAQ;QACzD,eAAe;QACf,gBAAgB;QAChB,GAAG;IACL;AACF;AACA,MAAM,aAAa,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,EAAE;uCACnC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 999, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Project%20By%20AI/Technoloway/Technoloway%20%28Processing%29/technoloway-simple/node_modules/%40heroicons/react/24/outline/esm/PaintBrushIcon.js"], "sourcesContent": ["import * as React from \"react\";\nfunction PaintBrushIcon({\n  title,\n  titleId,\n  ...props\n}, svgRef) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    xmlns: \"http://www.w3.org/2000/svg\",\n    fill: \"none\",\n    viewBox: \"0 0 24 24\",\n    strokeWidth: 1.5,\n    stroke: \"currentColor\",\n    \"aria-hidden\": \"true\",\n    \"data-slot\": \"icon\",\n    ref: svgRef,\n    \"aria-labelledby\": titleId\n  }, props), title ? /*#__PURE__*/React.createElement(\"title\", {\n    id: titleId\n  }, title) : null, /*#__PURE__*/React.createElement(\"path\", {\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\",\n    d: \"M9.53 16.122a3 3 0 0 0-5.78 1.128 2.25 2.25 0 0 1-2.4 2.245 4.5 4.5 0 0 0 8.4-2.245c0-.399-.078-.78-.22-1.128Zm0 0a15.998 15.998 0 0 0 3.388-1.62m-5.043-.025a15.994 15.994 0 0 1 1.622-3.395m3.42 3.42a15.995 15.995 0 0 0 4.764-4.648l3.876-5.814a1.151 1.151 0 0 0-1.597-1.597L14.146 6.32a15.996 15.996 0 0 0-4.649 4.763m3.42 3.42a6.776 6.776 0 0 0-3.42-3.42\"\n  }));\n}\nconst ForwardRef = /*#__PURE__*/ React.forwardRef(PaintBrushIcon);\nexport default ForwardRef;"], "names": [], "mappings": ";;;AAAA;;AACA,SAAS,eAAe,EACtB,KAAK,EACL,OAAO,EACP,GAAG,OACJ,EAAE,MAAM;IACP,OAAO,WAAW,GAAE,CAAA,GAAA,6JAAA,CAAA,gBAAmB,AAAD,EAAE,OAAO,OAAO,MAAM,CAAC;QAC3D,OAAO;QACP,MAAM;QACN,SAAS;QACT,aAAa;QACb,QAAQ;QACR,eAAe;QACf,aAAa;QACb,KAAK;QACL,mBAAmB;IACrB,GAAG,QAAQ,QAAQ,WAAW,GAAE,CAAA,GAAA,6JAAA,CAAA,gBAAmB,AAAD,EAAE,SAAS;QAC3D,IAAI;IACN,GAAG,SAAS,MAAM,WAAW,GAAE,CAAA,GAAA,6JAAA,CAAA,gBAAmB,AAAD,EAAE,QAAQ;QACzD,eAAe;QACf,gBAAgB;QAChB,GAAG;IACL;AACF;AACA,MAAM,aAAa,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,EAAE;uCACnC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1041, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Project%20By%20AI/Technoloway/Technoloway%20%28Processing%29/technoloway-simple/node_modules/%40heroicons/react/24/outline/esm/CloudIcon.js"], "sourcesContent": ["import * as React from \"react\";\nfunction CloudIcon({\n  title,\n  titleId,\n  ...props\n}, svgRef) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    xmlns: \"http://www.w3.org/2000/svg\",\n    fill: \"none\",\n    viewBox: \"0 0 24 24\",\n    strokeWidth: 1.5,\n    stroke: \"currentColor\",\n    \"aria-hidden\": \"true\",\n    \"data-slot\": \"icon\",\n    ref: svgRef,\n    \"aria-labelledby\": titleId\n  }, props), title ? /*#__PURE__*/React.createElement(\"title\", {\n    id: titleId\n  }, title) : null, /*#__PURE__*/React.createElement(\"path\", {\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\",\n    d: \"M2.25 15a4.5 4.5 0 0 0 4.5 4.5H18a3.75 3.75 0 0 0 1.332-7.257 3 3 0 0 0-3.758-3.848 5.25 5.25 0 0 0-10.233 2.33A4.502 4.502 0 0 0 2.25 15Z\"\n  }));\n}\nconst ForwardRef = /*#__PURE__*/ React.forwardRef(CloudIcon);\nexport default ForwardRef;"], "names": [], "mappings": ";;;AAAA;;AACA,SAAS,UAAU,EACjB,KAAK,EACL,OAAO,EACP,GAAG,OACJ,EAAE,MAAM;IACP,OAAO,WAAW,GAAE,CAAA,GAAA,6JAAA,CAAA,gBAAmB,AAAD,EAAE,OAAO,OAAO,MAAM,CAAC;QAC3D,OAAO;QACP,MAAM;QACN,SAAS;QACT,aAAa;QACb,QAAQ;QACR,eAAe;QACf,aAAa;QACb,KAAK;QACL,mBAAmB;IACrB,GAAG,QAAQ,QAAQ,WAAW,GAAE,CAAA,GAAA,6JAAA,CAAA,gBAAmB,AAAD,EAAE,SAAS;QAC3D,IAAI;IACN,GAAG,SAAS,MAAM,WAAW,GAAE,CAAA,GAAA,6JAAA,CAAA,gBAAmB,AAAD,EAAE,QAAQ;QACzD,eAAe;QACf,gBAAgB;QAChB,GAAG;IACL;AACF;AACA,MAAM,aAAa,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,EAAE;uCACnC", "ignoreList": [0], "debugId": null}}]}