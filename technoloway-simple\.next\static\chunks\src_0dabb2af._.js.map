{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Project%20By%20AI/Technoloway/Technoloway%20%28Processing%29/technoloway-simple/src/components/header.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport Link from 'next/link';\nimport { usePathname } from 'next/navigation';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport { Bars3Icon, XMarkIcon } from '@heroicons/react/24/outline';\n\nconst navigation = [\n  { name: 'Home', href: '/' },\n  { name: 'About', href: '/about' },\n  { name: 'Services', href: '/services' },\n  { name: 'Projects', href: '/projects' },\n  { name: 'Technologies', href: '/technologies' },\n  { name: 'Contact', href: '/contact' },\n];\n\nexport function Header() {\n  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);\n  const pathname = usePathname();\n\n  const isActive = (href: string) => {\n    if (href === '/') {\n      return pathname === '/';\n    }\n    return pathname.startsWith(href);\n  };\n\n  return (\n    <header className=\"fixed inset-x-0 top-0 z-50 bg-white/80 backdrop-blur-md border-b border-gray-200\">\n      <nav className=\"container flex items-center justify-between py-4\" aria-label=\"Global\">\n        <div className=\"flex lg:flex-1\">\n          <Link href=\"/\" className=\"-m-1.5 p-1.5\">\n            <span className=\"sr-only\">Technoloway</span>\n            <div className=\"flex items-center space-x-2\">\n              <div className=\"h-8 w-8 bg-gradient-to-r from-blue-600 to-blue-400 rounded-lg flex items-center justify-center\">\n                <span className=\"text-white font-bold text-sm\">T</span>\n              </div>\n              <span className=\"text-xl font-bold text-gray-900\">Technoloway</span>\n            </div>\n          </Link>\n        </div>\n        \n        <div className=\"flex lg:hidden\">\n          <button\n            type=\"button\"\n            className=\"-m-2.5 inline-flex items-center justify-center rounded-md p-2.5 text-gray-700\"\n            onClick={() => setMobileMenuOpen(true)}\n          >\n            <span className=\"sr-only\">Open main menu</span>\n            <Bars3Icon className=\"h-6 w-6\" aria-hidden=\"true\" />\n          </button>\n        </div>\n        \n        <div className=\"hidden lg:flex lg:gap-x-8\">\n          {navigation.map((item) => (\n            <Link\n              key={item.name}\n              href={item.href}\n              className={`text-sm font-semibold leading-6 transition-colors hover:text-blue-600 ${\n                isActive(item.href) ? 'text-blue-600' : 'text-gray-900'\n              }`}\n            >\n              {item.name}\n            </Link>\n          ))}\n        </div>\n        \n        <div className=\"hidden lg:flex lg:flex-1 lg:justify-end\">\n          <Link href=\"/contact\" className=\"btn-primary\">\n            Get Started\n          </Link>\n        </div>\n      </nav>\n      \n      {/* Mobile menu */}\n      <AnimatePresence>\n        {mobileMenuOpen && (\n          <motion.div\n            initial={{ opacity: 0 }}\n            animate={{ opacity: 1 }}\n            exit={{ opacity: 0 }}\n            className=\"lg:hidden\"\n          >\n            <div className=\"fixed inset-0 z-50\" />\n            <motion.div\n              initial={{ x: '100%' }}\n              animate={{ x: 0 }}\n              exit={{ x: '100%' }}\n              transition={{ type: 'spring', damping: 25, stiffness: 200 }}\n              className=\"fixed inset-y-0 right-0 z-50 w-full overflow-y-auto bg-white px-6 py-6 sm:max-w-sm sm:ring-1 sm:ring-gray-900/10\"\n            >\n              <div className=\"flex items-center justify-between\">\n                <Link href=\"/\" className=\"-m-1.5 p-1.5\">\n                  <span className=\"sr-only\">Technoloway</span>\n                  <div className=\"flex items-center space-x-2\">\n                    <div className=\"h-8 w-8 bg-gradient-to-r from-blue-600 to-blue-400 rounded-lg flex items-center justify-center\">\n                      <span className=\"text-white font-bold text-sm\">T</span>\n                    </div>\n                    <span className=\"text-xl font-bold text-gray-900\">Technoloway</span>\n                  </div>\n                </Link>\n                <button\n                  type=\"button\"\n                  className=\"-m-2.5 rounded-md p-2.5 text-gray-700\"\n                  onClick={() => setMobileMenuOpen(false)}\n                >\n                  <span className=\"sr-only\">Close menu</span>\n                  <XMarkIcon className=\"h-6 w-6\" aria-hidden=\"true\" />\n                </button>\n              </div>\n              <div className=\"mt-6 flow-root\">\n                <div className=\"-my-6 divide-y divide-gray-500/10\">\n                  <div className=\"space-y-2 py-6\">\n                    {navigation.map((item) => (\n                      <Link\n                        key={item.name}\n                        href={item.href}\n                        className={`-mx-3 block rounded-lg px-3 py-2 text-base font-semibold leading-7 hover:bg-gray-50 ${\n                          isActive(item.href) ? 'text-blue-600' : 'text-gray-900'\n                        }`}\n                        onClick={() => setMobileMenuOpen(false)}\n                      >\n                        {item.name}\n                      </Link>\n                    ))}\n                  </div>\n                  <div className=\"py-6\">\n                    <Link\n                      href=\"/contact\"\n                      className=\"-mx-3 block rounded-lg px-3 py-2.5 text-base font-semibold leading-7 text-blue-600 hover:bg-gray-50\"\n                      onClick={() => setMobileMenuOpen(false)}\n                    >\n                      Get Started\n                    </Link>\n                  </div>\n                </div>\n              </div>\n            </motion.div>\n          </motion.div>\n        )}\n      </AnimatePresence>\n    </header>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AACA;AAAA;;;AANA;;;;;;AAQA,MAAM,aAAa;IACjB;QAAE,MAAM;QAAQ,MAAM;IAAI;IAC1B;QAAE,MAAM;QAAS,MAAM;IAAS;IAChC;QAAE,MAAM;QAAY,MAAM;IAAY;IACtC;QAAE,MAAM;QAAY,MAAM;IAAY;IACtC;QAAE,MAAM;QAAgB,MAAM;IAAgB;IAC9C;QAAE,MAAM;QAAW,MAAM;IAAW;CACrC;AAEM,SAAS;;IACd,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,WAAW,CAAA,GAAA,qIAAA,CAAA,cAAW,AAAD;IAE3B,MAAM,WAAW,CAAC;QAChB,IAAI,SAAS,KAAK;YAChB,OAAO,aAAa;QACtB;QACA,OAAO,SAAS,UAAU,CAAC;IAC7B;IAEA,qBACE,6LAAC;QAAO,WAAU;;0BAChB,6LAAC;gBAAI,WAAU;gBAAmD,cAAW;;kCAC3E,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC,+JAAA,CAAA,UAAI;4BAAC,MAAK;4BAAI,WAAU;;8CACvB,6LAAC;oCAAK,WAAU;8CAAU;;;;;;8CAC1B,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDAAK,WAAU;0DAA+B;;;;;;;;;;;sDAEjD,6LAAC;4CAAK,WAAU;sDAAkC;;;;;;;;;;;;;;;;;;;;;;;kCAKxD,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BACC,MAAK;4BACL,WAAU;4BACV,SAAS,IAAM,kBAAkB;;8CAEjC,6LAAC;oCAAK,WAAU;8CAAU;;;;;;8CAC1B,6LAAC,oNAAA,CAAA,YAAS;oCAAC,WAAU;oCAAU,eAAY;;;;;;;;;;;;;;;;;kCAI/C,6LAAC;wBAAI,WAAU;kCACZ,WAAW,GAAG,CAAC,CAAC,qBACf,6LAAC,+JAAA,CAAA,UAAI;gCAEH,MAAM,KAAK,IAAI;gCACf,WAAW,CAAC,sEAAsE,EAChF,SAAS,KAAK,IAAI,IAAI,kBAAkB,iBACxC;0CAED,KAAK,IAAI;+BANL,KAAK,IAAI;;;;;;;;;;kCAWpB,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC,+JAAA,CAAA,UAAI;4BAAC,MAAK;4BAAW,WAAU;sCAAc;;;;;;;;;;;;;;;;;0BAOlD,6LAAC,4LAAA,CAAA,kBAAe;0BACb,gCACC,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;oBAAE;oBACtB,SAAS;wBAAE,SAAS;oBAAE;oBACtB,MAAM;wBAAE,SAAS;oBAAE;oBACnB,WAAU;;sCAEV,6LAAC;4BAAI,WAAU;;;;;;sCACf,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,SAAS;gCAAE,GAAG;4BAAO;4BACrB,SAAS;gCAAE,GAAG;4BAAE;4BAChB,MAAM;gCAAE,GAAG;4BAAO;4BAClB,YAAY;gCAAE,MAAM;gCAAU,SAAS;gCAAI,WAAW;4BAAI;4BAC1D,WAAU;;8CAEV,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,+JAAA,CAAA,UAAI;4CAAC,MAAK;4CAAI,WAAU;;8DACvB,6LAAC;oDAAK,WAAU;8DAAU;;;;;;8DAC1B,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAI,WAAU;sEACb,cAAA,6LAAC;gEAAK,WAAU;0EAA+B;;;;;;;;;;;sEAEjD,6LAAC;4DAAK,WAAU;sEAAkC;;;;;;;;;;;;;;;;;;sDAGtD,6LAAC;4CACC,MAAK;4CACL,WAAU;4CACV,SAAS,IAAM,kBAAkB;;8DAEjC,6LAAC;oDAAK,WAAU;8DAAU;;;;;;8DAC1B,6LAAC,oNAAA,CAAA,YAAS;oDAAC,WAAU;oDAAU,eAAY;;;;;;;;;;;;;;;;;;8CAG/C,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;0DACZ,WAAW,GAAG,CAAC,CAAC,qBACf,6LAAC,+JAAA,CAAA,UAAI;wDAEH,MAAM,KAAK,IAAI;wDACf,WAAW,CAAC,oFAAoF,EAC9F,SAAS,KAAK,IAAI,IAAI,kBAAkB,iBACxC;wDACF,SAAS,IAAM,kBAAkB;kEAEhC,KAAK,IAAI;uDAPL,KAAK,IAAI;;;;;;;;;;0DAWpB,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC,+JAAA,CAAA,UAAI;oDACH,MAAK;oDACL,WAAU;oDACV,SAAS,IAAM,kBAAkB;8DAClC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAYrB;GA/HgB;;QAEG,qIAAA,CAAA,cAAW;;;KAFd", "debugId": null}}, {"offset": {"line": 412, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Project%20By%20AI/Technoloway/Technoloway%20%28Processing%29/technoloway-simple/src/components/footer.tsx"], "sourcesContent": ["import Link from 'next/link';\n\nexport function Footer() {\n  return (\n    <footer className=\"bg-gray-900 text-white\">\n      <div className=\"container py-16\">\n        <div className=\"grid grid-cols-1 md:grid-cols-4 gap-8\">\n          <div className=\"col-span-1 md:col-span-2\">\n            <div className=\"flex items-center space-x-2 mb-6\">\n              <div className=\"h-8 w-8 bg-gradient-to-r from-blue-600 to-blue-400 rounded-lg flex items-center justify-center\">\n                <span className=\"text-white font-bold text-sm\">T</span>\n              </div>\n              <span className=\"text-xl font-bold\">Technoloway</span>\n            </div>\n            <p className=\"text-gray-300 mb-6 max-w-md\">\n              Transforming ideas into digital reality. We build exceptional software \n              solutions that drive business growth and innovation.\n            </p>\n            <div className=\"space-y-2\">\n              <p className=\"text-gray-300\"><EMAIL></p>\n              <p className=\"text-gray-300\">+****************</p>\n              <p className=\"text-gray-300\">San Francisco, CA</p>\n            </div>\n          </div>\n          \n          <div>\n            <h3 className=\"text-lg font-semibold mb-4\">Services</h3>\n            <ul className=\"space-y-2 text-gray-300\">\n              <li><Link href=\"/#services\" className=\"hover:text-white transition-colors\">Web Development</Link></li>\n              <li><Link href=\"/#services\" className=\"hover:text-white transition-colors\">Mobile Apps</Link></li>\n              <li><Link href=\"/#services\" className=\"hover:text-white transition-colors\">Cloud Solutions</Link></li>\n              <li><Link href=\"/#services\" className=\"hover:text-white transition-colors\">API Development</Link></li>\n            </ul>\n          </div>\n          \n          <div>\n            <h3 className=\"text-lg font-semibold mb-4\">Company</h3>\n            <ul className=\"space-y-2 text-gray-300\">\n              <li><Link href=\"/team\" className=\"hover:text-white transition-colors\">About</Link></li>\n              <li><Link href=\"/team\" className=\"hover:text-white transition-colors\">Team</Link></li>\n              <li><Link href=\"/portfolio\" className=\"hover:text-white transition-colors\">Portfolio</Link></li>\n              <li><Link href=\"/blog\" className=\"hover:text-white transition-colors\">Blog</Link></li>\n              <li><Link href=\"/#contact\" className=\"hover:text-white transition-colors\">Contact</Link></li>\n            </ul>\n          </div>\n        </div>\n        \n        <div className=\"border-t border-gray-700 mt-12 pt-8 text-center\">\n          <p className=\"text-gray-400\">\n            &copy; {new Date().getFullYear()} Technoloway. All rights reserved.\n          </p>\n        </div>\n      </div>\n    </footer>\n  );\n}\n"], "names": [], "mappings": ";;;;AAAA;;;AAEO,SAAS;IACd,qBACE,6LAAC;QAAO,WAAU;kBAChB,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDAAK,WAAU;0DAA+B;;;;;;;;;;;sDAEjD,6LAAC;4CAAK,WAAU;sDAAoB;;;;;;;;;;;;8CAEtC,6LAAC;oCAAE,WAAU;8CAA8B;;;;;;8CAI3C,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAE,WAAU;sDAAgB;;;;;;sDAC7B,6LAAC;4CAAE,WAAU;sDAAgB;;;;;;sDAC7B,6LAAC;4CAAE,WAAU;sDAAgB;;;;;;;;;;;;;;;;;;sCAIjC,6LAAC;;8CACC,6LAAC;oCAAG,WAAU;8CAA6B;;;;;;8CAC3C,6LAAC;oCAAG,WAAU;;sDACZ,6LAAC;sDAAG,cAAA,6LAAC,+JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAa,WAAU;0DAAqC;;;;;;;;;;;sDAC3E,6LAAC;sDAAG,cAAA,6LAAC,+JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAa,WAAU;0DAAqC;;;;;;;;;;;sDAC3E,6LAAC;sDAAG,cAAA,6LAAC,+JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAa,WAAU;0DAAqC;;;;;;;;;;;sDAC3E,6LAAC;sDAAG,cAAA,6LAAC,+JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAa,WAAU;0DAAqC;;;;;;;;;;;;;;;;;;;;;;;sCAI/E,6LAAC;;8CACC,6LAAC;oCAAG,WAAU;8CAA6B;;;;;;8CAC3C,6LAAC;oCAAG,WAAU;;sDACZ,6LAAC;sDAAG,cAAA,6LAAC,+JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAQ,WAAU;0DAAqC;;;;;;;;;;;sDACtE,6LAAC;sDAAG,cAAA,6LAAC,+JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAQ,WAAU;0DAAqC;;;;;;;;;;;sDACtE,6LAAC;sDAAG,cAAA,6LAAC,+JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAa,WAAU;0DAAqC;;;;;;;;;;;sDAC3E,6LAAC;sDAAG,cAAA,6LAAC,+JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAQ,WAAU;0DAAqC;;;;;;;;;;;sDACtE,6LAAC;sDAAG,cAAA,6LAAC,+JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAY,WAAU;0DAAqC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAKhF,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAE,WAAU;;4BAAgB;4BACnB,IAAI,OAAO,WAAW;4BAAG;;;;;;;;;;;;;;;;;;;;;;;AAM7C;KArDgB", "debugId": null}}, {"offset": {"line": 744, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Project%20By%20AI/Technoloway/Technoloway%20%28Processing%29/technoloway-simple/src/app/contact/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport { motion } from 'framer-motion';\nimport { useForm } from 'react-hook-form';\nimport { zodResolver } from '@hookform/resolvers/zod';\nimport { z } from 'zod';\nimport {\n  EnvelopeIcon,\n  PhoneIcon,\n  MapPinIcon,\n  ClockIcon,\n  CheckCircleIcon,\n  ExclamationTriangleIcon,\n} from '@heroicons/react/24/outline';\nimport { Header } from '@/components/header';\nimport { Footer } from '@/components/footer';\n\n// Form validation schema\nconst contactSchema = z.object({\n  firstName: z.string().min(2, 'First name must be at least 2 characters'),\n  lastName: z.string().min(2, 'Last name must be at least 2 characters'),\n  email: z.string().email('Please enter a valid email address'),\n  phone: z.string().min(10, 'Please enter a valid phone number').optional(),\n  company: z.string().optional(),\n  projectType: z.string().min(1, 'Please select a project type'),\n  budget: z.string().min(1, 'Please select a budget range'),\n  timeline: z.string().min(1, 'Please select a timeline'),\n  message: z.string().min(10, 'Message must be at least 10 characters'),\n  agreeToTerms: z.boolean().refine(val => val === true, 'You must agree to the terms'),\n});\n\ntype ContactFormData = z.infer<typeof contactSchema>;\n\nconst projectTypes = [\n  'Web Development',\n  'Mobile App Development',\n  'E-commerce Platform',\n  'Custom Software',\n  'API Development',\n  'Cloud Solutions',\n  'UI/UX Design',\n  'Consulting',\n  'Other'\n];\n\nconst budgetRanges = [\n  'Under $10,000',\n  '$10,000 - $25,000',\n  '$25,000 - $50,000',\n  '$50,000 - $100,000',\n  'Over $100,000',\n  'Not sure yet'\n];\n\nconst timelines = [\n  'ASAP',\n  '1-2 months',\n  '3-6 months',\n  '6-12 months',\n  'More than 1 year',\n  'Flexible'\n];\n\nexport default function ContactPage() {\n  const [isSubmitting, setIsSubmitting] = useState(false);\n  const [submitStatus, setSubmitStatus] = useState<'idle' | 'success' | 'error'>('idle');\n\n  const {\n    register,\n    handleSubmit,\n    formState: { errors },\n    reset,\n  } = useForm<ContactFormData>({\n    resolver: zodResolver(contactSchema),\n  });\n\n  const onSubmit = async (data: ContactFormData) => {\n    setIsSubmitting(true);\n    setSubmitStatus('idle');\n\n    try {\n      // Simulate API call\n      await new Promise(resolve => setTimeout(resolve, 2000));\n      \n      // In a real app, you would send the data to your API\n      console.log('Form data:', data);\n      \n      setSubmitStatus('success');\n      reset();\n    } catch (error) {\n      setSubmitStatus('error');\n    } finally {\n      setIsSubmitting(false);\n    }\n  };\n\n  return (\n    <div className=\"min-h-screen bg-white\">\n      <Header />\n\n      <main className=\"pt-20\">\n        {/* Hero Section */}\n        <section className=\"relative py-24 bg-gradient-to-br from-gray-50 via-white to-gray-100\">\n          <div className=\"container\">\n            <motion.div\n              initial={{ opacity: 0, y: 20 }}\n              animate={{ opacity: 1, y: 0 }}\n              transition={{ duration: 0.6 }}\n              className=\"text-center max-w-4xl mx-auto\"\n            >\n              <motion.h1\n                initial={{ opacity: 0, y: 20 }}\n                animate={{ opacity: 1, y: 0 }}\n                transition={{ duration: 0.6, delay: 0.1 }}\n                className=\"text-4xl font-bold tracking-tight text-gray-900 sm:text-6xl lg:text-7xl\"\n              >\n                Let's Build Something <span className=\"gradient-text\">Amazing</span>\n              </motion.h1>\n\n              <motion.p\n                initial={{ opacity: 0, y: 20 }}\n                animate={{ opacity: 1, y: 0 }}\n                transition={{ duration: 0.6, delay: 0.2 }}\n                className=\"mt-6 text-lg leading-8 text-gray-600 sm:text-xl lg:text-2xl\"\n              >\n                Ready to transform your ideas into reality? Get in touch with our team\n                and let's discuss how we can help bring your vision to life.\n              </motion.p>\n            </motion.div>\n          </div>\n        </section>\n\n        {/* Contact Info */}\n        <section className=\"py-16 bg-white\">\n          <div className=\"container\">\n            <div className=\"grid grid-cols-1 md:grid-cols-3 gap-8 mb-16\">\n              {[\n                {\n                  icon: EnvelopeIcon,\n                  title: 'Email Us',\n                  content: '<EMAIL>',\n                  subContent: 'We respond within 24 hours',\n                },\n                {\n                  icon: PhoneIcon,\n                  title: 'Call Us',\n                  content: '+****************',\n                  subContent: 'Mon-Fri 9AM-6PM EST',\n                },\n                {\n                  icon: MapPinIcon,\n                  title: 'Visit Us',\n                  content: '123 Tech Street, San Francisco, CA 94105',\n                  subContent: 'By appointment only',\n                },\n              ].map((item, index) => (\n                <motion.div\n                  key={item.title}\n                  initial={{ opacity: 0, y: 20 }}\n                  whileInView={{ opacity: 1, y: 0 }}\n                  transition={{ duration: 0.6, delay: index * 0.1 }}\n                  viewport={{ once: true }}\n                  className=\"text-center\"\n                >\n                  <div className=\"flex items-center justify-center w-16 h-16 bg-blue-100 rounded-2xl mx-auto mb-4\">\n                    <item.icon className=\"w-8 h-8 text-blue-600\" />\n                  </div>\n                  <h3 className=\"text-lg font-semibold text-gray-900 mb-2\">{item.title}</h3>\n                  <p className=\"text-gray-700 font-medium\">{item.content}</p>\n                  <p className=\"text-sm text-gray-500 mt-1\">{item.subContent}</p>\n                </motion.div>\n              ))}\n            </div>\n          </div>\n        </section>\n\n        {/* Contact Form */}\n        <section className=\"py-16 bg-gray-50\">\n          <div className=\"container\">\n            <div className=\"max-w-4xl mx-auto\">\n              <motion.div\n                initial={{ opacity: 0, y: 20 }}\n                whileInView={{ opacity: 1, y: 0 }}\n                transition={{ duration: 0.6 }}\n                viewport={{ once: true }}\n                className=\"text-center mb-12\"\n              >\n                <h2 className=\"text-3xl font-bold text-gray-900 mb-4\">Start Your Project</h2>\n                <p className=\"text-lg text-gray-600\">\n                  Fill out the form below and we'll get back to you within 24 hours with a detailed proposal.\n                </p>\n              </motion.div>\n\n              <motion.div\n                initial={{ opacity: 0, y: 20 }}\n                whileInView={{ opacity: 1, y: 0 }}\n                transition={{ duration: 0.6, delay: 0.2 }}\n                viewport={{ once: true }}\n                className=\"bg-white rounded-2xl shadow-lg p-8\"\n              >\n                {/* Success Message */}\n                {submitStatus === 'success' && (\n                  <motion.div\n                    initial={{ opacity: 0, scale: 0.95 }}\n                    animate={{ opacity: 1, scale: 1 }}\n                    className=\"mb-8 p-4 bg-green-50 border border-green-200 rounded-lg flex items-center\"\n                  >\n                    <CheckCircleIcon className=\"w-5 h-5 text-green-500 mr-3\" />\n                    <div>\n                      <h3 className=\"text-sm font-medium text-green-800\">Message sent successfully!</h3>\n                      <p className=\"text-sm text-green-700 mt-1\">\n                        Thank you for reaching out. We'll get back to you within 24 hours.\n                      </p>\n                    </div>\n                  </motion.div>\n                )}\n\n                {/* Error Message */}\n                {submitStatus === 'error' && (\n                  <motion.div\n                    initial={{ opacity: 0, scale: 0.95 }}\n                    animate={{ opacity: 1, scale: 1 }}\n                    className=\"mb-8 p-4 bg-red-50 border border-red-200 rounded-lg flex items-center\"\n                  >\n                    <ExclamationTriangleIcon className=\"w-5 h-5 text-red-500 mr-3\" />\n                    <div>\n                      <h3 className=\"text-sm font-medium text-red-800\">Error sending message</h3>\n                      <p className=\"text-sm text-red-700 mt-1\">\n                        Please try again or contact us <NAME_EMAIL>\n                      </p>\n                    </div>\n                  </motion.div>\n                )}\n\n                <form onSubmit={handleSubmit(onSubmit)} className=\"space-y-6\">\n                  {/* Name Fields */}\n                  <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n                    <div>\n                      <label htmlFor=\"firstName\" className=\"block text-sm font-medium text-gray-700 mb-2\">\n                        First Name *\n                      </label>\n                      <input\n                        {...register('firstName')}\n                        type=\"text\"\n                        id=\"firstName\"\n                        className={`block w-full px-4 py-3 border rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${\n                          errors.firstName ? 'border-red-300' : 'border-gray-300'\n                        }`}\n                        placeholder=\"John\"\n                      />\n                      {errors.firstName && (\n                        <p className=\"mt-1 text-sm text-red-600\">{errors.firstName.message}</p>\n                      )}\n                    </div>\n\n                    <div>\n                      <label htmlFor=\"lastName\" className=\"block text-sm font-medium text-gray-700 mb-2\">\n                        Last Name *\n                      </label>\n                      <input\n                        {...register('lastName')}\n                        type=\"text\"\n                        id=\"lastName\"\n                        className={`block w-full px-4 py-3 border rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${\n                          errors.lastName ? 'border-red-300' : 'border-gray-300'\n                        }`}\n                        placeholder=\"Doe\"\n                      />\n                      {errors.lastName && (\n                        <p className=\"mt-1 text-sm text-red-600\">{errors.lastName.message}</p>\n                      )}\n                    </div>\n                  </div>\n\n                  {/* Contact Fields */}\n                  <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n                    <div>\n                      <label htmlFor=\"email\" className=\"block text-sm font-medium text-gray-700 mb-2\">\n                        Email Address *\n                      </label>\n                      <input\n                        {...register('email')}\n                        type=\"email\"\n                        id=\"email\"\n                        className={`block w-full px-4 py-3 border rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${\n                          errors.email ? 'border-red-300' : 'border-gray-300'\n                        }`}\n                        placeholder=\"<EMAIL>\"\n                      />\n                      {errors.email && (\n                        <p className=\"mt-1 text-sm text-red-600\">{errors.email.message}</p>\n                      )}\n                    </div>\n\n                    <div>\n                      <label htmlFor=\"phone\" className=\"block text-sm font-medium text-gray-700 mb-2\">\n                        Phone Number\n                      </label>\n                      <input\n                        {...register('phone')}\n                        type=\"tel\"\n                        id=\"phone\"\n                        className={`block w-full px-4 py-3 border rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${\n                          errors.phone ? 'border-red-300' : 'border-gray-300'\n                        }`}\n                        placeholder=\"+****************\"\n                      />\n                      {errors.phone && (\n                        <p className=\"mt-1 text-sm text-red-600\">{errors.phone.message}</p>\n                      )}\n                    </div>\n                  </div>\n\n                  {/* Company */}\n                  <div>\n                    <label htmlFor=\"company\" className=\"block text-sm font-medium text-gray-700 mb-2\">\n                      Company Name\n                    </label>\n                    <input\n                      {...register('company')}\n                      type=\"text\"\n                      id=\"company\"\n                      className=\"block w-full px-4 py-3 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"\n                      placeholder=\"Your Company\"\n                    />\n                  </div>\n\n                  {/* Project Details */}\n                  <div className=\"grid grid-cols-1 md:grid-cols-3 gap-6\">\n                    <div>\n                      <label htmlFor=\"projectType\" className=\"block text-sm font-medium text-gray-700 mb-2\">\n                        Project Type *\n                      </label>\n                      <select\n                        {...register('projectType')}\n                        id=\"projectType\"\n                        className={`block w-full px-4 py-3 border rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${\n                          errors.projectType ? 'border-red-300' : 'border-gray-300'\n                        }`}\n                      >\n                        <option value=\"\">Select type</option>\n                        {projectTypes.map(type => (\n                          <option key={type} value={type}>{type}</option>\n                        ))}\n                      </select>\n                      {errors.projectType && (\n                        <p className=\"mt-1 text-sm text-red-600\">{errors.projectType.message}</p>\n                      )}\n                    </div>\n\n                    <div>\n                      <label htmlFor=\"budget\" className=\"block text-sm font-medium text-gray-700 mb-2\">\n                        Budget Range *\n                      </label>\n                      <select\n                        {...register('budget')}\n                        id=\"budget\"\n                        className={`block w-full px-4 py-3 border rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${\n                          errors.budget ? 'border-red-300' : 'border-gray-300'\n                        }`}\n                      >\n                        <option value=\"\">Select budget</option>\n                        {budgetRanges.map(range => (\n                          <option key={range} value={range}>{range}</option>\n                        ))}\n                      </select>\n                      {errors.budget && (\n                        <p className=\"mt-1 text-sm text-red-600\">{errors.budget.message}</p>\n                      )}\n                    </div>\n\n                    <div>\n                      <label htmlFor=\"timeline\" className=\"block text-sm font-medium text-gray-700 mb-2\">\n                        Timeline *\n                      </label>\n                      <select\n                        {...register('timeline')}\n                        id=\"timeline\"\n                        className={`block w-full px-4 py-3 border rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${\n                          errors.timeline ? 'border-red-300' : 'border-gray-300'\n                        }`}\n                      >\n                        <option value=\"\">Select timeline</option>\n                        {timelines.map(time => (\n                          <option key={time} value={time}>{time}</option>\n                        ))}\n                      </select>\n                      {errors.timeline && (\n                        <p className=\"mt-1 text-sm text-red-600\">{errors.timeline.message}</p>\n                      )}\n                    </div>\n                  </div>\n\n                  {/* Message */}\n                  <div>\n                    <label htmlFor=\"message\" className=\"block text-sm font-medium text-gray-700 mb-2\">\n                      Project Description *\n                    </label>\n                    <textarea\n                      {...register('message')}\n                      id=\"message\"\n                      rows={6}\n                      className={`block w-full px-4 py-3 border rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${\n                        errors.message ? 'border-red-300' : 'border-gray-300'\n                      }`}\n                      placeholder=\"Tell us about your project, goals, and any specific requirements...\"\n                    />\n                    {errors.message && (\n                      <p className=\"mt-1 text-sm text-red-600\">{errors.message.message}</p>\n                    )}\n                  </div>\n\n                  {/* Terms Agreement */}\n                  <div className=\"flex items-start\">\n                    <div className=\"flex items-center h-5\">\n                      <input\n                        {...register('agreeToTerms')}\n                        id=\"agreeToTerms\"\n                        type=\"checkbox\"\n                        className=\"focus:ring-blue-500 h-4 w-4 text-blue-600 border-gray-300 rounded\"\n                      />\n                    </div>\n                    <div className=\"ml-3 text-sm\">\n                      <label htmlFor=\"agreeToTerms\" className=\"text-gray-700\">\n                        I agree to the{' '}\n                        <a href=\"/legal/terms\" className=\"text-blue-600 hover:text-blue-500\">\n                          Terms of Service\n                        </a>{' '}\n                        and{' '}\n                        <a href=\"/legal/privacy\" className=\"text-blue-600 hover:text-blue-500\">\n                          Privacy Policy\n                        </a>\n                        *\n                      </label>\n                      {errors.agreeToTerms && (\n                        <p className=\"mt-1 text-sm text-red-600\">{errors.agreeToTerms.message}</p>\n                      )}\n                    </div>\n                  </div>\n\n                  {/* Submit Button */}\n                  <div>\n                    <button\n                      type=\"submit\"\n                      disabled={isSubmitting}\n                      className=\"w-full flex justify-center py-3 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed\"\n                    >\n                      {isSubmitting ? (\n                        <div className=\"flex items-center\">\n                          <div className=\"animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2\"></div>\n                          Sending Message...\n                        </div>\n                      ) : (\n                        'Send Message'\n                      )}\n                    </button>\n                  </div>\n                </form>\n              </motion.div>\n            </div>\n          </div>\n        </section>\n\n        {/* FAQ Section */}\n        <section className=\"py-16 bg-white\">\n          <div className=\"container\">\n            <motion.div\n              initial={{ opacity: 0, y: 20 }}\n              whileInView={{ opacity: 1, y: 0 }}\n              transition={{ duration: 0.6 }}\n              viewport={{ once: true }}\n              className=\"text-center mb-12\"\n            >\n              <h2 className=\"text-3xl font-bold text-gray-900 mb-4\">Frequently Asked Questions</h2>\n              <p className=\"text-lg text-gray-600 max-w-3xl mx-auto\">\n                Quick answers to common questions about our services and process.\n              </p>\n            </motion.div>\n\n            <div className=\"max-w-3xl mx-auto space-y-6\">\n              {[\n                {\n                  question: 'How long does a typical project take?',\n                  answer: 'Project timelines vary based on complexity and scope. Simple websites typically take 4-6 weeks, while complex applications can take 3-6 months. We provide detailed timelines during our initial consultation.'\n                },\n                {\n                  question: 'What is your development process?',\n                  answer: 'We follow an agile development methodology with regular check-ins and updates. Our process includes discovery, planning, design, development, testing, and deployment phases with continuous client collaboration.'\n                },\n                {\n                  question: 'Do you provide ongoing support and maintenance?',\n                  answer: 'Yes, we offer comprehensive support and maintenance packages to ensure your application stays secure, updated, and performing optimally. We provide different tiers of support based on your needs.'\n                },\n                {\n                  question: 'Can you work with our existing team?',\n                  answer: 'Absolutely! We can integrate with your existing development team, provide consulting services, or work as an extension of your team. We adapt our approach to fit your organizational needs.'\n                }\n              ].map((faq, index) => (\n                <motion.div\n                  key={index}\n                  initial={{ opacity: 0, y: 20 }}\n                  whileInView={{ opacity: 1, y: 0 }}\n                  transition={{ duration: 0.6, delay: index * 0.1 }}\n                  viewport={{ once: true }}\n                  className=\"bg-gray-50 rounded-lg p-6\"\n                >\n                  <h3 className=\"text-lg font-semibold text-gray-900 mb-3\">{faq.question}</h3>\n                  <p className=\"text-gray-600 leading-relaxed\">{faq.answer}</p>\n                </motion.div>\n              ))}\n            </div>\n          </div>\n        </section>\n      </main>\n\n      <Footer />\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AAQA;AACA;;;AAhBA;;;;;;;;;AAkBA,yBAAyB;AACzB,MAAM,gBAAgB,oLAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IAC7B,WAAW,oLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;IAC7B,UAAU,oLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;IAC5B,OAAO,oLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,KAAK,CAAC;IACxB,OAAO,oLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,IAAI,qCAAqC,QAAQ;IACvE,SAAS,oLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;IAC5B,aAAa,oLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;IAC/B,QAAQ,oLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;IAC1B,UAAU,oLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;IAC5B,SAAS,oLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,IAAI;IAC5B,cAAc,oLAAA,CAAA,IAAC,CAAC,OAAO,GAAG,MAAM,CAAC,CAAA,MAAO,QAAQ,MAAM;AACxD;AAIA,MAAM,eAAe;IACnB;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;CACD;AAED,MAAM,eAAe;IACnB;IACA;IACA;IACA;IACA;IACA;CACD;AAED,MAAM,YAAY;IAChB;IACA;IACA;IACA;IACA;IACA;CACD;AAEc,SAAS;;IACtB,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAgC;IAE/E,MAAM,EACJ,QAAQ,EACR,YAAY,EACZ,WAAW,EAAE,MAAM,EAAE,EACrB,KAAK,EACN,GAAG,CAAA,GAAA,iKAAA,CAAA,UAAO,AAAD,EAAmB;QAC3B,UAAU,CAAA,GAAA,iKAAA,CAAA,cAAW,AAAD,EAAE;IACxB;IAEA,MAAM,WAAW,OAAO;QACtB,gBAAgB;QAChB,gBAAgB;QAEhB,IAAI;YACF,oBAAoB;YACpB,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;YAEjD,qDAAqD;YACrD,QAAQ,GAAG,CAAC,cAAc;YAE1B,gBAAgB;YAChB;QACF,EAAE,OAAO,OAAO;YACd,gBAAgB;QAClB,SAAU;YACR,gBAAgB;QAClB;IACF;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC,+HAAA,CAAA,SAAM;;;;;0BAEP,6LAAC;gBAAK,WAAU;;kCAEd,6LAAC;wBAAQ,WAAU;kCACjB,cAAA,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gCACT,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAG;gCAC7B,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAE;gCAC5B,YAAY;oCAAE,UAAU;gCAAI;gCAC5B,WAAU;;kDAEV,6LAAC,6LAAA,CAAA,SAAM,CAAC,EAAE;wCACR,SAAS;4CAAE,SAAS;4CAAG,GAAG;wCAAG;wCAC7B,SAAS;4CAAE,SAAS;4CAAG,GAAG;wCAAE;wCAC5B,YAAY;4CAAE,UAAU;4CAAK,OAAO;wCAAI;wCACxC,WAAU;;4CACX;0DACuB,6LAAC;gDAAK,WAAU;0DAAgB;;;;;;;;;;;;kDAGxD,6LAAC,6LAAA,CAAA,SAAM,CAAC,CAAC;wCACP,SAAS;4CAAE,SAAS;4CAAG,GAAG;wCAAG;wCAC7B,SAAS;4CAAE,SAAS;4CAAG,GAAG;wCAAE;wCAC5B,YAAY;4CAAE,UAAU;4CAAK,OAAO;wCAAI;wCACxC,WAAU;kDACX;;;;;;;;;;;;;;;;;;;;;;kCASP,6LAAC;wBAAQ,WAAU;kCACjB,cAAA,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;0CACZ;oCACC;wCACE,MAAM,0NAAA,CAAA,eAAY;wCAClB,OAAO;wCACP,SAAS;wCACT,YAAY;oCACd;oCACA;wCACE,MAAM,oNAAA,CAAA,YAAS;wCACf,OAAO;wCACP,SAAS;wCACT,YAAY;oCACd;oCACA;wCACE,MAAM,sNAAA,CAAA,aAAU;wCAChB,OAAO;wCACP,SAAS;wCACT,YAAY;oCACd;iCACD,CAAC,GAAG,CAAC,CAAC,MAAM,sBACX,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wCAET,SAAS;4CAAE,SAAS;4CAAG,GAAG;wCAAG;wCAC7B,aAAa;4CAAE,SAAS;4CAAG,GAAG;wCAAE;wCAChC,YAAY;4CAAE,UAAU;4CAAK,OAAO,QAAQ;wCAAI;wCAChD,UAAU;4CAAE,MAAM;wCAAK;wCACvB,WAAU;;0DAEV,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC,KAAK,IAAI;oDAAC,WAAU;;;;;;;;;;;0DAEvB,6LAAC;gDAAG,WAAU;0DAA4C,KAAK,KAAK;;;;;;0DACpE,6LAAC;gDAAE,WAAU;0DAA6B,KAAK,OAAO;;;;;;0DACtD,6LAAC;gDAAE,WAAU;0DAA8B,KAAK,UAAU;;;;;;;uCAZrD,KAAK,KAAK;;;;;;;;;;;;;;;;;;;;kCAoBzB,6LAAC;wBAAQ,WAAU;kCACjB,cAAA,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wCACT,SAAS;4CAAE,SAAS;4CAAG,GAAG;wCAAG;wCAC7B,aAAa;4CAAE,SAAS;4CAAG,GAAG;wCAAE;wCAChC,YAAY;4CAAE,UAAU;wCAAI;wCAC5B,UAAU;4CAAE,MAAM;wCAAK;wCACvB,WAAU;;0DAEV,6LAAC;gDAAG,WAAU;0DAAwC;;;;;;0DACtD,6LAAC;gDAAE,WAAU;0DAAwB;;;;;;;;;;;;kDAKvC,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wCACT,SAAS;4CAAE,SAAS;4CAAG,GAAG;wCAAG;wCAC7B,aAAa;4CAAE,SAAS;4CAAG,GAAG;wCAAE;wCAChC,YAAY;4CAAE,UAAU;4CAAK,OAAO;wCAAI;wCACxC,UAAU;4CAAE,MAAM;wCAAK;wCACvB,WAAU;;4CAGT,iBAAiB,2BAChB,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gDACT,SAAS;oDAAE,SAAS;oDAAG,OAAO;gDAAK;gDACnC,SAAS;oDAAE,SAAS;oDAAG,OAAO;gDAAE;gDAChC,WAAU;;kEAEV,6LAAC,gOAAA,CAAA,kBAAe;wDAAC,WAAU;;;;;;kEAC3B,6LAAC;;0EACC,6LAAC;gEAAG,WAAU;0EAAqC;;;;;;0EACnD,6LAAC;gEAAE,WAAU;0EAA8B;;;;;;;;;;;;;;;;;;4CAQhD,iBAAiB,yBAChB,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gDACT,SAAS;oDAAE,SAAS;oDAAG,OAAO;gDAAK;gDACnC,SAAS;oDAAE,SAAS;oDAAG,OAAO;gDAAE;gDAChC,WAAU;;kEAEV,6LAAC,gPAAA,CAAA,0BAAuB;wDAAC,WAAU;;;;;;kEACnC,6LAAC;;0EACC,6LAAC;gEAAG,WAAU;0EAAmC;;;;;;0EACjD,6LAAC;gEAAE,WAAU;0EAA4B;;;;;;;;;;;;;;;;;;0DAO/C,6LAAC;gDAAK,UAAU,aAAa;gDAAW,WAAU;;kEAEhD,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;;kFACC,6LAAC;wEAAM,SAAQ;wEAAY,WAAU;kFAA+C;;;;;;kFAGpF,6LAAC;wEACE,GAAG,SAAS,YAAY;wEACzB,MAAK;wEACL,IAAG;wEACH,WAAW,CAAC,6HAA6H,EACvI,OAAO,SAAS,GAAG,mBAAmB,mBACtC;wEACF,aAAY;;;;;;oEAEb,OAAO,SAAS,kBACf,6LAAC;wEAAE,WAAU;kFAA6B,OAAO,SAAS,CAAC,OAAO;;;;;;;;;;;;0EAItE,6LAAC;;kFACC,6LAAC;wEAAM,SAAQ;wEAAW,WAAU;kFAA+C;;;;;;kFAGnF,6LAAC;wEACE,GAAG,SAAS,WAAW;wEACxB,MAAK;wEACL,IAAG;wEACH,WAAW,CAAC,6HAA6H,EACvI,OAAO,QAAQ,GAAG,mBAAmB,mBACrC;wEACF,aAAY;;;;;;oEAEb,OAAO,QAAQ,kBACd,6LAAC;wEAAE,WAAU;kFAA6B,OAAO,QAAQ,CAAC,OAAO;;;;;;;;;;;;;;;;;;kEAMvE,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;;kFACC,6LAAC;wEAAM,SAAQ;wEAAQ,WAAU;kFAA+C;;;;;;kFAGhF,6LAAC;wEACE,GAAG,SAAS,QAAQ;wEACrB,MAAK;wEACL,IAAG;wEACH,WAAW,CAAC,6HAA6H,EACvI,OAAO,KAAK,GAAG,mBAAmB,mBAClC;wEACF,aAAY;;;;;;oEAEb,OAAO,KAAK,kBACX,6LAAC;wEAAE,WAAU;kFAA6B,OAAO,KAAK,CAAC,OAAO;;;;;;;;;;;;0EAIlE,6LAAC;;kFACC,6LAAC;wEAAM,SAAQ;wEAAQ,WAAU;kFAA+C;;;;;;kFAGhF,6LAAC;wEACE,GAAG,SAAS,QAAQ;wEACrB,MAAK;wEACL,IAAG;wEACH,WAAW,CAAC,6HAA6H,EACvI,OAAO,KAAK,GAAG,mBAAmB,mBAClC;wEACF,aAAY;;;;;;oEAEb,OAAO,KAAK,kBACX,6LAAC;wEAAE,WAAU;kFAA6B,OAAO,KAAK,CAAC,OAAO;;;;;;;;;;;;;;;;;;kEAMpE,6LAAC;;0EACC,6LAAC;gEAAM,SAAQ;gEAAU,WAAU;0EAA+C;;;;;;0EAGlF,6LAAC;gEACE,GAAG,SAAS,UAAU;gEACvB,MAAK;gEACL,IAAG;gEACH,WAAU;gEACV,aAAY;;;;;;;;;;;;kEAKhB,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;;kFACC,6LAAC;wEAAM,SAAQ;wEAAc,WAAU;kFAA+C;;;;;;kFAGtF,6LAAC;wEACE,GAAG,SAAS,cAAc;wEAC3B,IAAG;wEACH,WAAW,CAAC,6HAA6H,EACvI,OAAO,WAAW,GAAG,mBAAmB,mBACxC;;0FAEF,6LAAC;gFAAO,OAAM;0FAAG;;;;;;4EAChB,aAAa,GAAG,CAAC,CAAA,qBAChB,6LAAC;oFAAkB,OAAO;8FAAO;mFAApB;;;;;;;;;;;oEAGhB,OAAO,WAAW,kBACjB,6LAAC;wEAAE,WAAU;kFAA6B,OAAO,WAAW,CAAC,OAAO;;;;;;;;;;;;0EAIxE,6LAAC;;kFACC,6LAAC;wEAAM,SAAQ;wEAAS,WAAU;kFAA+C;;;;;;kFAGjF,6LAAC;wEACE,GAAG,SAAS,SAAS;wEACtB,IAAG;wEACH,WAAW,CAAC,6HAA6H,EACvI,OAAO,MAAM,GAAG,mBAAmB,mBACnC;;0FAEF,6LAAC;gFAAO,OAAM;0FAAG;;;;;;4EAChB,aAAa,GAAG,CAAC,CAAA,sBAChB,6LAAC;oFAAmB,OAAO;8FAAQ;mFAAtB;;;;;;;;;;;oEAGhB,OAAO,MAAM,kBACZ,6LAAC;wEAAE,WAAU;kFAA6B,OAAO,MAAM,CAAC,OAAO;;;;;;;;;;;;0EAInE,6LAAC;;kFACC,6LAAC;wEAAM,SAAQ;wEAAW,WAAU;kFAA+C;;;;;;kFAGnF,6LAAC;wEACE,GAAG,SAAS,WAAW;wEACxB,IAAG;wEACH,WAAW,CAAC,6HAA6H,EACvI,OAAO,QAAQ,GAAG,mBAAmB,mBACrC;;0FAEF,6LAAC;gFAAO,OAAM;0FAAG;;;;;;4EAChB,UAAU,GAAG,CAAC,CAAA,qBACb,6LAAC;oFAAkB,OAAO;8FAAO;mFAApB;;;;;;;;;;;oEAGhB,OAAO,QAAQ,kBACd,6LAAC;wEAAE,WAAU;kFAA6B,OAAO,QAAQ,CAAC,OAAO;;;;;;;;;;;;;;;;;;kEAMvE,6LAAC;;0EACC,6LAAC;gEAAM,SAAQ;gEAAU,WAAU;0EAA+C;;;;;;0EAGlF,6LAAC;gEACE,GAAG,SAAS,UAAU;gEACvB,IAAG;gEACH,MAAM;gEACN,WAAW,CAAC,6HAA6H,EACvI,OAAO,OAAO,GAAG,mBAAmB,mBACpC;gEACF,aAAY;;;;;;4DAEb,OAAO,OAAO,kBACb,6LAAC;gEAAE,WAAU;0EAA6B,OAAO,OAAO,CAAC,OAAO;;;;;;;;;;;;kEAKpE,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAI,WAAU;0EACb,cAAA,6LAAC;oEACE,GAAG,SAAS,eAAe;oEAC5B,IAAG;oEACH,MAAK;oEACL,WAAU;;;;;;;;;;;0EAGd,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;wEAAM,SAAQ;wEAAe,WAAU;;4EAAgB;4EACvC;0FACf,6LAAC;gFAAE,MAAK;gFAAe,WAAU;0FAAoC;;;;;;4EAEhE;4EAAI;4EACL;0FACJ,6LAAC;gFAAE,MAAK;gFAAiB,WAAU;0FAAoC;;;;;;4EAEnE;;;;;;;oEAGL,OAAO,YAAY,kBAClB,6LAAC;wEAAE,WAAU;kFAA6B,OAAO,YAAY,CAAC,OAAO;;;;;;;;;;;;;;;;;;kEAM3E,6LAAC;kEACC,cAAA,6LAAC;4DACC,MAAK;4DACL,UAAU;4DACV,WAAU;sEAET,6BACC,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;wEAAI,WAAU;;;;;;oEAAuE;;;;;;uEAIxF;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAWhB,6LAAC;wBAAQ,WAAU;kCACjB,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oCACT,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAG;oCAC7B,aAAa;wCAAE,SAAS;wCAAG,GAAG;oCAAE;oCAChC,YAAY;wCAAE,UAAU;oCAAI;oCAC5B,UAAU;wCAAE,MAAM;oCAAK;oCACvB,WAAU;;sDAEV,6LAAC;4CAAG,WAAU;sDAAwC;;;;;;sDACtD,6LAAC;4CAAE,WAAU;sDAA0C;;;;;;;;;;;;8CAKzD,6LAAC;oCAAI,WAAU;8CACZ;wCACC;4CACE,UAAU;4CACV,QAAQ;wCACV;wCACA;4CACE,UAAU;4CACV,QAAQ;wCACV;wCACA;4CACE,UAAU;4CACV,QAAQ;wCACV;wCACA;4CACE,UAAU;4CACV,QAAQ;wCACV;qCACD,CAAC,GAAG,CAAC,CAAC,KAAK,sBACV,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4CAET,SAAS;gDAAE,SAAS;gDAAG,GAAG;4CAAG;4CAC7B,aAAa;gDAAE,SAAS;gDAAG,GAAG;4CAAE;4CAChC,YAAY;gDAAE,UAAU;gDAAK,OAAO,QAAQ;4CAAI;4CAChD,UAAU;gDAAE,MAAM;4CAAK;4CACvB,WAAU;;8DAEV,6LAAC;oDAAG,WAAU;8DAA4C,IAAI,QAAQ;;;;;;8DACtE,6LAAC;oDAAE,WAAU;8DAAiC,IAAI,MAAM;;;;;;;2CARnD;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAgBjB,6LAAC,+HAAA,CAAA,SAAM;;;;;;;;;;;AAGb;GAvcwB;;QASlB,iKAAA,CAAA,UAAO;;;KATW", "debugId": null}}]}