import * as core from "zod/v4/core";
import * as schemas from "./schemas.js";
export const ZodMiniISODateTime = /*@__PURE__*/ core.$constructor("$ZodISODateTime", (inst, def) => {
    core.$ZodISODateTime.init(inst, def);
    schemas.ZodMiniStringFormat.init(inst, def);
});
export function datetime(params) {
    return core._isoDateTime(ZodMiniISODateTime, params);
}
export const ZodMiniISODate = /*@__PURE__*/ core.$constructor("$ZodISODate", (inst, def) => {
    core.$ZodISODate.init(inst, def);
    schemas.ZodMiniStringFormat.init(inst, def);
});
export function date(params) {
    return core._isoDate(ZodMiniISODate, params);
}
export const ZodMiniISOTime = /*@__PURE__*/ core.$constructor("$ZodISOTime", (inst, def) => {
    core.$ZodISOTime.init(inst, def);
    schemas.ZodMiniStringFormat.init(inst, def);
});
export function time(params) {
    return core._isoTime(ZodMiniISOTime, params);
}
export const ZodMiniISODuration = /*@__PURE__*/ core.$constructor("$ZodISODuration", (inst, def) => {
    core.$ZodISODuration.init(inst, def);
    schemas.ZodMiniStringFormat.init(inst, def);
});
export function duration(params) {
    return core._isoDuration(ZodMiniISODuration, params);
}
