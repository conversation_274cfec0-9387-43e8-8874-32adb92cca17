{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Project%20By%20AI/Technoloway/Technoloway%20%28Processing%29/technoloway-simple/src/app/admin/invoices/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport { motion } from 'framer-motion';\nimport {\n  PlusIcon,\n  PencilIcon,\n  TrashIcon,\n  EyeIcon,\n  DocumentTextIcon,\n  CurrencyDollarIcon,\n  CalendarIcon,\n  CheckCircleIcon,\n  ExclamationTriangleIcon,\n  ClockIcon,\n  MagnifyingGlassIcon,\n  ArrowDownTrayIcon,\n} from '@heroicons/react/24/outline';\n\n// Mock data - in real app, this would come from API\nconst invoices = [\n  {\n    id: 1,\n    invoiceNumber: 'INV-2024-001',\n    clientName: 'GreenTech Solutions',\n    clientEmail: '<EMAIL>',\n    projectName: 'EcoCommerce Platform',\n    amount: 25000,\n    currency: 'USD',\n    status: 'Paid',\n    issueDate: '2024-01-15T00:00:00Z',\n    dueDate: '2024-02-14T00:00:00Z',\n    paidDate: '2024-01-28T00:00:00Z',\n    description: 'Development of e-commerce platform - Phase 1',\n    items: [\n      { description: 'Frontend Development', quantity: 80, rate: 150, amount: 12000 },\n      { description: 'Backend Development', quantity: 60, rate: 160, amount: 9600 },\n      { description: 'UI/UX Design', quantity: 40, rate: 120, amount: 4800 },\n      { description: 'Project Management', quantity: 20, rate: 100, amount: 2000 }\n    ],\n    taxRate: 0.08,\n    taxAmount: 2000,\n    totalAmount: 27000,\n    notes: 'Payment received via wire transfer',\n    createdAt: '2024-01-15T10:00:00Z',\n    updatedAt: '2024-01-28T14:30:00Z',\n  },\n  {\n    id: 2,\n    invoiceNumber: 'INV-2024-002',\n    clientName: 'MedTech Innovations',\n    clientEmail: '<EMAIL>',\n    projectName: 'HealthTracker Mobile App',\n    amount: 18000,\n    currency: 'USD',\n    status: 'Pending',\n    issueDate: '2024-01-20T00:00:00Z',\n    dueDate: '2024-02-19T00:00:00Z',\n    paidDate: null,\n    description: 'Mobile app development - Final phase',\n    items: [\n      { description: 'Mobile Development', quantity: 100, rate: 140, amount: 14000 },\n      { description: 'Testing & QA', quantity: 30, rate: 100, amount: 3000 },\n      { description: 'App Store Deployment', quantity: 10, rate: 100, amount: 1000 }\n    ],\n    taxRate: 0.08,\n    taxAmount: 1440,\n    totalAmount: 19440,\n    notes: 'Net 30 payment terms',\n    createdAt: '2024-01-20T11:00:00Z',\n    updatedAt: '2024-01-22T16:45:00Z',\n  },\n  {\n    id: 3,\n    invoiceNumber: 'INV-2024-003',\n    clientName: 'FinanceFlow Corp',\n    clientEmail: '<EMAIL>',\n    projectName: 'Financial Dashboard',\n    amount: 35000,\n    currency: 'USD',\n    status: 'Overdue',\n    issueDate: '2024-01-05T00:00:00Z',\n    dueDate: '2024-01-20T00:00:00Z',\n    paidDate: null,\n    description: 'Custom financial analytics dashboard',\n    items: [\n      { description: 'Dashboard Development', quantity: 120, rate: 160, amount: 19200 },\n      { description: 'Data Integration', quantity: 80, rate: 150, amount: 12000 },\n      { description: 'Security Implementation', quantity: 40, rate: 180, amount: 7200 }\n    ],\n    taxRate: 0.08,\n    taxAmount: 3072,\n    totalAmount: 41472,\n    notes: 'Follow up required - payment overdue',\n    createdAt: '2024-01-05T09:00:00Z',\n    updatedAt: '2024-01-25T13:20:00Z',\n  },\n  {\n    id: 4,\n    invoiceNumber: 'INV-2024-004',\n    clientName: 'EcoCommerce',\n    clientEmail: '<EMAIL>',\n    projectName: 'Website Maintenance',\n    amount: 5000,\n    currency: 'USD',\n    status: 'Draft',\n    issueDate: '2024-01-25T00:00:00Z',\n    dueDate: '2024-02-24T00:00:00Z',\n    paidDate: null,\n    description: 'Monthly maintenance and support',\n    items: [\n      { description: 'Website Maintenance', quantity: 20, rate: 120, amount: 2400 },\n      { description: 'Content Updates', quantity: 15, rate: 100, amount: 1500 },\n      { description: 'Performance Optimization', quantity: 10, rate: 150, amount: 1500 }\n    ],\n    taxRate: 0.08,\n    taxAmount: 432,\n    totalAmount: 5432,\n    notes: 'Monthly recurring service',\n    createdAt: '2024-01-25T14:00:00Z',\n    updatedAt: '2024-01-25T14:00:00Z',\n  },\n];\n\nconst statuses = ['All', 'Draft', 'Pending', 'Paid', 'Overdue', 'Cancelled'];\n\nconst getStatusColor = (status: string) => {\n  switch (status) {\n    case 'Paid': return 'bg-green-100 text-green-800';\n    case 'Pending': return 'bg-yellow-100 text-yellow-800';\n    case 'Overdue': return 'bg-red-100 text-red-800';\n    case 'Draft': return 'bg-gray-100 text-gray-800';\n    case 'Cancelled': return 'bg-gray-100 text-gray-800';\n    default: return 'bg-gray-100 text-gray-800';\n  }\n};\n\nconst getStatusIcon = (status: string) => {\n  switch (status) {\n    case 'Paid': return CheckCircleIcon;\n    case 'Pending': return ClockIcon;\n    case 'Overdue': return ExclamationTriangleIcon;\n    case 'Draft': return DocumentTextIcon;\n    default: return ClockIcon;\n  }\n};\n\nexport default function InvoicesPage() {\n  const [invoicesList, setInvoicesList] = useState(invoices);\n  const [searchTerm, setSearchTerm] = useState('');\n  const [selectedStatus, setSelectedStatus] = useState('All');\n  const [selectedInvoice, setSelectedInvoice] = useState<any>(null);\n  const [showAddModal, setShowAddModal] = useState(false);\n\n  const filteredInvoices = invoicesList.filter(invoice => {\n    const matchesSearch = invoice.invoiceNumber.toLowerCase().includes(searchTerm.toLowerCase()) ||\n                         invoice.clientName.toLowerCase().includes(searchTerm.toLowerCase()) ||\n                         invoice.projectName.toLowerCase().includes(searchTerm.toLowerCase());\n    const matchesStatus = selectedStatus === 'All' || invoice.status === selectedStatus;\n    \n    return matchesSearch && matchesStatus;\n  });\n\n  const handleStatusChange = (id: number, newStatus: string) => {\n    setInvoicesList(prev => prev.map(invoice => \n      invoice.id === id \n        ? { \n            ...invoice, \n            status: newStatus,\n            paidDate: newStatus === 'Paid' ? new Date().toISOString() : null\n          }\n        : invoice\n    ));\n  };\n\n  const handleDelete = (id: number) => {\n    if (confirm('Are you sure you want to delete this invoice?')) {\n      setInvoicesList(prev => prev.filter(invoice => invoice.id !== id));\n    }\n  };\n\n  const formatCurrency = (amount: number, currency: string = 'USD') => {\n    return new Intl.NumberFormat('en-US', {\n      style: 'currency',\n      currency: currency,\n      minimumFractionDigits: 0,\n    }).format(amount);\n  };\n\n  const formatDate = (dateString: string | null) => {\n    if (!dateString) return 'Not set';\n    return new Date(dateString).toLocaleDateString('en-US', {\n      year: 'numeric',\n      month: 'short',\n      day: 'numeric',\n    });\n  };\n\n  const isOverdue = (dueDate: string, status: string) => {\n    return status !== 'Paid' && new Date(dueDate) < new Date();\n  };\n\n  const getTotalStats = () => {\n    const total = invoicesList.reduce((sum, inv) => sum + inv.totalAmount, 0);\n    const paid = invoicesList.filter(inv => inv.status === 'Paid').reduce((sum, inv) => sum + inv.totalAmount, 0);\n    const pending = invoicesList.filter(inv => inv.status === 'Pending').reduce((sum, inv) => sum + inv.totalAmount, 0);\n    const overdue = invoicesList.filter(inv => inv.status === 'Overdue').reduce((sum, inv) => sum + inv.totalAmount, 0);\n    \n    return { total, paid, pending, overdue };\n  };\n\n  const stats = getTotalStats();\n\n  return (\n    <div className=\"py-6\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 md:px-8\">\n        {/* Header */}\n        <div className=\"md:flex md:items-center md:justify-between mb-8\">\n          <div className=\"flex-1 min-w-0\">\n            <h2 className=\"text-2xl font-bold leading-7 text-gray-900 sm:text-3xl sm:truncate\">\n              Invoices\n            </h2>\n            <p className=\"mt-1 text-sm text-gray-500\">\n              Manage client invoices, payments, and billing information\n            </p>\n          </div>\n          <div className=\"mt-4 flex md:mt-0 md:ml-4\">\n            <button\n              onClick={() => setShowAddModal(true)}\n              className=\"ml-3 inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500\"\n            >\n              <PlusIcon className=\"-ml-1 mr-2 h-5 w-5\" />\n              Create Invoice\n            </button>\n          </div>\n        </div>\n\n        {/* Stats */}\n        <div className=\"mb-6 grid grid-cols-1 gap-5 sm:grid-cols-4\">\n          <div className=\"bg-white overflow-hidden shadow rounded-lg\">\n            <div className=\"p-5\">\n              <div className=\"flex items-center\">\n                <div className=\"flex-shrink-0\">\n                  <div className=\"w-8 h-8 bg-blue-500 rounded-md flex items-center justify-center\">\n                    <span className=\"text-white text-sm font-bold\">{formatCurrency(stats.total / 1000)}K</span>\n                  </div>\n                </div>\n                <div className=\"ml-5 w-0 flex-1\">\n                  <dl>\n                    <dt className=\"text-sm font-medium text-gray-500 truncate\">Total Invoiced</dt>\n                    <dd className=\"text-lg font-medium text-gray-900\">All Time</dd>\n                  </dl>\n                </div>\n              </div>\n            </div>\n          </div>\n\n          <div className=\"bg-white overflow-hidden shadow rounded-lg\">\n            <div className=\"p-5\">\n              <div className=\"flex items-center\">\n                <div className=\"flex-shrink-0\">\n                  <div className=\"w-8 h-8 bg-green-500 rounded-md flex items-center justify-center\">\n                    <span className=\"text-white text-sm font-bold\">{formatCurrency(stats.paid / 1000)}K</span>\n                  </div>\n                </div>\n                <div className=\"ml-5 w-0 flex-1\">\n                  <dl>\n                    <dt className=\"text-sm font-medium text-gray-500 truncate\">Paid</dt>\n                    <dd className=\"text-lg font-medium text-gray-900\">Received</dd>\n                  </dl>\n                </div>\n              </div>\n            </div>\n          </div>\n\n          <div className=\"bg-white overflow-hidden shadow rounded-lg\">\n            <div className=\"p-5\">\n              <div className=\"flex items-center\">\n                <div className=\"flex-shrink-0\">\n                  <div className=\"w-8 h-8 bg-yellow-500 rounded-md flex items-center justify-center\">\n                    <span className=\"text-white text-sm font-bold\">{formatCurrency(stats.pending / 1000)}K</span>\n                  </div>\n                </div>\n                <div className=\"ml-5 w-0 flex-1\">\n                  <dl>\n                    <dt className=\"text-sm font-medium text-gray-500 truncate\">Pending</dt>\n                    <dd className=\"text-lg font-medium text-gray-900\">Outstanding</dd>\n                  </dl>\n                </div>\n              </div>\n            </div>\n          </div>\n\n          <div className=\"bg-white overflow-hidden shadow rounded-lg\">\n            <div className=\"p-5\">\n              <div className=\"flex items-center\">\n                <div className=\"flex-shrink-0\">\n                  <div className=\"w-8 h-8 bg-red-500 rounded-md flex items-center justify-center\">\n                    <span className=\"text-white text-sm font-bold\">{formatCurrency(stats.overdue / 1000)}K</span>\n                  </div>\n                </div>\n                <div className=\"ml-5 w-0 flex-1\">\n                  <dl>\n                    <dt className=\"text-sm font-medium text-gray-500 truncate\">Overdue</dt>\n                    <dd className=\"text-lg font-medium text-gray-900\">Past Due</dd>\n                  </dl>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        {/* Filters */}\n        <div className=\"mb-6 bg-white shadow rounded-lg\">\n          <div className=\"px-4 py-5 sm:p-6\">\n            <div className=\"grid grid-cols-1 gap-4 sm:grid-cols-2\">\n              {/* Search */}\n              <div className=\"relative\">\n                <div className=\"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\">\n                  <MagnifyingGlassIcon className=\"h-5 w-5 text-gray-400\" />\n                </div>\n                <input\n                  type=\"text\"\n                  placeholder=\"Search invoices...\"\n                  value={searchTerm}\n                  onChange={(e) => setSearchTerm(e.target.value)}\n                  className=\"block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-blue-500 focus:border-blue-500 sm:text-sm\"\n                />\n              </div>\n\n              {/* Status Filter */}\n              <div>\n                <select\n                  value={selectedStatus}\n                  onChange={(e) => setSelectedStatus(e.target.value)}\n                  className=\"block w-full pl-3 pr-10 py-2 text-base border border-gray-300 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm rounded-md\"\n                >\n                  {statuses.map(status => (\n                    <option key={status} value={status}>{status}</option>\n                  ))}\n                </select>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        {/* Invoices List */}\n        <div className=\"bg-white shadow overflow-hidden sm:rounded-md\">\n          <ul className=\"divide-y divide-gray-200\">\n            {filteredInvoices.map((invoice, index) => {\n              const StatusIcon = getStatusIcon(invoice.status);\n              return (\n                <motion.li\n                  key={invoice.id}\n                  initial={{ opacity: 0, y: 20 }}\n                  animate={{ opacity: 1, y: 0 }}\n                  transition={{ duration: 0.3, delay: index * 0.1 }}\n                >\n                  <div className=\"px-4 py-4 sm:px-6\">\n                    <div className=\"flex items-center justify-between\">\n                      <div className=\"flex items-start space-x-4 flex-1\">\n                        <div className=\"flex-shrink-0\">\n                          <div className={`w-10 h-10 rounded-lg flex items-center justify-center ${getStatusColor(invoice.status)}`}>\n                            <StatusIcon className=\"w-6 h-6\" />\n                          </div>\n                        </div>\n                        \n                        <div className=\"flex-1 min-w-0\">\n                          <div className=\"flex items-center space-x-2 mb-1\">\n                            <h3 className=\"text-lg font-medium text-gray-900\">\n                              {invoice.invoiceNumber}\n                            </h3>\n                            <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(invoice.status)}`}>\n                              {invoice.status}\n                            </span>\n                            {isOverdue(invoice.dueDate, invoice.status) && (\n                              <span className=\"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800\">\n                                Overdue\n                              </span>\n                            )}\n                          </div>\n                          \n                          <div className=\"flex items-center space-x-4 text-sm text-gray-500 mb-2\">\n                            <span>{invoice.clientName}</span>\n                            <span>{invoice.projectName}</span>\n                            <span className=\"font-medium text-gray-900\">{formatCurrency(invoice.totalAmount)}</span>\n                          </div>\n                          \n                          <div className=\"flex items-center space-x-4 text-sm text-gray-500\">\n                            <div className=\"flex items-center\">\n                              <CalendarIcon className=\"h-4 w-4 mr-1\" />\n                              Issued: {formatDate(invoice.issueDate)}\n                            </div>\n                            <div className=\"flex items-center\">\n                              <CalendarIcon className=\"h-4 w-4 mr-1\" />\n                              Due: {formatDate(invoice.dueDate)}\n                            </div>\n                            {invoice.paidDate && (\n                              <div className=\"flex items-center\">\n                                <CheckCircleIcon className=\"h-4 w-4 mr-1 text-green-500\" />\n                                Paid: {formatDate(invoice.paidDate)}\n                              </div>\n                            )}\n                          </div>\n                        </div>\n                      </div>\n                      \n                      <div className=\"flex items-center space-x-2 ml-4\">\n                        <button\n                          onClick={() => setSelectedInvoice(invoice)}\n                          className=\"text-gray-400 hover:text-blue-600 transition-colors\"\n                          title=\"View Details\"\n                        >\n                          <EyeIcon className=\"h-5 w-5\" />\n                        </button>\n                        <button\n                          className=\"text-gray-400 hover:text-blue-600 transition-colors\"\n                          title=\"Edit\"\n                        >\n                          <PencilIcon className=\"h-5 w-5\" />\n                        </button>\n                        <button\n                          className=\"text-gray-400 hover:text-green-600 transition-colors\"\n                          title=\"Download PDF\"\n                        >\n                          <ArrowDownTrayIcon className=\"h-5 w-5\" />\n                        </button>\n                        <button\n                          onClick={() => handleDelete(invoice.id)}\n                          className=\"text-gray-400 hover:text-red-600 transition-colors\"\n                          title=\"Delete\"\n                        >\n                          <TrashIcon className=\"h-5 w-5\" />\n                        </button>\n                      </div>\n                    </div>\n                  </div>\n                </motion.li>\n              );\n            })}\n          </ul>\n        </div>\n\n        {/* Empty State */}\n        {filteredInvoices.length === 0 && (\n          <div className=\"bg-white shadow rounded-lg\">\n            <div className=\"text-center py-12\">\n              <DocumentTextIcon className=\"mx-auto h-12 w-12 text-gray-400\" />\n              <h3 className=\"mt-2 text-sm font-medium text-gray-900\">No invoices found</h3>\n              <p className=\"mt-1 text-sm text-gray-500\">\n                Try adjusting your search or filter criteria.\n              </p>\n            </div>\n          </div>\n        )}\n\n        {/* Invoice Detail Modal */}\n        {selectedInvoice && (\n          <div className=\"fixed inset-0 z-50 overflow-y-auto\">\n            <div className=\"flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0\">\n              <div className=\"fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity\" onClick={() => setSelectedInvoice(null)} />\n              \n              <div className=\"inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-4xl sm:w-full\">\n                <div className=\"bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4 max-h-96 overflow-y-auto\">\n                  <div className=\"sm:flex sm:items-start\">\n                    <div className=\"mt-3 text-center sm:mt-0 sm:text-left w-full\">\n                      <h3 className=\"text-lg leading-6 font-medium text-gray-900 mb-4\">\n                        Invoice {selectedInvoice.invoiceNumber}\n                      </h3>\n                      \n                      <div className=\"space-y-4\">\n                        <div className=\"grid grid-cols-2 gap-4\">\n                          <div>\n                            <label className=\"block text-sm font-medium text-gray-700\">Client</label>\n                            <p className=\"mt-1 text-sm text-gray-900\">{selectedInvoice.clientName}</p>\n                          </div>\n                          <div>\n                            <label className=\"block text-sm font-medium text-gray-700\">Project</label>\n                            <p className=\"mt-1 text-sm text-gray-900\">{selectedInvoice.projectName}</p>\n                          </div>\n                          <div>\n                            <label className=\"block text-sm font-medium text-gray-700\">Status</label>\n                            <span className={`mt-1 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(selectedInvoice.status)}`}>\n                              {selectedInvoice.status}\n                            </span>\n                          </div>\n                          <div>\n                            <label className=\"block text-sm font-medium text-gray-700\">Total Amount</label>\n                            <p className=\"mt-1 text-sm text-gray-900 font-semibold\">{formatCurrency(selectedInvoice.totalAmount)}</p>\n                          </div>\n                        </div>\n                        \n                        <div>\n                          <label className=\"block text-sm font-medium text-gray-700\">Line Items</label>\n                          <div className=\"mt-1 overflow-x-auto\">\n                            <table className=\"min-w-full divide-y divide-gray-200\">\n                              <thead className=\"bg-gray-50\">\n                                <tr>\n                                  <th className=\"px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase\">Description</th>\n                                  <th className=\"px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase\">Qty</th>\n                                  <th className=\"px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase\">Rate</th>\n                                  <th className=\"px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase\">Amount</th>\n                                </tr>\n                              </thead>\n                              <tbody className=\"bg-white divide-y divide-gray-200\">\n                                {selectedInvoice.items.map((item: any, index: number) => (\n                                  <tr key={index}>\n                                    <td className=\"px-3 py-2 text-sm text-gray-900\">{item.description}</td>\n                                    <td className=\"px-3 py-2 text-sm text-gray-900\">{item.quantity}</td>\n                                    <td className=\"px-3 py-2 text-sm text-gray-900\">{formatCurrency(item.rate)}</td>\n                                    <td className=\"px-3 py-2 text-sm text-gray-900\">{formatCurrency(item.amount)}</td>\n                                  </tr>\n                                ))}\n                              </tbody>\n                            </table>\n                          </div>\n                        </div>\n                        \n                        <div className=\"grid grid-cols-3 gap-4\">\n                          <div>\n                            <label className=\"block text-sm font-medium text-gray-700\">Issue Date</label>\n                            <p className=\"mt-1 text-sm text-gray-900\">{formatDate(selectedInvoice.issueDate)}</p>\n                          </div>\n                          <div>\n                            <label className=\"block text-sm font-medium text-gray-700\">Due Date</label>\n                            <p className=\"mt-1 text-sm text-gray-900\">{formatDate(selectedInvoice.dueDate)}</p>\n                          </div>\n                          <div>\n                            <label className=\"block text-sm font-medium text-gray-700\">Paid Date</label>\n                            <p className=\"mt-1 text-sm text-gray-900\">{formatDate(selectedInvoice.paidDate)}</p>\n                          </div>\n                        </div>\n                      </div>\n                    </div>\n                  </div>\n                </div>\n                <div className=\"bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse\">\n                  <button\n                    type=\"button\"\n                    className=\"w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-blue-600 text-base font-medium text-white hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 sm:ml-3 sm:w-auto sm:text-sm\"\n                  >\n                    Edit Invoice\n                  </button>\n                  <button\n                    type=\"button\"\n                    className=\"mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 sm:mt-0 sm:w-auto sm:text-sm\"\n                    onClick={() => setSelectedInvoice(null)}\n                  >\n                    Close\n                  </button>\n                </div>\n              </div>\n            </div>\n          </div>\n        )}\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAJA;;;;;AAmBA,oDAAoD;AACpD,MAAM,WAAW;IACf;QACE,IAAI;QACJ,eAAe;QACf,YAAY;QACZ,aAAa;QACb,aAAa;QACb,QAAQ;QACR,UAAU;QACV,QAAQ;QACR,WAAW;QACX,SAAS;QACT,UAAU;QACV,aAAa;QACb,OAAO;YACL;gBAAE,aAAa;gBAAwB,UAAU;gBAAI,MAAM;gBAAK,QAAQ;YAAM;YAC9E;gBAAE,aAAa;gBAAuB,UAAU;gBAAI,MAAM;gBAAK,QAAQ;YAAK;YAC5E;gBAAE,aAAa;gBAAgB,UAAU;gBAAI,MAAM;gBAAK,QAAQ;YAAK;YACrE;gBAAE,aAAa;gBAAsB,UAAU;gBAAI,MAAM;gBAAK,QAAQ;YAAK;SAC5E;QACD,SAAS;QACT,WAAW;QACX,aAAa;QACb,OAAO;QACP,WAAW;QACX,WAAW;IACb;IACA;QACE,IAAI;QACJ,eAAe;QACf,YAAY;QACZ,aAAa;QACb,aAAa;QACb,QAAQ;QACR,UAAU;QACV,QAAQ;QACR,WAAW;QACX,SAAS;QACT,UAAU;QACV,aAAa;QACb,OAAO;YACL;gBAAE,aAAa;gBAAsB,UAAU;gBAAK,MAAM;gBAAK,QAAQ;YAAM;YAC7E;gBAAE,aAAa;gBAAgB,UAAU;gBAAI,MAAM;gBAAK,QAAQ;YAAK;YACrE;gBAAE,aAAa;gBAAwB,UAAU;gBAAI,MAAM;gBAAK,QAAQ;YAAK;SAC9E;QACD,SAAS;QACT,WAAW;QACX,aAAa;QACb,OAAO;QACP,WAAW;QACX,WAAW;IACb;IACA;QACE,IAAI;QACJ,eAAe;QACf,YAAY;QACZ,aAAa;QACb,aAAa;QACb,QAAQ;QACR,UAAU;QACV,QAAQ;QACR,WAAW;QACX,SAAS;QACT,UAAU;QACV,aAAa;QACb,OAAO;YACL;gBAAE,aAAa;gBAAyB,UAAU;gBAAK,MAAM;gBAAK,QAAQ;YAAM;YAChF;gBAAE,aAAa;gBAAoB,UAAU;gBAAI,MAAM;gBAAK,QAAQ;YAAM;YAC1E;gBAAE,aAAa;gBAA2B,UAAU;gBAAI,MAAM;gBAAK,QAAQ;YAAK;SACjF;QACD,SAAS;QACT,WAAW;QACX,aAAa;QACb,OAAO;QACP,WAAW;QACX,WAAW;IACb;IACA;QACE,IAAI;QACJ,eAAe;QACf,YAAY;QACZ,aAAa;QACb,aAAa;QACb,QAAQ;QACR,UAAU;QACV,QAAQ;QACR,WAAW;QACX,SAAS;QACT,UAAU;QACV,aAAa;QACb,OAAO;YACL;gBAAE,aAAa;gBAAuB,UAAU;gBAAI,MAAM;gBAAK,QAAQ;YAAK;YAC5E;gBAAE,aAAa;gBAAmB,UAAU;gBAAI,MAAM;gBAAK,QAAQ;YAAK;YACxE;gBAAE,aAAa;gBAA4B,UAAU;gBAAI,MAAM;gBAAK,QAAQ;YAAK;SAClF;QACD,SAAS;QACT,WAAW;QACX,aAAa;QACb,OAAO;QACP,WAAW;QACX,WAAW;IACb;CACD;AAED,MAAM,WAAW;IAAC;IAAO;IAAS;IAAW;IAAQ;IAAW;CAAY;AAE5E,MAAM,iBAAiB,CAAC;IACtB,OAAQ;QACN,KAAK;YAAQ,OAAO;QACpB,KAAK;YAAW,OAAO;QACvB,KAAK;YAAW,OAAO;QACvB,KAAK;YAAS,OAAO;QACrB,KAAK;YAAa,OAAO;QACzB;YAAS,OAAO;IAClB;AACF;AAEA,MAAM,gBAAgB,CAAC;IACrB,OAAQ;QACN,KAAK;YAAQ,OAAO,6NAAA,CAAA,kBAAe;QACnC,KAAK;YAAW,OAAO,iNAAA,CAAA,YAAS;QAChC,KAAK;YAAW,OAAO,6OAAA,CAAA,0BAAuB;QAC9C,KAAK;YAAS,OAAO,+NAAA,CAAA,mBAAgB;QACrC;YAAS,OAAO,iNAAA,CAAA,YAAS;IAC3B;AACF;AAEe,SAAS;IACtB,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAO;IAC5D,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEjD,MAAM,mBAAmB,aAAa,MAAM,CAAC,CAAA;QAC3C,MAAM,gBAAgB,QAAQ,aAAa,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW,OACpE,QAAQ,UAAU,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW,OAChE,QAAQ,WAAW,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW;QACtF,MAAM,gBAAgB,mBAAmB,SAAS,QAAQ,MAAM,KAAK;QAErE,OAAO,iBAAiB;IAC1B;IAEA,MAAM,qBAAqB,CAAC,IAAY;QACtC,gBAAgB,CAAA,OAAQ,KAAK,GAAG,CAAC,CAAA,UAC/B,QAAQ,EAAE,KAAK,KACX;oBACE,GAAG,OAAO;oBACV,QAAQ;oBACR,UAAU,cAAc,SAAS,IAAI,OAAO,WAAW,KAAK;gBAC9D,IACA;IAER;IAEA,MAAM,eAAe,CAAC;QACpB,IAAI,QAAQ,kDAAkD;YAC5D,gBAAgB,CAAA,OAAQ,KAAK,MAAM,CAAC,CAAA,UAAW,QAAQ,EAAE,KAAK;QAChE;IACF;IAEA,MAAM,iBAAiB,CAAC,QAAgB,WAAmB,KAAK;QAC9D,OAAO,IAAI,KAAK,YAAY,CAAC,SAAS;YACpC,OAAO;YACP,UAAU;YACV,uBAAuB;QACzB,GAAG,MAAM,CAAC;IACZ;IAEA,MAAM,aAAa,CAAC;QAClB,IAAI,CAAC,YAAY,OAAO;QACxB,OAAO,IAAI,KAAK,YAAY,kBAAkB,CAAC,SAAS;YACtD,MAAM;YACN,OAAO;YACP,KAAK;QACP;IACF;IAEA,MAAM,YAAY,CAAC,SAAiB;QAClC,OAAO,WAAW,UAAU,IAAI,KAAK,WAAW,IAAI;IACtD;IAEA,MAAM,gBAAgB;QACpB,MAAM,QAAQ,aAAa,MAAM,CAAC,CAAC,KAAK,MAAQ,MAAM,IAAI,WAAW,EAAE;QACvE,MAAM,OAAO,aAAa,MAAM,CAAC,CAAA,MAAO,IAAI,MAAM,KAAK,QAAQ,MAAM,CAAC,CAAC,KAAK,MAAQ,MAAM,IAAI,WAAW,EAAE;QAC3G,MAAM,UAAU,aAAa,MAAM,CAAC,CAAA,MAAO,IAAI,MAAM,KAAK,WAAW,MAAM,CAAC,CAAC,KAAK,MAAQ,MAAM,IAAI,WAAW,EAAE;QACjH,MAAM,UAAU,aAAa,MAAM,CAAC,CAAA,MAAO,IAAI,MAAM,KAAK,WAAW,MAAM,CAAC,CAAC,KAAK,MAAQ,MAAM,IAAI,WAAW,EAAE;QAEjH,OAAO;YAAE;YAAO;YAAM;YAAS;QAAQ;IACzC;IAEA,MAAM,QAAQ;IAEd,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;;8BAEb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAAqE;;;;;;8CAGnF,8OAAC;oCAAE,WAAU;8CAA6B;;;;;;;;;;;;sCAI5C,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCACC,SAAS,IAAM,gBAAgB;gCAC/B,WAAU;;kDAEV,8OAAC,+MAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;oCAAuB;;;;;;;;;;;;;;;;;;8BAOjD,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDAAK,WAAU;;wDAAgC,eAAe,MAAM,KAAK,GAAG;wDAAM;;;;;;;;;;;;;;;;;sDAGvF,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;;kEACC,8OAAC;wDAAG,WAAU;kEAA6C;;;;;;kEAC3D,8OAAC;wDAAG,WAAU;kEAAoC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAO5D,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDAAK,WAAU;;wDAAgC,eAAe,MAAM,IAAI,GAAG;wDAAM;;;;;;;;;;;;;;;;;sDAGtF,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;;kEACC,8OAAC;wDAAG,WAAU;kEAA6C;;;;;;kEAC3D,8OAAC;wDAAG,WAAU;kEAAoC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAO5D,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDAAK,WAAU;;wDAAgC,eAAe,MAAM,OAAO,GAAG;wDAAM;;;;;;;;;;;;;;;;;sDAGzF,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;;kEACC,8OAAC;wDAAG,WAAU;kEAA6C;;;;;;kEAC3D,8OAAC;wDAAG,WAAU;kEAAoC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAO5D,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDAAK,WAAU;;wDAAgC,eAAe,MAAM,OAAO,GAAG;wDAAM;;;;;;;;;;;;;;;;;sDAGzF,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;;kEACC,8OAAC;wDAAG,WAAU;kEAA6C;;;;;;kEAC3D,8OAAC;wDAAG,WAAU;kEAAoC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAS9D,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CAEb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC,qOAAA,CAAA,sBAAmB;gDAAC,WAAU;;;;;;;;;;;sDAEjC,8OAAC;4CACC,MAAK;4CACL,aAAY;4CACZ,OAAO;4CACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;4CAC7C,WAAU;;;;;;;;;;;;8CAKd,8OAAC;8CACC,cAAA,8OAAC;wCACC,OAAO;wCACP,UAAU,CAAC,IAAM,kBAAkB,EAAE,MAAM,CAAC,KAAK;wCACjD,WAAU;kDAET,SAAS,GAAG,CAAC,CAAA,uBACZ,8OAAC;gDAAoB,OAAO;0DAAS;+CAAxB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BASzB,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAG,WAAU;kCACX,iBAAiB,GAAG,CAAC,CAAC,SAAS;4BAC9B,MAAM,aAAa,cAAc,QAAQ,MAAM;4BAC/C,qBACE,8OAAC,0LAAA,CAAA,SAAM,CAAC,EAAE;gCAER,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAG;gCAC7B,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAE;gCAC5B,YAAY;oCAAE,UAAU;oCAAK,OAAO,QAAQ;gCAAI;0CAEhD,cAAA,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC;4DAAI,WAAW,CAAC,sDAAsD,EAAE,eAAe,QAAQ,MAAM,GAAG;sEACvG,cAAA,8OAAC;gEAAW,WAAU;;;;;;;;;;;;;;;;kEAI1B,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEAAG,WAAU;kFACX,QAAQ,aAAa;;;;;;kFAExB,8OAAC;wEAAK,WAAW,CAAC,wEAAwE,EAAE,eAAe,QAAQ,MAAM,GAAG;kFACzH,QAAQ,MAAM;;;;;;oEAEhB,UAAU,QAAQ,OAAO,EAAE,QAAQ,MAAM,mBACxC,8OAAC;wEAAK,WAAU;kFAAkG;;;;;;;;;;;;0EAMtH,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;kFAAM,QAAQ,UAAU;;;;;;kFACzB,8OAAC;kFAAM,QAAQ,WAAW;;;;;;kFAC1B,8OAAC;wEAAK,WAAU;kFAA6B,eAAe,QAAQ,WAAW;;;;;;;;;;;;0EAGjF,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEAAI,WAAU;;0FACb,8OAAC,uNAAA,CAAA,eAAY;gFAAC,WAAU;;;;;;4EAAiB;4EAChC,WAAW,QAAQ,SAAS;;;;;;;kFAEvC,8OAAC;wEAAI,WAAU;;0FACb,8OAAC,uNAAA,CAAA,eAAY;gFAAC,WAAU;;;;;;4EAAiB;4EACnC,WAAW,QAAQ,OAAO;;;;;;;oEAEjC,QAAQ,QAAQ,kBACf,8OAAC;wEAAI,WAAU;;0FACb,8OAAC,6NAAA,CAAA,kBAAe;gFAAC,WAAU;;;;;;4EAAgC;4EACpD,WAAW,QAAQ,QAAQ;;;;;;;;;;;;;;;;;;;;;;;;;0DAO5C,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDACC,SAAS,IAAM,mBAAmB;wDAClC,WAAU;wDACV,OAAM;kEAEN,cAAA,8OAAC,6MAAA,CAAA,UAAO;4DAAC,WAAU;;;;;;;;;;;kEAErB,8OAAC;wDACC,WAAU;wDACV,OAAM;kEAEN,cAAA,8OAAC,mNAAA,CAAA,aAAU;4DAAC,WAAU;;;;;;;;;;;kEAExB,8OAAC;wDACC,WAAU;wDACV,OAAM;kEAEN,cAAA,8OAAC,iOAAA,CAAA,oBAAiB;4DAAC,WAAU;;;;;;;;;;;kEAE/B,8OAAC;wDACC,SAAS,IAAM,aAAa,QAAQ,EAAE;wDACtC,WAAU;wDACV,OAAM;kEAEN,cAAA,8OAAC,iNAAA,CAAA,YAAS;4DAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;+BA/ExB,QAAQ,EAAE;;;;;wBAsFrB;;;;;;;;;;;gBAKH,iBAAiB,MAAM,KAAK,mBAC3B,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,+NAAA,CAAA,mBAAgB;gCAAC,WAAU;;;;;;0CAC5B,8OAAC;gCAAG,WAAU;0CAAyC;;;;;;0CACvD,8OAAC;gCAAE,WAAU;0CAA6B;;;;;;;;;;;;;;;;;gBAQ/C,iCACC,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;gCAA6D,SAAS,IAAM,mBAAmB;;;;;;0CAE9G,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAG,WAAU;;4DAAmD;4DACtD,gBAAgB,aAAa;;;;;;;kEAGxC,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;;0FACC,8OAAC;gFAAM,WAAU;0FAA0C;;;;;;0FAC3D,8OAAC;gFAAE,WAAU;0FAA8B,gBAAgB,UAAU;;;;;;;;;;;;kFAEvE,8OAAC;;0FACC,8OAAC;gFAAM,WAAU;0FAA0C;;;;;;0FAC3D,8OAAC;gFAAE,WAAU;0FAA8B,gBAAgB,WAAW;;;;;;;;;;;;kFAExE,8OAAC;;0FACC,8OAAC;gFAAM,WAAU;0FAA0C;;;;;;0FAC3D,8OAAC;gFAAK,WAAW,CAAC,6EAA6E,EAAE,eAAe,gBAAgB,MAAM,GAAG;0FACtI,gBAAgB,MAAM;;;;;;;;;;;;kFAG3B,8OAAC;;0FACC,8OAAC;gFAAM,WAAU;0FAA0C;;;;;;0FAC3D,8OAAC;gFAAE,WAAU;0FAA4C,eAAe,gBAAgB,WAAW;;;;;;;;;;;;;;;;;;0EAIvG,8OAAC;;kFACC,8OAAC;wEAAM,WAAU;kFAA0C;;;;;;kFAC3D,8OAAC;wEAAI,WAAU;kFACb,cAAA,8OAAC;4EAAM,WAAU;;8FACf,8OAAC;oFAAM,WAAU;8FACf,cAAA,8OAAC;;0GACC,8OAAC;gGAAG,WAAU;0GAAkE;;;;;;0GAChF,8OAAC;gGAAG,WAAU;0GAAkE;;;;;;0GAChF,8OAAC;gGAAG,WAAU;0GAAkE;;;;;;0GAChF,8OAAC;gGAAG,WAAU;0GAAkE;;;;;;;;;;;;;;;;;8FAGpF,8OAAC;oFAAM,WAAU;8FACd,gBAAgB,KAAK,CAAC,GAAG,CAAC,CAAC,MAAW,sBACrC,8OAAC;;8GACC,8OAAC;oGAAG,WAAU;8GAAmC,KAAK,WAAW;;;;;;8GACjE,8OAAC;oGAAG,WAAU;8GAAmC,KAAK,QAAQ;;;;;;8GAC9D,8OAAC;oGAAG,WAAU;8GAAmC,eAAe,KAAK,IAAI;;;;;;8GACzE,8OAAC;oGAAG,WAAU;8GAAmC,eAAe,KAAK,MAAM;;;;;;;2FAJpE;;;;;;;;;;;;;;;;;;;;;;;;;;;0EAYnB,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;;0FACC,8OAAC;gFAAM,WAAU;0FAA0C;;;;;;0FAC3D,8OAAC;gFAAE,WAAU;0FAA8B,WAAW,gBAAgB,SAAS;;;;;;;;;;;;kFAEjF,8OAAC;;0FACC,8OAAC;gFAAM,WAAU;0FAA0C;;;;;;0FAC3D,8OAAC;gFAAE,WAAU;0FAA8B,WAAW,gBAAgB,OAAO;;;;;;;;;;;;kFAE/E,8OAAC;;0FACC,8OAAC;gFAAM,WAAU;0FAA0C;;;;;;0FAC3D,8OAAC;gFAAE,WAAU;0FAA8B,WAAW,gBAAgB,QAAQ;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kDAO1F,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDACC,MAAK;gDACL,WAAU;0DACX;;;;;;0DAGD,8OAAC;gDACC,MAAK;gDACL,WAAU;gDACV,SAAS,IAAM,mBAAmB;0DACnC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAWnB", "debugId": null}}]}