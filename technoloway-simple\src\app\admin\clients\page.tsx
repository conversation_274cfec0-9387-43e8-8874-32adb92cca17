'use client';

import { useState } from 'react';
import { motion } from 'framer-motion';
import {
  PlusIcon,
  PencilIcon,
  TrashIcon,
  MagnifyingGlassIcon,
  EyeIcon,
  BuildingOfficeIcon,
  EnvelopeIcon,
  PhoneIcon,
  MapPinIcon,
  CurrencyDollarIcon,
  BriefcaseIcon,
} from '@heroicons/react/24/outline';

// Mock data - in real app, this would come from API
const clients = [
  {
    id: 1,
    name: 'GreenTech Solutions',
    contactPerson: '<PERSON>',
    email: '<EMAIL>',
    phone: '+****************',
    company: 'GreenTech Solutions Inc.',
    industry: 'Technology',
    location: 'San Francisco, CA',
    status: 'Active',
    joinDate: '2023-01-15',
    totalProjects: 3,
    activeProjects: 1,
    totalRevenue: 185000,
    lastContact: '2024-01-20',
    website: 'https://greentech-solutions.com',
    notes: 'Long-term client focused on sustainable technology solutions.',
    logo: 'https://images.unsplash.com/photo-**********-b33ff0c44a43?w=100&h=100&fit=crop',
  },
  {
    id: 2,
    name: 'MedTech Innovations',
    contactPerson: 'Dr. <PERSON>',
    email: 'micha<PERSON>.<EMAIL>',
    phone: '+****************',
    company: 'MedTech Innovations LLC',
    industry: 'Healthcare',
    location: 'Boston, MA',
    status: 'Active',
    joinDate: '2023-03-10',
    totalProjects: 2,
    activeProjects: 1,
    totalRevenue: 125000,
    lastContact: '2024-01-18',
    website: 'https://medtech-innovations.com',
    notes: 'Healthcare technology startup with innovative mobile solutions.',
    logo: 'https://images.unsplash.com/photo-1576091160399-112ba8d25d1f?w=100&h=100&fit=crop',
  },
  {
    id: 3,
    name: 'EcoCommerce',
    contactPerson: 'Emily Rodriguez',
    email: '<EMAIL>',
    phone: '+****************',
    company: 'EcoCommerce Ltd.',
    industry: 'E-commerce',
    location: 'Austin, TX',
    status: 'Active',
    joinDate: '2023-06-20',
    totalProjects: 1,
    activeProjects: 0,
    totalRevenue: 95000,
    lastContact: '2024-01-15',
    website: 'https://ecocommerce.com',
    notes: 'Sustainable e-commerce platform with focus on eco-friendly products.',
    logo: 'https://images.unsplash.com/photo-1441986300917-64674bd600d8?w=100&h=100&fit=crop',
  },
  {
    id: 4,
    name: 'FinanceFlow Corp',
    contactPerson: 'David Park',
    email: '<EMAIL>',
    phone: '+****************',
    company: 'FinanceFlow Corporation',
    industry: 'Finance',
    location: 'New York, NY',
    status: 'Prospect',
    joinDate: '2024-01-10',
    totalProjects: 0,
    activeProjects: 0,
    totalRevenue: 0,
    lastContact: '2024-01-22',
    website: 'https://financeflow.com',
    notes: 'Potential client interested in financial dashboard development.',
    logo: 'https://images.unsplash.com/photo-**********-6726b3ff858f?w=100&h=100&fit=crop',
  },
];

const industries = ['All', 'Technology', 'Healthcare', 'E-commerce', 'Finance', 'Education', 'Manufacturing'];
const statuses = ['All', 'Active', 'Inactive', 'Prospect', 'Former'];

const getStatusColor = (status: string) => {
  switch (status) {
    case 'Active': return 'bg-green-100 text-green-800';
    case 'Inactive': return 'bg-gray-100 text-gray-800';
    case 'Prospect': return 'bg-blue-100 text-blue-800';
    case 'Former': return 'bg-red-100 text-red-800';
    default: return 'bg-gray-100 text-gray-800';
  }
};

export default function ClientsPage() {
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedIndustry, setSelectedIndustry] = useState('All');
  const [selectedStatus, setSelectedStatus] = useState('All');

  const filteredClients = clients.filter(client => {
    const matchesSearch = client.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         client.contactPerson.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         client.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         client.company.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesIndustry = selectedIndustry === 'All' || client.industry === selectedIndustry;
    const matchesStatus = selectedStatus === 'All' || client.status === selectedStatus;
    
    return matchesSearch && matchesIndustry && matchesStatus;
  });

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
    }).format(amount);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
    });
  };

  return (
    <div className="py-6">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 md:px-8">
        {/* Header */}
        <div className="md:flex md:items-center md:justify-between">
          <div className="flex-1 min-w-0">
            <h2 className="text-2xl font-bold leading-7 text-gray-900 sm:text-3xl sm:truncate">
              Clients
            </h2>
            <p className="mt-1 text-sm text-gray-500">
              Manage your client relationships and track business opportunities
            </p>
          </div>
          <div className="mt-4 flex md:mt-0 md:ml-4">
            <button className="ml-3 inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
              <PlusIcon className="-ml-1 mr-2 h-5 w-5" />
              Add Client
            </button>
          </div>
        </div>

        {/* Stats Cards */}
        <div className="mt-6 grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4">
          <div className="bg-white overflow-hidden shadow rounded-lg">
            <div className="p-5">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <div className="w-8 h-8 bg-blue-500 rounded-md flex items-center justify-center">
                    <span className="text-white text-sm font-bold">{clients.length}</span>
                  </div>
                </div>
                <div className="ml-5 w-0 flex-1">
                  <dl>
                    <dt className="text-sm font-medium text-gray-500 truncate">Total Clients</dt>
                    <dd className="text-lg font-medium text-gray-900">All Time</dd>
                  </dl>
                </div>
              </div>
            </div>
          </div>

          <div className="bg-white overflow-hidden shadow rounded-lg">
            <div className="p-5">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <div className="w-8 h-8 bg-green-500 rounded-md flex items-center justify-center">
                    <span className="text-white text-sm font-bold">
                      {clients.filter(c => c.status === 'Active').length}
                    </span>
                  </div>
                </div>
                <div className="ml-5 w-0 flex-1">
                  <dl>
                    <dt className="text-sm font-medium text-gray-500 truncate">Active Clients</dt>
                    <dd className="text-lg font-medium text-gray-900">Current</dd>
                  </dl>
                </div>
              </div>
            </div>
          </div>

          <div className="bg-white overflow-hidden shadow rounded-lg">
            <div className="p-5">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <div className="w-8 h-8 bg-purple-500 rounded-md flex items-center justify-center">
                    <span className="text-white text-sm font-bold">
                      {clients.filter(c => c.status === 'Prospect').length}
                    </span>
                  </div>
                </div>
                <div className="ml-5 w-0 flex-1">
                  <dl>
                    <dt className="text-sm font-medium text-gray-500 truncate">Prospects</dt>
                    <dd className="text-lg font-medium text-gray-900">Potential</dd>
                  </dl>
                </div>
              </div>
            </div>
          </div>

          <div className="bg-white overflow-hidden shadow rounded-lg">
            <div className="p-5">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <div className="w-8 h-8 bg-yellow-500 rounded-md flex items-center justify-center">
                    <span className="text-white text-sm font-bold">
                      {formatCurrency(clients.reduce((sum, c) => sum + c.totalRevenue, 0) / 1000)}K
                    </span>
                  </div>
                </div>
                <div className="ml-5 w-0 flex-1">
                  <dl>
                    <dt className="text-sm font-medium text-gray-500 truncate">Total Revenue</dt>
                    <dd className="text-lg font-medium text-gray-900">All Clients</dd>
                  </dl>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Filters */}
        <div className="mt-6 bg-white shadow rounded-lg">
          <div className="px-4 py-5 sm:p-6">
            <div className="grid grid-cols-1 gap-4 sm:grid-cols-3">
              {/* Search */}
              <div className="relative">
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <MagnifyingGlassIcon className="h-5 w-5 text-gray-400" />
                </div>
                <input
                  type="text"
                  placeholder="Search clients..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                />
              </div>

              {/* Industry Filter */}
              <div>
                <select
                  value={selectedIndustry}
                  onChange={(e) => setSelectedIndustry(e.target.value)}
                  className="block w-full pl-3 pr-10 py-2 text-base border border-gray-300 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm rounded-md"
                >
                  {industries.map(industry => (
                    <option key={industry} value={industry}>{industry}</option>
                  ))}
                </select>
              </div>

              {/* Status Filter */}
              <div>
                <select
                  value={selectedStatus}
                  onChange={(e) => setSelectedStatus(e.target.value)}
                  className="block w-full pl-3 pr-10 py-2 text-base border border-gray-300 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm rounded-md"
                >
                  {statuses.map(status => (
                    <option key={status} value={status}>{status}</option>
                  ))}
                </select>
              </div>
            </div>
          </div>
        </div>

        {/* Clients Grid */}
        <div className="mt-6 grid grid-cols-1 gap-6 lg:grid-cols-2">
          {filteredClients.map((client, index) => (
            <motion.div
              key={client.id}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.3, delay: index * 0.1 }}
              className="bg-white overflow-hidden shadow rounded-lg"
            >
              <div className="px-4 py-5 sm:p-6">
                <div className="flex items-center justify-between mb-4">
                  <div className="flex items-center space-x-3">
                    <div className="flex-shrink-0">
                      <img
                        className="h-12 w-12 rounded-lg object-cover"
                        src={client.logo}
                        alt={client.name}
                      />
                    </div>
                    <div>
                      <h3 className="text-lg font-medium text-gray-900">{client.name}</h3>
                      <p className="text-sm text-gray-500">{client.industry}</p>
                    </div>
                  </div>
                  <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(client.status)}`}>
                    {client.status}
                  </span>
                </div>

                {/* Contact Information */}
                <div className="space-y-2 mb-4">
                  <div className="flex items-center text-sm text-gray-600">
                    <BuildingOfficeIcon className="h-4 w-4 mr-2" />
                    <span>{client.contactPerson}</span>
                  </div>
                  <div className="flex items-center text-sm text-gray-600">
                    <EnvelopeIcon className="h-4 w-4 mr-2" />
                    <span>{client.email}</span>
                  </div>
                  <div className="flex items-center text-sm text-gray-600">
                    <PhoneIcon className="h-4 w-4 mr-2" />
                    <span>{client.phone}</span>
                  </div>
                  <div className="flex items-center text-sm text-gray-600">
                    <MapPinIcon className="h-4 w-4 mr-2" />
                    <span>{client.location}</span>
                  </div>
                </div>

                {/* Business Metrics */}
                <div className="grid grid-cols-2 gap-4 mb-4">
                  <div className="text-center p-3 bg-gray-50 rounded-lg">
                    <div className="text-lg font-semibold text-gray-900">{client.totalProjects}</div>
                    <div className="text-xs text-gray-500">Total Projects</div>
                  </div>
                  <div className="text-center p-3 bg-gray-50 rounded-lg">
                    <div className="text-lg font-semibold text-gray-900">{client.activeProjects}</div>
                    <div className="text-xs text-gray-500">Active Projects</div>
                  </div>
                  <div className="text-center p-3 bg-gray-50 rounded-lg col-span-2">
                    <div className="text-lg font-semibold text-gray-900">{formatCurrency(client.totalRevenue)}</div>
                    <div className="text-xs text-gray-500">Total Revenue</div>
                  </div>
                </div>

                {/* Additional Info */}
                <div className="text-sm text-gray-600 mb-4">
                  <p><strong>Joined:</strong> {formatDate(client.joinDate)}</p>
                  <p><strong>Last Contact:</strong> {formatDate(client.lastContact)}</p>
                  <p className="mt-2">{client.notes}</p>
                </div>

                {/* Actions */}
                <div className="flex justify-end space-x-2">
                  <button className="text-gray-400 hover:text-blue-600 transition-colors">
                    <EyeIcon className="h-5 w-5" />
                  </button>
                  <button className="text-gray-400 hover:text-blue-600 transition-colors">
                    <PencilIcon className="h-5 w-5" />
                  </button>
                  <button className="text-gray-400 hover:text-red-600 transition-colors">
                    <TrashIcon className="h-5 w-5" />
                  </button>
                </div>
              </div>
            </motion.div>
          ))}
        </div>

        {/* Empty State */}
        {filteredClients.length === 0 && (
          <div className="mt-6 bg-white shadow rounded-lg">
            <div className="text-center py-12">
              <svg
                className="mx-auto h-12 w-12 text-gray-400"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"
                />
              </svg>
              <h3 className="mt-2 text-sm font-medium text-gray-900">No clients found</h3>
              <p className="mt-1 text-sm text-gray-500">
                Try adjusting your search or filter criteria.
              </p>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
