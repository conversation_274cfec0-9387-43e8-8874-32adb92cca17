{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Project%20By%20AI/Technoloway/Technoloway%20%28Processing%29/technoloway-simple/src/app/admin/layout.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport Link from 'next/link';\nimport { usePathname } from 'next/navigation';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport {\n  HomeIcon,\n  UserGroupIcon,\n  DocumentTextIcon,\n  CogIcon,\n  ChartBarIcon,\n  BriefcaseIcon,\n  UserIcon,\n  DocumentDuplicateIcon,\n  ChatBubbleLeftRightIcon,\n  CloudArrowUpIcon,\n  Bars3Icon,\n  XMarkIcon,\n  BellIcon,\n  MagnifyingGlassIcon,\n  ArrowRightOnRectangleIcon,\n} from '@heroicons/react/24/outline';\n\nconst navigation = [\n  {\n    name: 'Dashboard',\n    href: '/admin',\n    icon: HomeIcon,\n    section: 'Main'\n  },\n  {\n    name: 'Content Management',\n    section: 'Content',\n    items: [\n      { name: 'Hero Sections', href: '/admin/hero-sections', icon: DocumentTextIcon },\n      { name: 'About Pages', href: '/admin/about-pages', icon: DocumentTextIcon },\n      { name: 'Services', href: '/admin/services', icon: CogIcon },\n      { name: 'Team Members', href: '/admin/team-members', icon: UserGroupIcon },\n      { name: 'Technologies', href: '/admin/technologies', icon: CogIcon },\n      { name: 'Testimonials', href: '/admin/testimonials', icon: ChatBubbleLeftRightIcon },\n      { name: 'Blog Posts', href: '/admin/blog', icon: DocumentTextIcon },\n      { name: 'Legal Pages', href: '/admin/legal-pages', icon: DocumentDuplicateIcon },\n    ]\n  },\n  {\n    name: 'Business',\n    section: 'Business',\n    items: [\n      { name: 'Jobs', href: '/admin/jobs', icon: BriefcaseIcon },\n      { name: 'Clients', href: '/admin/clients', icon: UserIcon },\n      { name: 'Projects', href: '/admin/projects', icon: BriefcaseIcon },\n      { name: 'Invoices', href: '/admin/invoices', icon: DocumentTextIcon },\n      { name: 'Contact Forms', href: '/admin/contact-forms', icon: ChatBubbleLeftRightIcon },\n    ]\n  },\n  {\n    name: 'System',\n    section: 'System',\n    items: [\n      { name: 'Data Upload', href: '/admin/data-upload', icon: CloudArrowUpIcon },\n      { name: 'Chatbot', href: '/admin/chatbot', icon: ChatBubbleLeftRightIcon },\n      { name: 'Users', href: '/admin/users', icon: UserGroupIcon },\n      { name: 'Settings', href: '/admin/settings', icon: CogIcon },\n    ]\n  }\n];\n\nexport default function AdminLayout({\n  children,\n}: {\n  children: React.ReactNode;\n}) {\n  const [sidebarOpen, setSidebarOpen] = useState(false);\n  const pathname = usePathname();\n\n  const isActive = (href: string) => {\n    if (href === '/admin') {\n      return pathname === '/admin';\n    }\n    return pathname.startsWith(href);\n  };\n\n  return (\n    <div className=\"min-h-screen bg-gray-50\">\n      {/* Mobile sidebar */}\n      <AnimatePresence>\n        {sidebarOpen && (\n          <>\n            <motion.div\n              initial={{ opacity: 0 }}\n              animate={{ opacity: 1 }}\n              exit={{ opacity: 0 }}\n              className=\"fixed inset-0 z-40 bg-gray-600 bg-opacity-75 lg:hidden\"\n              onClick={() => setSidebarOpen(false)}\n            />\n            <motion.div\n              initial={{ x: '-100%' }}\n              animate={{ x: 0 }}\n              exit={{ x: '-100%' }}\n              transition={{ type: 'spring', damping: 25, stiffness: 200 }}\n              className=\"fixed inset-y-0 left-0 z-50 w-64 bg-white shadow-xl lg:hidden\"\n            >\n              <div className=\"flex h-16 items-center justify-between px-4 border-b border-gray-200\">\n                <div className=\"flex items-center space-x-2\">\n                  <div className=\"h-8 w-8 bg-gradient-to-r from-blue-600 to-blue-400 rounded-lg flex items-center justify-center\">\n                    <span className=\"text-white font-bold text-sm\">T</span>\n                  </div>\n                  <div>\n                    <div className=\"text-lg font-bold text-gray-900\">Technoloway</div>\n                    <div className=\"text-xs text-gray-500\">Admin Panel</div>\n                  </div>\n                </div>\n                <button\n                  onClick={() => setSidebarOpen(false)}\n                  className=\"p-2 rounded-md text-gray-400 hover:text-gray-500\"\n                >\n                  <XMarkIcon className=\"h-6 w-6\" />\n                </button>\n              </div>\n              <nav className=\"mt-5 px-2\">\n                {navigation.map((item) => (\n                  <div key={item.name} className=\"mb-6\">\n                    {item.href ? (\n                      <Link\n                        href={item.href}\n                        className={`group flex items-center px-2 py-2 text-sm font-medium rounded-md ${\n                          isActive(item.href)\n                            ? 'bg-blue-100 text-blue-900'\n                            : 'text-gray-600 hover:bg-gray-50 hover:text-gray-900'\n                        }`}\n                        onClick={() => setSidebarOpen(false)}\n                      >\n                        <item.icon className=\"mr-3 h-6 w-6\" />\n                        {item.name}\n                      </Link>\n                    ) : (\n                      <>\n                        <div className=\"text-xs font-semibold text-gray-400 uppercase tracking-wide mb-2\">\n                          {item.section}\n                        </div>\n                        {item.items?.map((subItem) => (\n                          <Link\n                            key={subItem.name}\n                            href={subItem.href}\n                            className={`group flex items-center px-2 py-2 text-sm font-medium rounded-md ${\n                              isActive(subItem.href)\n                                ? 'bg-blue-100 text-blue-900'\n                                : 'text-gray-600 hover:bg-gray-50 hover:text-gray-900'\n                            }`}\n                            onClick={() => setSidebarOpen(false)}\n                          >\n                            <subItem.icon className=\"mr-3 h-5 w-5\" />\n                            {subItem.name}\n                          </Link>\n                        ))}\n                      </>\n                    )}\n                  </div>\n                ))}\n              </nav>\n            </motion.div>\n          </>\n        )}\n      </AnimatePresence>\n\n      {/* Desktop sidebar */}\n      <div className=\"hidden lg:fixed lg:inset-y-0 lg:flex lg:w-64 lg:flex-col\">\n        <div className=\"flex min-h-0 flex-1 flex-col bg-white border-r border-gray-200\">\n          <div className=\"flex h-16 flex-shrink-0 items-center px-4 border-b border-gray-200\">\n            <div className=\"flex items-center space-x-2\">\n              <div className=\"h-8 w-8 bg-gradient-to-r from-blue-600 to-blue-400 rounded-lg flex items-center justify-center\">\n                <span className=\"text-white font-bold text-sm\">T</span>\n              </div>\n              <div>\n                <div className=\"text-lg font-bold text-gray-900\">Technoloway</div>\n                <div className=\"text-xs text-gray-500\">Admin Panel</div>\n              </div>\n            </div>\n          </div>\n          <div className=\"flex flex-1 flex-col overflow-y-auto pt-5 pb-4\">\n            <nav className=\"mt-5 flex-1 px-2 space-y-6\">\n              {navigation.map((item) => (\n                <div key={item.name}>\n                  {item.href ? (\n                    <Link\n                      href={item.href}\n                      className={`group flex items-center px-2 py-2 text-sm font-medium rounded-md ${\n                        isActive(item.href)\n                          ? 'bg-blue-100 text-blue-900'\n                          : 'text-gray-600 hover:bg-gray-50 hover:text-gray-900'\n                      }`}\n                    >\n                      <item.icon className=\"mr-3 h-6 w-6\" />\n                      {item.name}\n                    </Link>\n                  ) : (\n                    <>\n                      <div className=\"text-xs font-semibold text-gray-400 uppercase tracking-wide mb-2\">\n                        {item.section}\n                      </div>\n                      <div className=\"space-y-1\">\n                        {item.items?.map((subItem) => (\n                          <Link\n                            key={subItem.name}\n                            href={subItem.href}\n                            className={`group flex items-center px-2 py-2 text-sm font-medium rounded-md ${\n                              isActive(subItem.href)\n                                ? 'bg-blue-100 text-blue-900'\n                                : 'text-gray-600 hover:bg-gray-50 hover:text-gray-900'\n                            }`}\n                          >\n                            <subItem.icon className=\"mr-3 h-5 w-5\" />\n                            {subItem.name}\n                          </Link>\n                        ))}\n                      </div>\n                    </>\n                  )}\n                </div>\n              ))}\n            </nav>\n          </div>\n        </div>\n      </div>\n\n      {/* Main content */}\n      <div className=\"lg:pl-64 flex flex-col flex-1\">\n        {/* Top navigation */}\n        <div className=\"sticky top-0 z-10 flex h-16 flex-shrink-0 bg-white shadow\">\n          <button\n            type=\"button\"\n            className=\"border-r border-gray-200 px-4 text-gray-500 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-blue-500 lg:hidden\"\n            onClick={() => setSidebarOpen(true)}\n          >\n            <Bars3Icon className=\"h-6 w-6\" />\n          </button>\n          <div className=\"flex flex-1 justify-between px-4\">\n            <div className=\"flex flex-1\">\n              <div className=\"flex w-full md:ml-0\">\n                <div className=\"relative w-full text-gray-400 focus-within:text-gray-600\">\n                  <div className=\"pointer-events-none absolute inset-y-0 left-0 flex items-center\">\n                    <MagnifyingGlassIcon className=\"h-5 w-5\" />\n                  </div>\n                  <input\n                    className=\"block h-full w-full border-transparent py-2 pl-8 pr-3 text-gray-900 placeholder-gray-500 focus:border-transparent focus:placeholder-gray-400 focus:outline-none focus:ring-0 sm:text-sm\"\n                    placeholder=\"Search...\"\n                    type=\"search\"\n                  />\n                </div>\n              </div>\n            </div>\n            <div className=\"ml-4 flex items-center md:ml-6\">\n              <button\n                type=\"button\"\n                className=\"rounded-full bg-white p-1 text-gray-400 hover:text-gray-500 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2\"\n              >\n                <BellIcon className=\"h-6 w-6\" />\n              </button>\n              <div className=\"relative ml-3\">\n                <button\n                  type=\"button\"\n                  className=\"flex max-w-xs items-center rounded-full bg-white text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2\"\n                >\n                  <div className=\"h-8 w-8 rounded-full bg-blue-600 flex items-center justify-center\">\n                    <span className=\"text-white text-sm font-medium\">A</span>\n                  </div>\n                </button>\n              </div>\n              <button\n                type=\"button\"\n                className=\"ml-3 rounded-full bg-white p-1 text-gray-400 hover:text-gray-500 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2\"\n              >\n                <ArrowRightOnRectangleIcon className=\"h-6 w-6\" />\n              </button>\n            </div>\n          </div>\n        </div>\n\n        {/* Page content */}\n        <main className=\"flex-1\">\n          {children}\n        </main>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;AANA;;;;;;AAwBA,MAAM,aAAa;IACjB;QACE,MAAM;QACN,MAAM;QACN,MAAM,kNAAA,CAAA,WAAQ;QACd,SAAS;IACX;IACA;QACE,MAAM;QACN,SAAS;QACT,OAAO;YACL;gBAAE,MAAM;gBAAiB,MAAM;gBAAwB,MAAM,kOAAA,CAAA,mBAAgB;YAAC;YAC9E;gBAAE,MAAM;gBAAe,MAAM;gBAAsB,MAAM,kOAAA,CAAA,mBAAgB;YAAC;YAC1E;gBAAE,MAAM;gBAAY,MAAM;gBAAmB,MAAM,gNAAA,CAAA,UAAO;YAAC;YAC3D;gBAAE,MAAM;gBAAgB,MAAM;gBAAuB,MAAM,4NAAA,CAAA,gBAAa;YAAC;YACzE;gBAAE,MAAM;gBAAgB,MAAM;gBAAuB,MAAM,gNAAA,CAAA,UAAO;YAAC;YACnE;gBAAE,MAAM;gBAAgB,MAAM;gBAAuB,MAAM,gPAAA,CAAA,0BAAuB;YAAC;YACnF;gBAAE,MAAM;gBAAc,MAAM;gBAAe,MAAM,kOAAA,CAAA,mBAAgB;YAAC;YAClE;gBAAE,MAAM;gBAAe,MAAM;gBAAsB,MAAM,4OAAA,CAAA,wBAAqB;YAAC;SAChF;IACH;IACA;QACE,MAAM;QACN,SAAS;QACT,OAAO;YACL;gBAAE,MAAM;gBAAQ,MAAM;gBAAe,MAAM,4NAAA,CAAA,gBAAa;YAAC;YACzD;gBAAE,MAAM;gBAAW,MAAM;gBAAkB,MAAM,kNAAA,CAAA,WAAQ;YAAC;YAC1D;gBAAE,MAAM;gBAAY,MAAM;gBAAmB,MAAM,4NAAA,CAAA,gBAAa;YAAC;YACjE;gBAAE,MAAM;gBAAY,MAAM;gBAAmB,MAAM,kOAAA,CAAA,mBAAgB;YAAC;YACpE;gBAAE,MAAM;gBAAiB,MAAM;gBAAwB,MAAM,gPAAA,CAAA,0BAAuB;YAAC;SACtF;IACH;IACA;QACE,MAAM;QACN,SAAS;QACT,OAAO;YACL;gBAAE,MAAM;gBAAe,MAAM;gBAAsB,MAAM,kOAAA,CAAA,mBAAgB;YAAC;YAC1E;gBAAE,MAAM;gBAAW,MAAM;gBAAkB,MAAM,gPAAA,CAAA,0BAAuB;YAAC;YACzE;gBAAE,MAAM;gBAAS,MAAM;gBAAgB,MAAM,4NAAA,CAAA,gBAAa;YAAC;YAC3D;gBAAE,MAAM;gBAAY,MAAM;gBAAmB,MAAM,gNAAA,CAAA,UAAO;YAAC;SAC5D;IACH;CACD;AAEc,SAAS,YAAY,EAClC,QAAQ,EAGT;;IACC,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,WAAW,CAAA,GAAA,qIAAA,CAAA,cAAW,AAAD;IAE3B,MAAM,WAAW,CAAC;QAChB,IAAI,SAAS,UAAU;YACrB,OAAO,aAAa;QACtB;QACA,OAAO,SAAS,UAAU,CAAC;IAC7B;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC,4LAAA,CAAA,kBAAe;0BACb,6BACC;;sCACE,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,SAAS;gCAAE,SAAS;4BAAE;4BACtB,SAAS;gCAAE,SAAS;4BAAE;4BACtB,MAAM;gCAAE,SAAS;4BAAE;4BACnB,WAAU;4BACV,SAAS,IAAM,eAAe;;;;;;sCAEhC,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,SAAS;gCAAE,GAAG;4BAAQ;4BACtB,SAAS;gCAAE,GAAG;4BAAE;4BAChB,MAAM;gCAAE,GAAG;4BAAQ;4BACnB,YAAY;gCAAE,MAAM;gCAAU,SAAS;gCAAI,WAAW;4BAAI;4BAC1D,WAAU;;8CAEV,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC;wDAAK,WAAU;kEAA+B;;;;;;;;;;;8DAEjD,6LAAC;;sEACC,6LAAC;4DAAI,WAAU;sEAAkC;;;;;;sEACjD,6LAAC;4DAAI,WAAU;sEAAwB;;;;;;;;;;;;;;;;;;sDAG3C,6LAAC;4CACC,SAAS,IAAM,eAAe;4CAC9B,WAAU;sDAEV,cAAA,6LAAC,oNAAA,CAAA,YAAS;gDAAC,WAAU;;;;;;;;;;;;;;;;;8CAGzB,6LAAC;oCAAI,WAAU;8CACZ,WAAW,GAAG,CAAC,CAAC,qBACf,6LAAC;4CAAoB,WAAU;sDAC5B,KAAK,IAAI,iBACR,6LAAC,+JAAA,CAAA,UAAI;gDACH,MAAM,KAAK,IAAI;gDACf,WAAW,CAAC,iEAAiE,EAC3E,SAAS,KAAK,IAAI,IACd,8BACA,sDACJ;gDACF,SAAS,IAAM,eAAe;;kEAE9B,6LAAC,KAAK,IAAI;wDAAC,WAAU;;;;;;oDACpB,KAAK,IAAI;;;;;;qEAGZ;;kEACE,6LAAC;wDAAI,WAAU;kEACZ,KAAK,OAAO;;;;;;oDAEd,KAAK,KAAK,EAAE,IAAI,CAAC,wBAChB,6LAAC,+JAAA,CAAA,UAAI;4DAEH,MAAM,QAAQ,IAAI;4DAClB,WAAW,CAAC,iEAAiE,EAC3E,SAAS,QAAQ,IAAI,IACjB,8BACA,sDACJ;4DACF,SAAS,IAAM,eAAe;;8EAE9B,6LAAC,QAAQ,IAAI;oEAAC,WAAU;;;;;;gEACvB,QAAQ,IAAI;;2DAVR,QAAQ,IAAI;;;;;;;2CArBjB,KAAK,IAAI;;;;;;;;;;;;;;;;;;;;;;;0BA6C/B,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CAAK,WAAU;sDAA+B;;;;;;;;;;;kDAEjD,6LAAC;;0DACC,6LAAC;gDAAI,WAAU;0DAAkC;;;;;;0DACjD,6LAAC;gDAAI,WAAU;0DAAwB;;;;;;;;;;;;;;;;;;;;;;;sCAI7C,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;0CACZ,WAAW,GAAG,CAAC,CAAC,qBACf,6LAAC;kDACE,KAAK,IAAI,iBACR,6LAAC,+JAAA,CAAA,UAAI;4CACH,MAAM,KAAK,IAAI;4CACf,WAAW,CAAC,iEAAiE,EAC3E,SAAS,KAAK,IAAI,IACd,8BACA,sDACJ;;8DAEF,6LAAC,KAAK,IAAI;oDAAC,WAAU;;;;;;gDACpB,KAAK,IAAI;;;;;;iEAGZ;;8DACE,6LAAC;oDAAI,WAAU;8DACZ,KAAK,OAAO;;;;;;8DAEf,6LAAC;oDAAI,WAAU;8DACZ,KAAK,KAAK,EAAE,IAAI,CAAC,wBAChB,6LAAC,+JAAA,CAAA,UAAI;4DAEH,MAAM,QAAQ,IAAI;4DAClB,WAAW,CAAC,iEAAiE,EAC3E,SAAS,QAAQ,IAAI,IACjB,8BACA,sDACJ;;8EAEF,6LAAC,QAAQ,IAAI;oEAAC,WAAU;;;;;;gEACvB,QAAQ,IAAI;;2DATR,QAAQ,IAAI;;;;;;;;;;;;uCArBnB,KAAK,IAAI;;;;;;;;;;;;;;;;;;;;;;;;;;0BA4C7B,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCACC,MAAK;gCACL,WAAU;gCACV,SAAS,IAAM,eAAe;0CAE9B,cAAA,6LAAC,oNAAA,CAAA,YAAS;oCAAC,WAAU;;;;;;;;;;;0CAEvB,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;kEACb,cAAA,6LAAC,wOAAA,CAAA,sBAAmB;4DAAC,WAAU;;;;;;;;;;;kEAEjC,6LAAC;wDACC,WAAU;wDACV,aAAY;wDACZ,MAAK;;;;;;;;;;;;;;;;;;;;;;kDAKb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDACC,MAAK;gDACL,WAAU;0DAEV,cAAA,6LAAC,kNAAA,CAAA,WAAQ;oDAAC,WAAU;;;;;;;;;;;0DAEtB,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDACC,MAAK;oDACL,WAAU;8DAEV,cAAA,6LAAC;wDAAI,WAAU;kEACb,cAAA,6LAAC;4DAAK,WAAU;sEAAiC;;;;;;;;;;;;;;;;;;;;;0DAIvD,6LAAC;gDACC,MAAK;gDACL,WAAU;0DAEV,cAAA,6LAAC,oPAAA,CAAA,4BAAyB;oDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAO7C,6LAAC;wBAAK,WAAU;kCACb;;;;;;;;;;;;;;;;;;AAKX;GA1NwB;;QAML,qIAAA,CAAA,cAAW;;;KANN", "debugId": null}}]}