{"version": 3, "sources": [], "sections": [{"offset": {"line": 39, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Project%20By%20AI/Technoloway/Technoloway%20%28Processing%29/technoloway-simple/src/components/header.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport Link from 'next/link';\nimport { usePathname } from 'next/navigation';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport { Bars3Icon, XMarkIcon } from '@heroicons/react/24/outline';\n\nconst navigation = [\n  { name: 'Home', href: '/' },\n  { name: 'About', href: '/about' },\n  { name: 'Services', href: '/services' },\n  { name: 'Projects', href: '/projects' },\n  { name: 'Technologies', href: '/technologies' },\n  { name: 'Contact', href: '/contact' },\n];\n\nexport function Header() {\n  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);\n  const pathname = usePathname();\n\n  const isActive = (href: string) => {\n    if (href === '/') {\n      return pathname === '/';\n    }\n    return pathname.startsWith(href);\n  };\n\n  return (\n    <header className=\"fixed inset-x-0 top-0 z-50 bg-white/80 backdrop-blur-md border-b border-gray-200\">\n      <nav className=\"container flex items-center justify-between py-4\" aria-label=\"Global\">\n        <div className=\"flex lg:flex-1\">\n          <Link href=\"/\" className=\"-m-1.5 p-1.5\">\n            <span className=\"sr-only\">Technoloway</span>\n            <div className=\"flex items-center space-x-2\">\n              <div className=\"h-8 w-8 bg-gradient-to-r from-blue-600 to-blue-400 rounded-lg flex items-center justify-center\">\n                <span className=\"text-white font-bold text-sm\">T</span>\n              </div>\n              <span className=\"text-xl font-bold text-gray-900\">Technoloway</span>\n            </div>\n          </Link>\n        </div>\n        \n        <div className=\"flex lg:hidden\">\n          <button\n            type=\"button\"\n            className=\"-m-2.5 inline-flex items-center justify-center rounded-md p-2.5 text-gray-700\"\n            onClick={() => setMobileMenuOpen(true)}\n          >\n            <span className=\"sr-only\">Open main menu</span>\n            <Bars3Icon className=\"h-6 w-6\" aria-hidden=\"true\" />\n          </button>\n        </div>\n        \n        <div className=\"hidden lg:flex lg:gap-x-8\">\n          {navigation.map((item) => (\n            <Link\n              key={item.name}\n              href={item.href}\n              className={`text-sm font-semibold leading-6 transition-colors hover:text-blue-600 ${\n                isActive(item.href) ? 'text-blue-600' : 'text-gray-900'\n              }`}\n            >\n              {item.name}\n            </Link>\n          ))}\n        </div>\n        \n        <div className=\"hidden lg:flex lg:flex-1 lg:justify-end\">\n          <Link href=\"/contact\" className=\"btn-primary\">\n            Get Started\n          </Link>\n        </div>\n      </nav>\n      \n      {/* Mobile menu */}\n      <AnimatePresence>\n        {mobileMenuOpen && (\n          <motion.div\n            initial={{ opacity: 0 }}\n            animate={{ opacity: 1 }}\n            exit={{ opacity: 0 }}\n            className=\"lg:hidden\"\n          >\n            <div className=\"fixed inset-0 z-50\" />\n            <motion.div\n              initial={{ x: '100%' }}\n              animate={{ x: 0 }}\n              exit={{ x: '100%' }}\n              transition={{ type: 'spring', damping: 25, stiffness: 200 }}\n              className=\"fixed inset-y-0 right-0 z-50 w-full overflow-y-auto bg-white px-6 py-6 sm:max-w-sm sm:ring-1 sm:ring-gray-900/10\"\n            >\n              <div className=\"flex items-center justify-between\">\n                <Link href=\"/\" className=\"-m-1.5 p-1.5\">\n                  <span className=\"sr-only\">Technoloway</span>\n                  <div className=\"flex items-center space-x-2\">\n                    <div className=\"h-8 w-8 bg-gradient-to-r from-blue-600 to-blue-400 rounded-lg flex items-center justify-center\">\n                      <span className=\"text-white font-bold text-sm\">T</span>\n                    </div>\n                    <span className=\"text-xl font-bold text-gray-900\">Technoloway</span>\n                  </div>\n                </Link>\n                <button\n                  type=\"button\"\n                  className=\"-m-2.5 rounded-md p-2.5 text-gray-700\"\n                  onClick={() => setMobileMenuOpen(false)}\n                >\n                  <span className=\"sr-only\">Close menu</span>\n                  <XMarkIcon className=\"h-6 w-6\" aria-hidden=\"true\" />\n                </button>\n              </div>\n              <div className=\"mt-6 flow-root\">\n                <div className=\"-my-6 divide-y divide-gray-500/10\">\n                  <div className=\"space-y-2 py-6\">\n                    {navigation.map((item) => (\n                      <Link\n                        key={item.name}\n                        href={item.href}\n                        className={`-mx-3 block rounded-lg px-3 py-2 text-base font-semibold leading-7 hover:bg-gray-50 ${\n                          isActive(item.href) ? 'text-blue-600' : 'text-gray-900'\n                        }`}\n                        onClick={() => setMobileMenuOpen(false)}\n                      >\n                        {item.name}\n                      </Link>\n                    ))}\n                  </div>\n                  <div className=\"py-6\">\n                    <Link\n                      href=\"/contact\"\n                      className=\"-mx-3 block rounded-lg px-3 py-2.5 text-base font-semibold leading-7 text-blue-600 hover:bg-gray-50\"\n                      onClick={() => setMobileMenuOpen(false)}\n                    >\n                      Get Started\n                    </Link>\n                  </div>\n                </div>\n              </div>\n            </motion.div>\n          </motion.div>\n        )}\n      </AnimatePresence>\n    </header>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AACA;AAAA;AANA;;;;;;;AAQA,MAAM,aAAa;IACjB;QAAE,MAAM;QAAQ,MAAM;IAAI;IAC1B;QAAE,MAAM;QAAS,MAAM;IAAS;IAChC;QAAE,MAAM;QAAY,MAAM;IAAY;IACtC;QAAE,MAAM;QAAY,MAAM;IAAY;IACtC;QAAE,MAAM;QAAgB,MAAM;IAAgB;IAC9C;QAAE,MAAM;QAAW,MAAM;IAAW;CACrC;AAEM,SAAS;IACd,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,WAAW,CAAA,GAAA,kIAAA,CAAA,cAAW,AAAD;IAE3B,MAAM,WAAW,CAAC;QAChB,IAAI,SAAS,KAAK;YAChB,OAAO,aAAa;QACtB;QACA,OAAO,SAAS,UAAU,CAAC;IAC7B;IAEA,qBACE,8OAAC;QAAO,WAAU;;0BAChB,8OAAC;gBAAI,WAAU;gBAAmD,cAAW;;kCAC3E,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC,4JAAA,CAAA,UAAI;4BAAC,MAAK;4BAAI,WAAU;;8CACvB,8OAAC;oCAAK,WAAU;8CAAU;;;;;;8CAC1B,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAK,WAAU;0DAA+B;;;;;;;;;;;sDAEjD,8OAAC;4CAAK,WAAU;sDAAkC;;;;;;;;;;;;;;;;;;;;;;;kCAKxD,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BACC,MAAK;4BACL,WAAU;4BACV,SAAS,IAAM,kBAAkB;;8CAEjC,8OAAC;oCAAK,WAAU;8CAAU;;;;;;8CAC1B,8OAAC,iNAAA,CAAA,YAAS;oCAAC,WAAU;oCAAU,eAAY;;;;;;;;;;;;;;;;;kCAI/C,8OAAC;wBAAI,WAAU;kCACZ,WAAW,GAAG,CAAC,CAAC,qBACf,8OAAC,4JAAA,CAAA,UAAI;gCAEH,MAAM,KAAK,IAAI;gCACf,WAAW,CAAC,sEAAsE,EAChF,SAAS,KAAK,IAAI,IAAI,kBAAkB,iBACxC;0CAED,KAAK,IAAI;+BANL,KAAK,IAAI;;;;;;;;;;kCAWpB,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC,4JAAA,CAAA,UAAI;4BAAC,MAAK;4BAAW,WAAU;sCAAc;;;;;;;;;;;;;;;;;0BAOlD,8OAAC,yLAAA,CAAA,kBAAe;0BACb,gCACC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;oBAAE;oBACtB,SAAS;wBAAE,SAAS;oBAAE;oBACtB,MAAM;wBAAE,SAAS;oBAAE;oBACnB,WAAU;;sCAEV,8OAAC;4BAAI,WAAU;;;;;;sCACf,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,SAAS;gCAAE,GAAG;4BAAO;4BACrB,SAAS;gCAAE,GAAG;4BAAE;4BAChB,MAAM;gCAAE,GAAG;4BAAO;4BAClB,YAAY;gCAAE,MAAM;gCAAU,SAAS;gCAAI,WAAW;4BAAI;4BAC1D,WAAU;;8CAEV,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,4JAAA,CAAA,UAAI;4CAAC,MAAK;4CAAI,WAAU;;8DACvB,8OAAC;oDAAK,WAAU;8DAAU;;;;;;8DAC1B,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;sEACb,cAAA,8OAAC;gEAAK,WAAU;0EAA+B;;;;;;;;;;;sEAEjD,8OAAC;4DAAK,WAAU;sEAAkC;;;;;;;;;;;;;;;;;;sDAGtD,8OAAC;4CACC,MAAK;4CACL,WAAU;4CACV,SAAS,IAAM,kBAAkB;;8DAEjC,8OAAC;oDAAK,WAAU;8DAAU;;;;;;8DAC1B,8OAAC,iNAAA,CAAA,YAAS;oDAAC,WAAU;oDAAU,eAAY;;;;;;;;;;;;;;;;;;8CAG/C,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;0DACZ,WAAW,GAAG,CAAC,CAAC,qBACf,8OAAC,4JAAA,CAAA,UAAI;wDAEH,MAAM,KAAK,IAAI;wDACf,WAAW,CAAC,oFAAoF,EAC9F,SAAS,KAAK,IAAI,IAAI,kBAAkB,iBACxC;wDACF,SAAS,IAAM,kBAAkB;kEAEhC,KAAK,IAAI;uDAPL,KAAK,IAAI;;;;;;;;;;0DAWpB,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC,4JAAA,CAAA,UAAI;oDACH,MAAK;oDACL,WAAU;oDACV,SAAS,IAAM,kBAAkB;8DAClC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAYrB", "debugId": null}}, {"offset": {"line": 431, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Project%20By%20AI/Technoloway/Technoloway%20%28Processing%29/technoloway-simple/src/components/footer.tsx"], "sourcesContent": ["import Link from 'next/link';\n\nexport function Footer() {\n  return (\n    <footer className=\"bg-gray-900 text-white\">\n      <div className=\"container py-16\">\n        <div className=\"grid grid-cols-1 md:grid-cols-4 gap-8\">\n          <div className=\"col-span-1 md:col-span-2\">\n            <div className=\"flex items-center space-x-2 mb-6\">\n              <div className=\"h-8 w-8 bg-gradient-to-r from-blue-600 to-blue-400 rounded-lg flex items-center justify-center\">\n                <span className=\"text-white font-bold text-sm\">T</span>\n              </div>\n              <span className=\"text-xl font-bold\">Technoloway</span>\n            </div>\n            <p className=\"text-gray-300 mb-6 max-w-md\">\n              Transforming ideas into digital reality. We build exceptional software \n              solutions that drive business growth and innovation.\n            </p>\n            <div className=\"space-y-2\">\n              <p className=\"text-gray-300\"><EMAIL></p>\n              <p className=\"text-gray-300\">+****************</p>\n              <p className=\"text-gray-300\">San Francisco, CA</p>\n            </div>\n          </div>\n          \n          <div>\n            <h3 className=\"text-lg font-semibold mb-4\">Services</h3>\n            <ul className=\"space-y-2 text-gray-300\">\n              <li><Link href=\"/#services\" className=\"hover:text-white transition-colors\">Web Development</Link></li>\n              <li><Link href=\"/#services\" className=\"hover:text-white transition-colors\">Mobile Apps</Link></li>\n              <li><Link href=\"/#services\" className=\"hover:text-white transition-colors\">Cloud Solutions</Link></li>\n              <li><Link href=\"/#services\" className=\"hover:text-white transition-colors\">API Development</Link></li>\n            </ul>\n          </div>\n          \n          <div>\n            <h3 className=\"text-lg font-semibold mb-4\">Company</h3>\n            <ul className=\"space-y-2 text-gray-300\">\n              <li><Link href=\"/team\" className=\"hover:text-white transition-colors\">About</Link></li>\n              <li><Link href=\"/team\" className=\"hover:text-white transition-colors\">Team</Link></li>\n              <li><Link href=\"/portfolio\" className=\"hover:text-white transition-colors\">Portfolio</Link></li>\n              <li><Link href=\"/blog\" className=\"hover:text-white transition-colors\">Blog</Link></li>\n              <li><Link href=\"/#contact\" className=\"hover:text-white transition-colors\">Contact</Link></li>\n            </ul>\n          </div>\n        </div>\n        \n        <div className=\"border-t border-gray-700 mt-12 pt-8 text-center\">\n          <p className=\"text-gray-400\">\n            &copy; {new Date().getFullYear()} Technoloway. All rights reserved.\n          </p>\n        </div>\n      </div>\n    </footer>\n  );\n}\n"], "names": [], "mappings": ";;;;AAAA;;;AAEO,SAAS;IACd,qBACE,8OAAC;QAAO,WAAU;kBAChB,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAK,WAAU;0DAA+B;;;;;;;;;;;sDAEjD,8OAAC;4CAAK,WAAU;sDAAoB;;;;;;;;;;;;8CAEtC,8OAAC;oCAAE,WAAU;8CAA8B;;;;;;8CAI3C,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAE,WAAU;sDAAgB;;;;;;sDAC7B,8OAAC;4CAAE,WAAU;sDAAgB;;;;;;sDAC7B,8OAAC;4CAAE,WAAU;sDAAgB;;;;;;;;;;;;;;;;;;sCAIjC,8OAAC;;8CACC,8OAAC;oCAAG,WAAU;8CAA6B;;;;;;8CAC3C,8OAAC;oCAAG,WAAU;;sDACZ,8OAAC;sDAAG,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAa,WAAU;0DAAqC;;;;;;;;;;;sDAC3E,8OAAC;sDAAG,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAa,WAAU;0DAAqC;;;;;;;;;;;sDAC3E,8OAAC;sDAAG,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAa,WAAU;0DAAqC;;;;;;;;;;;sDAC3E,8OAAC;sDAAG,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAa,WAAU;0DAAqC;;;;;;;;;;;;;;;;;;;;;;;sCAI/E,8OAAC;;8CACC,8OAAC;oCAAG,WAAU;8CAA6B;;;;;;8CAC3C,8OAAC;oCAAG,WAAU;;sDACZ,8OAAC;sDAAG,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAQ,WAAU;0DAAqC;;;;;;;;;;;sDACtE,8OAAC;sDAAG,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAQ,WAAU;0DAAqC;;;;;;;;;;;sDACtE,8OAAC;sDAAG,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAa,WAAU;0DAAqC;;;;;;;;;;;sDAC3E,8OAAC;sDAAG,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAQ,WAAU;0DAAqC;;;;;;;;;;;sDACtE,8OAAC;sDAAG,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAY,WAAU;0DAAqC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAKhF,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAE,WAAU;;4BAAgB;4BACnB,IAAI,OAAO,WAAW;4BAAG;;;;;;;;;;;;;;;;;;;;;;;AAM7C", "debugId": null}}, {"offset": {"line": 757, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Project%20By%20AI/Technoloway/Technoloway%20%28Processing%29/technoloway-simple/src/app/team/page.tsx"], "sourcesContent": ["'use client';\n\nimport { motion } from 'framer-motion';\nimport Link from 'next/link';\nimport Image from 'next/image';\nimport {\n  EnvelopeIcon,\n  MapPinIcon,\n  AcademicCapIcon,\n  BriefcaseIcon,\n  HeartIcon,\n  CodeBracketIcon,\n  DevicePhoneMobileIcon,\n  CloudIcon,\n  ChartBarIcon,\n  ShieldCheckIcon,\n  CogIcon\n} from '@heroicons/react/24/outline';\nimport { Header } from '@/components/header';\nimport { Footer } from '@/components/footer';\n\nconst teamMembers = [\n  {\n    id: 1,\n    name: '<PERSON>',\n    role: 'CEO & Co-Founder',\n    department: 'Leadership',\n    bio: 'Visionary leader with 15+ years in tech. <PERSON> founded Technoloway with a mission to democratize cutting-edge software development.',\n    image: 'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=400&h=400&fit=crop&crop=face',\n    location: 'San Francisco, CA',\n    email: '<EMAIL>',\n    linkedin: 'https://linkedin.com/in/sarah<PERSON><PERSON><PERSON>',\n    twitter: 'https://twitter.com/sarah<PERSON><PERSON><PERSON>',\n    github: 'https://github.com/sarahjo<PERSON>son',\n    skills: ['Strategic Planning', 'Team Leadership', 'Product Vision', 'Business Development'],\n    education: 'MBA Stanford, BS Computer Science MIT',\n    experience: '15+ years',\n    languages: ['English', 'Spanish', 'French'],\n    interests: ['AI Ethics', 'Sustainable Tech', 'Mentoring'],\n    icon: BriefcaseIcon,\n    featured: true,\n  },\n  {\n    id: 2,\n    name: 'Mike Chen',\n    role: 'CTO & Co-Founder',\n    department: 'Engineering',\n    bio: 'Full-stack architect passionate about scalable systems. Mike leads our technical vision and ensures we stay at the forefront of technology.',\n    image: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=400&h=400&fit=crop&crop=face',\n    location: 'San Francisco, CA',\n    email: '<EMAIL>',\n    linkedin: 'https://linkedin.com/in/mikechen',\n    twitter: 'https://twitter.com/mikechen',\n    github: 'https://github.com/mikechen',\n    skills: ['System Architecture', 'Cloud Computing', 'DevOps', 'Team Management'],\n    education: 'MS Computer Science Stanford, BS Engineering UC Berkeley',\n    experience: '12+ years',\n    languages: ['English', 'Mandarin', 'Japanese'],\n    interests: ['Open Source', 'Cloud Architecture', 'Photography'],\n    icon: CloudIcon,\n    featured: true,\n  },\n  {\n    id: 3,\n    name: 'Emily Davis',\n    role: 'Lead Frontend Developer',\n    department: 'Engineering',\n    bio: 'UI/UX enthusiast who crafts beautiful, accessible user experiences. Emily ensures our applications are both functional and delightful.',\n    image: 'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=400&h=400&fit=crop&crop=face',\n    location: 'Austin, TX',\n    email: '<EMAIL>',\n    linkedin: 'https://linkedin.com/in/emilydavis',\n    twitter: 'https://twitter.com/emilydavis',\n    github: 'https://github.com/emilydavis',\n    skills: ['React', 'TypeScript', 'UI/UX Design', 'Accessibility'],\n    education: 'BS Computer Science UT Austin, UX Design Certification',\n    experience: '8+ years',\n    languages: ['English', 'German'],\n    interests: ['Design Systems', 'Accessibility', 'Digital Art'],\n    icon: CodeBracketIcon,\n    featured: false,\n  },\n  {\n    id: 4,\n    name: 'David Rodriguez',\n    role: 'Senior Backend Developer',\n    department: 'Engineering',\n    bio: 'Database wizard and API architect. David builds the robust backend systems that power our applications.',\n    image: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=400&h=400&fit=crop&crop=face',\n    location: 'Miami, FL',\n    email: '<EMAIL>',\n    linkedin: 'https://linkedin.com/in/davidrodriguez',\n    twitter: 'https://twitter.com/davidrodriguez',\n    github: 'https://github.com/davidrodriguez',\n    skills: ['Node.js', 'Python', 'PostgreSQL', 'Microservices'],\n    education: 'MS Software Engineering, BS Computer Science FIU',\n    experience: '10+ years',\n    languages: ['English', 'Spanish', 'Portuguese'],\n    interests: ['Database Optimization', 'Distributed Systems', 'Soccer'],\n    icon: CogIcon,\n    featured: false,\n  },\n  {\n    id: 5,\n    name: 'Lisa Wang',\n    role: 'Mobile Development Lead',\n    department: 'Engineering',\n    bio: 'Cross-platform mobile expert who creates seamless experiences across iOS and Android. Lisa leads our mobile innovation.',\n    image: 'https://images.unsplash.com/photo-1534528741775-53994a69daeb?w=400&h=400&fit=crop&crop=face',\n    location: 'Seattle, WA',\n    email: '<EMAIL>',\n    linkedin: 'https://linkedin.com/in/lisawang',\n    twitter: 'https://twitter.com/lisawang',\n    github: 'https://github.com/lisawang',\n    skills: ['React Native', 'Flutter', 'iOS', 'Android'],\n    education: 'MS Mobile Computing, BS Computer Science UW',\n    experience: '9+ years',\n    languages: ['English', 'Mandarin', 'Korean'],\n    interests: ['Mobile UX', 'AR/VR', 'Travel'],\n    icon: DevicePhoneMobileIcon,\n    featured: false,\n  },\n  {\n    id: 6,\n    name: 'Alex Thompson',\n    role: 'Security Engineer',\n    department: 'Security',\n    bio: 'Cybersecurity expert who keeps our systems and client data safe. Alex implements best practices for secure development.',\n    image: 'https://images.unsplash.com/photo-1560250097-0b93528c311a?w=400&h=400&fit=crop&crop=face',\n    location: 'Boston, MA',\n    email: '<EMAIL>',\n    linkedin: 'https://linkedin.com/in/alexthompson',\n    twitter: 'https://twitter.com/alexthompson',\n    github: 'https://github.com/alexthompson',\n    skills: ['Cybersecurity', 'Penetration Testing', 'DevSecOps', 'Compliance'],\n    education: 'MS Cybersecurity MIT, BS Computer Science Harvard',\n    experience: '11+ years',\n    languages: ['English', 'Russian'],\n    interests: ['Ethical Hacking', 'Privacy Rights', 'Chess'],\n    icon: ShieldCheckIcon,\n    featured: false,\n  },\n  {\n    id: 7,\n    name: 'Maria Garcia',\n    role: 'Data Scientist',\n    department: 'Analytics',\n    bio: 'AI and machine learning specialist who turns data into insights. Maria helps our clients make data-driven decisions.',\n    image: 'https://images.unsplash.com/photo-1573496359142-b8d87734a5a2?w=400&h=400&fit=crop&crop=face',\n    location: 'Los Angeles, CA',\n    email: '<EMAIL>',\n    linkedin: 'https://linkedin.com/in/mariagarcia',\n    twitter: 'https://twitter.com/mariagarcia',\n    github: 'https://github.com/mariagarcia',\n    skills: ['Machine Learning', 'Python', 'TensorFlow', 'Data Visualization'],\n    education: 'PhD Data Science UCLA, MS Statistics Caltech',\n    experience: '7+ years',\n    languages: ['English', 'Spanish', 'Italian'],\n    interests: ['AI Ethics', 'Climate Data', 'Salsa Dancing'],\n    icon: ChartBarIcon,\n    featured: false,\n  },\n  {\n    id: 8,\n    name: 'James Wilson',\n    role: 'DevOps Engineer',\n    department: 'Infrastructure',\n    bio: 'Cloud infrastructure expert who ensures our applications scale seamlessly. James automates everything for maximum efficiency.',\n    image: 'https://images.unsplash.com/photo-1519085360753-af0119f7cbe7?w=400&h=400&fit=crop&crop=face',\n    location: 'Denver, CO',\n    email: '<EMAIL>',\n    linkedin: 'https://linkedin.com/in/jameswilson',\n    twitter: 'https://twitter.com/jameswilson',\n    github: 'https://github.com/jameswilson',\n    skills: ['AWS', 'Kubernetes', 'Docker', 'CI/CD'],\n    education: 'BS Computer Engineering Colorado State',\n    experience: '6+ years',\n    languages: ['English'],\n    interests: ['Automation', 'Mountain Biking', 'Craft Beer'],\n    icon: CloudIcon,\n    featured: false,\n  },\n];\n\nconst departments = ['All', 'Leadership', 'Engineering', 'Security', 'Analytics', 'Infrastructure'];\n\nexport default function TeamPage() {\n  const featuredMembers = teamMembers.filter(member => member.featured);\n  const allMembers = teamMembers;\n\n  return (\n    <div className=\"min-h-screen bg-white\">\n      <Header />\n      {/* Header */}\n      <header className=\"bg-gray-50 py-20 pt-32\">\n        <div className=\"container\">\n          <motion.div\n            initial={{ opacity: 0, y: 20 }}\n            animate={{ opacity: 1, y: 0 }}\n            transition={{ duration: 0.6 }}\n            className=\"text-center\"\n          >\n            <h1 className=\"text-4xl font-bold tracking-tight text-gray-900 sm:text-5xl lg:text-6xl\">\n              Meet Our <span className=\"gradient-text\">Team</span>\n            </h1>\n            <p className=\"mt-6 text-xl text-gray-600 max-w-3xl mx-auto\">\n              We're a diverse group of passionate developers, designers, and innovators \n              committed to building exceptional software solutions that make a difference.\n            </p>\n          </motion.div>\n        </div>\n      </header>\n\n      <main className=\"container py-16\">\n        {/* Leadership Section */}\n        <motion.section\n          initial={{ opacity: 0, y: 20 }}\n          animate={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.6, delay: 0.2 }}\n          className=\"mb-20\"\n        >\n          <h2 className=\"text-3xl font-bold text-gray-900 mb-12 text-center\">Leadership</h2>\n          <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-12 max-w-6xl mx-auto\">\n            {featuredMembers.map((member, index) => (\n              <motion.div\n                key={member.id}\n                initial={{ opacity: 0, y: 20 }}\n                animate={{ opacity: 1, y: 0 }}\n                transition={{ duration: 0.6, delay: 0.1 * index }}\n                className=\"bg-white rounded-2xl shadow-lg p-8 border border-gray-200\"\n              >\n                <div className=\"flex flex-col items-center text-center\">\n                  <div className=\"relative w-32 h-32 mb-6\">\n                    <Image\n                      src={member.image}\n                      alt={member.name}\n                      fill\n                      className=\"object-cover rounded-full\"\n                    />\n                  </div>\n                  \n                  <h3 className=\"text-2xl font-bold text-gray-900 mb-2\">\n                    {member.name}\n                  </h3>\n                  \n                  <p className=\"text-blue-600 font-medium mb-4\">\n                    {member.role}\n                  </p>\n                  \n                  <p className=\"text-gray-600 mb-6 leading-relaxed\">\n                    {member.bio}\n                  </p>\n                  \n                  <div className=\"grid grid-cols-2 gap-4 w-full mb-6 text-sm\">\n                    <div className=\"text-left\">\n                      <p className=\"text-gray-500 mb-1\">Experience</p>\n                      <p className=\"font-medium\">{member.experience}</p>\n                    </div>\n                    <div className=\"text-left\">\n                      <p className=\"text-gray-500 mb-1\">Location</p>\n                      <p className=\"font-medium\">{member.location}</p>\n                    </div>\n                  </div>\n                  \n                  <div className=\"flex flex-wrap gap-2 mb-6\">\n                    {member.skills.slice(0, 4).map((skill) => (\n                      <span\n                        key={skill}\n                        className=\"px-3 py-1 bg-blue-100 text-blue-800 text-sm rounded-full\"\n                      >\n                        {skill}\n                      </span>\n                    ))}\n                  </div>\n                  \n                  <div className=\"flex space-x-4\">\n                    <Link\n                      href={`mailto:${member.email}`}\n                      className=\"p-2 text-gray-400 hover:text-blue-600 transition-colors\"\n                    >\n                      <EnvelopeIcon className=\"w-5 h-5\" />\n                    </Link>\n                    <Link\n                      href={member.linkedin}\n                      target=\"_blank\"\n                      rel=\"noopener noreferrer\"\n                      className=\"p-2 text-gray-400 hover:text-blue-600 transition-colors\"\n                    >\n                      <svg className=\"w-5 h-5\" fill=\"currentColor\" viewBox=\"0 0 24 24\">\n                        <path d=\"M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433c-1.144 0-2.063-.926-2.063-2.065 0-1.138.92-2.063 2.063-2.063 1.14 0 2.064.925 2.064 2.063 0 1.139-.925 2.065-2.064 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z\"/>\n                      </svg>\n                    </Link>\n                    <Link\n                      href={member.github}\n                      target=\"_blank\"\n                      rel=\"noopener noreferrer\"\n                      className=\"p-2 text-gray-400 hover:text-blue-600 transition-colors\"\n                    >\n                      <svg className=\"w-5 h-5\" fill=\"currentColor\" viewBox=\"0 0 24 24\">\n                        <path fillRule=\"evenodd\" d=\"M12 2C6.477 2 2 6.484 2 12.017c0 4.425 2.865 8.18 6.839 9.504.5.092.682-.217.682-.483 0-.237-.008-.868-.013-1.703-2.782.605-3.369-1.343-3.369-1.343-.454-1.158-1.11-1.466-1.11-1.466-.908-.62.069-.608.069-.608 1.003.07 1.531 1.032 1.531 1.032.892 1.53 2.341 1.088 2.91.832.092-.647.35-1.088.636-1.338-2.22-.253-4.555-1.113-4.555-4.951 0-1.093.39-1.988 1.029-2.688-.103-.253-.446-1.272.098-2.65 0 0 .84-.27 2.75 1.026A9.564 9.564 0 0112 6.844c.85.004 1.705.115 2.504.337 1.909-1.296 2.747-1.027 2.747-1.027.546 1.379.202 2.398.1 2.651.64.7 1.028 1.595 1.028 2.688 0 3.848-2.339 4.695-4.566 4.943.359.309.678.92.678 1.855 0 1.338-.012 2.419-.012 2.747 0 .268.18.58.688.482A10.019 10.019 0 0022 12.017C22 6.484 17.522 2 12 2z\" clipRule=\"evenodd\"/>\n                      </svg>\n                    </Link>\n                  </div>\n                </div>\n              </motion.div>\n            ))}\n          </div>\n        </motion.section>\n\n        {/* Department Filter */}\n        <motion.section\n          initial={{ opacity: 0, y: 20 }}\n          animate={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.6, delay: 0.4 }}\n          className=\"mb-12\"\n        >\n          <div className=\"flex flex-wrap gap-2 justify-center\">\n            {departments.map((department) => (\n              <button\n                key={department}\n                className=\"px-4 py-2 rounded-full text-sm font-medium border border-gray-300 hover:border-blue-500 hover:text-blue-600 transition-colors\"\n              >\n                {department}\n              </button>\n            ))}\n          </div>\n        </motion.section>\n\n        {/* All Team Members */}\n        <motion.section\n          initial={{ opacity: 0, y: 20 }}\n          animate={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.6, delay: 0.5 }}\n        >\n          <h2 className=\"text-3xl font-bold text-gray-900 mb-12 text-center\">Our Team</h2>\n          <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-8\">\n            {allMembers.map((member, index) => (\n              <motion.div\n                key={member.id}\n                initial={{ opacity: 0, y: 20 }}\n                animate={{ opacity: 1, y: 0 }}\n                transition={{ duration: 0.6, delay: 0.05 * index }}\n                className=\"bg-white rounded-xl shadow-md p-6 border border-gray-200 hover:shadow-lg transition-shadow group\"\n              >\n                <div className=\"text-center\">\n                  <div className=\"relative w-20 h-20 mx-auto mb-4\">\n                    <Image\n                      src={member.image}\n                      alt={member.name}\n                      fill\n                      className=\"object-cover rounded-full\"\n                    />\n                  </div>\n                  \n                  <h3 className=\"text-lg font-semibold text-gray-900 mb-1\">\n                    {member.name}\n                  </h3>\n                  \n                  <p className=\"text-blue-600 text-sm font-medium mb-2\">\n                    {member.role}\n                  </p>\n                  \n                  <div className=\"flex items-center justify-center text-gray-500 text-sm mb-3\">\n                    <MapPinIcon className=\"w-4 h-4 mr-1\" />\n                    {member.location}\n                  </div>\n                  \n                  <p className=\"text-gray-600 text-sm mb-4 line-clamp-3\">\n                    {member.bio}\n                  </p>\n                  \n                  <div className=\"flex flex-wrap gap-1 mb-4\">\n                    {member.skills.slice(0, 2).map((skill) => (\n                      <span\n                        key={skill}\n                        className=\"px-2 py-1 bg-gray-100 text-gray-600 text-xs rounded\"\n                      >\n                        {skill}\n                      </span>\n                    ))}\n                    {member.skills.length > 2 && (\n                      <span className=\"px-2 py-1 bg-gray-100 text-gray-600 text-xs rounded\">\n                        +{member.skills.length - 2}\n                      </span>\n                    )}\n                  </div>\n                  \n                  <div className=\"flex justify-center space-x-3 opacity-0 group-hover:opacity-100 transition-opacity\">\n                    <Link\n                      href={`mailto:${member.email}`}\n                      className=\"p-1 text-gray-400 hover:text-blue-600 transition-colors\"\n                    >\n                      <EnvelopeIcon className=\"w-4 h-4\" />\n                    </Link>\n                    <Link\n                      href={member.linkedin}\n                      target=\"_blank\"\n                      rel=\"noopener noreferrer\"\n                      className=\"p-1 text-gray-400 hover:text-blue-600 transition-colors\"\n                    >\n                      <svg className=\"w-4 h-4\" fill=\"currentColor\" viewBox=\"0 0 24 24\">\n                        <path d=\"M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433c-1.144 0-2.063-.926-2.063-2.065 0-1.138.92-2.063 2.063-2.063 1.14 0 2.064.925 2.064 2.063 0 1.139-.925 2.065-2.064 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z\"/>\n                      </svg>\n                    </Link>\n                    <Link\n                      href={member.github}\n                      target=\"_blank\"\n                      rel=\"noopener noreferrer\"\n                      className=\"p-1 text-gray-400 hover:text-blue-600 transition-colors\"\n                    >\n                      <svg className=\"w-4 h-4\" fill=\"currentColor\" viewBox=\"0 0 24 24\">\n                        <path fillRule=\"evenodd\" d=\"M12 2C6.477 2 2 6.484 2 12.017c0 4.425 2.865 8.18 6.839 9.504.5.092.682-.217.682-.483 0-.237-.008-.868-.013-1.703-2.782.605-3.369-1.343-3.369-1.343-.454-1.158-1.11-1.466-1.11-1.466-.908-.62.069-.608.069-.608 1.003.07 1.531 1.032 1.531 1.032.892 1.53 2.341 1.088 2.91.832.092-.647.35-1.088.636-1.338-2.22-.253-4.555-1.113-4.555-4.951 0-1.093.39-1.988 1.029-2.688-.103-.253-.446-1.272.098-2.65 0 0 .84-.27 2.75 1.026A9.564 9.564 0 0112 6.844c.85.004 1.705.115 2.504.337 1.909-1.296 2.747-1.027 2.747-1.027.546 1.379.202 2.398.1 2.651.64.7 1.028 1.595 1.028 2.688 0 3.848-2.339 4.695-4.566 4.943.359.309.678.92.678 1.855 0 1.338-.012 2.419-.012 2.747 0 .268.18.58.688.482A10.019 10.019 0 0022 12.017C22 6.484 17.522 2 12 2z\" clipRule=\"evenodd\"/>\n                      </svg>\n                    </Link>\n                  </div>\n                </div>\n              </motion.div>\n            ))}\n          </div>\n        </motion.section>\n\n        {/* Company Culture */}\n        <motion.section\n          initial={{ opacity: 0, y: 20 }}\n          animate={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.6, delay: 0.7 }}\n          className=\"mt-20 bg-gradient-to-r from-blue-50 to-purple-50 rounded-2xl p-12 text-center\"\n        >\n          <HeartIcon className=\"w-12 h-12 text-blue-600 mx-auto mb-6\" />\n          <h2 className=\"text-3xl font-bold text-gray-900 mb-4\">\n            Join Our Team\n          </h2>\n          <p className=\"text-gray-600 mb-8 max-w-2xl mx-auto\">\n            We're always looking for talented individuals who share our passion for innovation \n            and excellence. If you're ready to make an impact, we'd love to hear from you.\n          </p>\n          <Link href=\"/careers\" className=\"btn-primary\">\n            View Open Positions\n          </Link>\n        </motion.section>\n      </main>\n      <Footer />\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAaA;AACA;AAnBA;;;;;;;;AAqBA,MAAM,cAAc;IAClB;QACE,IAAI;QACJ,MAAM;QACN,MAAM;QACN,YAAY;QACZ,KAAK;QACL,OAAO;QACP,UAAU;QACV,OAAO;QACP,UAAU;QACV,SAAS;QACT,QAAQ;QACR,QAAQ;YAAC;YAAsB;YAAmB;YAAkB;SAAuB;QAC3F,WAAW;QACX,YAAY;QACZ,WAAW;YAAC;YAAW;YAAW;SAAS;QAC3C,WAAW;YAAC;YAAa;YAAoB;SAAY;QACzD,MAAM,yNAAA,CAAA,gBAAa;QACnB,UAAU;IACZ;IACA;QACE,IAAI;QACJ,MAAM;QACN,MAAM;QACN,YAAY;QACZ,KAAK;QACL,OAAO;QACP,UAAU;QACV,OAAO;QACP,UAAU;QACV,SAAS;QACT,QAAQ;QACR,QAAQ;YAAC;YAAuB;YAAmB;YAAU;SAAkB;QAC/E,WAAW;QACX,YAAY;QACZ,WAAW;YAAC;YAAW;YAAY;SAAW;QAC9C,WAAW;YAAC;YAAe;YAAsB;SAAc;QAC/D,MAAM,iNAAA,CAAA,YAAS;QACf,UAAU;IACZ;IACA;QACE,IAAI;QACJ,MAAM;QACN,MAAM;QACN,YAAY;QACZ,KAAK;QACL,OAAO;QACP,UAAU;QACV,OAAO;QACP,UAAU;QACV,SAAS;QACT,QAAQ;QACR,QAAQ;YAAC;YAAS;YAAc;YAAgB;SAAgB;QAChE,WAAW;QACX,YAAY;QACZ,WAAW;YAAC;YAAW;SAAS;QAChC,WAAW;YAAC;YAAkB;YAAiB;SAAc;QAC7D,MAAM,6NAAA,CAAA,kBAAe;QACrB,UAAU;IACZ;IACA;QACE,IAAI;QACJ,MAAM;QACN,MAAM;QACN,YAAY;QACZ,KAAK;QACL,OAAO;QACP,UAAU;QACV,OAAO;QACP,UAAU;QACV,SAAS;QACT,QAAQ;QACR,QAAQ;YAAC;YAAW;YAAU;YAAc;SAAgB;QAC5D,WAAW;QACX,YAAY;QACZ,WAAW;YAAC;YAAW;YAAW;SAAa;QAC/C,WAAW;YAAC;YAAyB;YAAuB;SAAS;QACrE,MAAM,6MAAA,CAAA,UAAO;QACb,UAAU;IACZ;IACA;QACE,IAAI;QACJ,MAAM;QACN,MAAM;QACN,YAAY;QACZ,KAAK;QACL,OAAO;QACP,UAAU;QACV,OAAO;QACP,UAAU;QACV,SAAS;QACT,QAAQ;QACR,QAAQ;YAAC;YAAgB;YAAW;YAAO;SAAU;QACrD,WAAW;QACX,YAAY;QACZ,WAAW;YAAC;YAAW;YAAY;SAAS;QAC5C,WAAW;YAAC;YAAa;YAAS;SAAS;QAC3C,MAAM,yOAAA,CAAA,wBAAqB;QAC3B,UAAU;IACZ;IACA;QACE,IAAI;QACJ,MAAM;QACN,MAAM;QACN,YAAY;QACZ,KAAK;QACL,OAAO;QACP,UAAU;QACV,OAAO;QACP,UAAU;QACV,SAAS;QACT,QAAQ;QACR,QAAQ;YAAC;YAAiB;YAAuB;YAAa;SAAa;QAC3E,WAAW;QACX,YAAY;QACZ,WAAW;YAAC;YAAW;SAAU;QACjC,WAAW;YAAC;YAAmB;YAAkB;SAAQ;QACzD,MAAM,6NAAA,CAAA,kBAAe;QACrB,UAAU;IACZ;IACA;QACE,IAAI;QACJ,MAAM;QACN,MAAM;QACN,YAAY;QACZ,KAAK;QACL,OAAO;QACP,UAAU;QACV,OAAO;QACP,UAAU;QACV,SAAS;QACT,QAAQ;QACR,QAAQ;YAAC;YAAoB;YAAU;YAAc;SAAqB;QAC1E,WAAW;QACX,YAAY;QACZ,WAAW;YAAC;YAAW;YAAW;SAAU;QAC5C,WAAW;YAAC;YAAa;YAAgB;SAAgB;QACzD,MAAM,uNAAA,CAAA,eAAY;QAClB,UAAU;IACZ;IACA;QACE,IAAI;QACJ,MAAM;QACN,MAAM;QACN,YAAY;QACZ,KAAK;QACL,OAAO;QACP,UAAU;QACV,OAAO;QACP,UAAU;QACV,SAAS;QACT,QAAQ;QACR,QAAQ;YAAC;YAAO;YAAc;YAAU;SAAQ;QAChD,WAAW;QACX,YAAY;QACZ,WAAW;YAAC;SAAU;QACtB,WAAW;YAAC;YAAc;YAAmB;SAAa;QAC1D,MAAM,iNAAA,CAAA,YAAS;QACf,UAAU;IACZ;CACD;AAED,MAAM,cAAc;IAAC;IAAO;IAAc;IAAe;IAAY;IAAa;CAAiB;AAEpF,SAAS;IACtB,MAAM,kBAAkB,YAAY,MAAM,CAAC,CAAA,SAAU,OAAO,QAAQ;IACpE,MAAM,aAAa;IAEnB,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC,4HAAA,CAAA,SAAM;;;;;0BAEP,8OAAC;gBAAO,WAAU;0BAChB,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAG;wBAC7B,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAE;wBAC5B,YAAY;4BAAE,UAAU;wBAAI;wBAC5B,WAAU;;0CAEV,8OAAC;gCAAG,WAAU;;oCAA0E;kDAC7E,8OAAC;wCAAK,WAAU;kDAAgB;;;;;;;;;;;;0CAE3C,8OAAC;gCAAE,WAAU;0CAA+C;;;;;;;;;;;;;;;;;;;;;;0BAQlE,8OAAC;gBAAK,WAAU;;kCAEd,8OAAC,0LAAA,CAAA,SAAM,CAAC,OAAO;wBACb,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAG;wBAC7B,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAE;wBAC5B,YAAY;4BAAE,UAAU;4BAAK,OAAO;wBAAI;wBACxC,WAAU;;0CAEV,8OAAC;gCAAG,WAAU;0CAAqD;;;;;;0CACnE,8OAAC;gCAAI,WAAU;0CACZ,gBAAgB,GAAG,CAAC,CAAC,QAAQ,sBAC5B,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wCAET,SAAS;4CAAE,SAAS;4CAAG,GAAG;wCAAG;wCAC7B,SAAS;4CAAE,SAAS;4CAAG,GAAG;wCAAE;wCAC5B,YAAY;4CAAE,UAAU;4CAAK,OAAO,MAAM;wCAAM;wCAChD,WAAU;kDAEV,cAAA,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC,6HAAA,CAAA,UAAK;wDACJ,KAAK,OAAO,KAAK;wDACjB,KAAK,OAAO,IAAI;wDAChB,IAAI;wDACJ,WAAU;;;;;;;;;;;8DAId,8OAAC;oDAAG,WAAU;8DACX,OAAO,IAAI;;;;;;8DAGd,8OAAC;oDAAE,WAAU;8DACV,OAAO,IAAI;;;;;;8DAGd,8OAAC;oDAAE,WAAU;8DACV,OAAO,GAAG;;;;;;8DAGb,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAE,WAAU;8EAAqB;;;;;;8EAClC,8OAAC;oEAAE,WAAU;8EAAe,OAAO,UAAU;;;;;;;;;;;;sEAE/C,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAE,WAAU;8EAAqB;;;;;;8EAClC,8OAAC;oEAAE,WAAU;8EAAe,OAAO,QAAQ;;;;;;;;;;;;;;;;;;8DAI/C,8OAAC;oDAAI,WAAU;8DACZ,OAAO,MAAM,CAAC,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,sBAC9B,8OAAC;4DAEC,WAAU;sEAET;2DAHI;;;;;;;;;;8DAQX,8OAAC;oDAAI,WAAU;;sEACb,8OAAC,4JAAA,CAAA,UAAI;4DACH,MAAM,CAAC,OAAO,EAAE,OAAO,KAAK,EAAE;4DAC9B,WAAU;sEAEV,cAAA,8OAAC,uNAAA,CAAA,eAAY;gEAAC,WAAU;;;;;;;;;;;sEAE1B,8OAAC,4JAAA,CAAA,UAAI;4DACH,MAAM,OAAO,QAAQ;4DACrB,QAAO;4DACP,KAAI;4DACJ,WAAU;sEAEV,cAAA,8OAAC;gEAAI,WAAU;gEAAU,MAAK;gEAAe,SAAQ;0EACnD,cAAA,8OAAC;oEAAK,GAAE;;;;;;;;;;;;;;;;sEAGZ,8OAAC,4JAAA,CAAA,UAAI;4DACH,MAAM,OAAO,MAAM;4DACnB,QAAO;4DACP,KAAI;4DACJ,WAAU;sEAEV,cAAA,8OAAC;gEAAI,WAAU;gEAAU,MAAK;gEAAe,SAAQ;0EACnD,cAAA,8OAAC;oEAAK,UAAS;oEAAU,GAAE;oEAAmtB,UAAS;;;;;;;;;;;;;;;;;;;;;;;;;;;;uCA1E1vB,OAAO,EAAE;;;;;;;;;;;;;;;;kCAqFtB,8OAAC,0LAAA,CAAA,SAAM,CAAC,OAAO;wBACb,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAG;wBAC7B,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAE;wBAC5B,YAAY;4BAAE,UAAU;4BAAK,OAAO;wBAAI;wBACxC,WAAU;kCAEV,cAAA,8OAAC;4BAAI,WAAU;sCACZ,YAAY,GAAG,CAAC,CAAC,2BAChB,8OAAC;oCAEC,WAAU;8CAET;mCAHI;;;;;;;;;;;;;;;kCAUb,8OAAC,0LAAA,CAAA,SAAM,CAAC,OAAO;wBACb,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAG;wBAC7B,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAE;wBAC5B,YAAY;4BAAE,UAAU;4BAAK,OAAO;wBAAI;;0CAExC,8OAAC;gCAAG,WAAU;0CAAqD;;;;;;0CACnE,8OAAC;gCAAI,WAAU;0CACZ,WAAW,GAAG,CAAC,CAAC,QAAQ,sBACvB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wCAET,SAAS;4CAAE,SAAS;4CAAG,GAAG;wCAAG;wCAC7B,SAAS;4CAAE,SAAS;4CAAG,GAAG;wCAAE;wCAC5B,YAAY;4CAAE,UAAU;4CAAK,OAAO,OAAO;wCAAM;wCACjD,WAAU;kDAEV,cAAA,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC,6HAAA,CAAA,UAAK;wDACJ,KAAK,OAAO,KAAK;wDACjB,KAAK,OAAO,IAAI;wDAChB,IAAI;wDACJ,WAAU;;;;;;;;;;;8DAId,8OAAC;oDAAG,WAAU;8DACX,OAAO,IAAI;;;;;;8DAGd,8OAAC;oDAAE,WAAU;8DACV,OAAO,IAAI;;;;;;8DAGd,8OAAC;oDAAI,WAAU;;sEACb,8OAAC,mNAAA,CAAA,aAAU;4DAAC,WAAU;;;;;;wDACrB,OAAO,QAAQ;;;;;;;8DAGlB,8OAAC;oDAAE,WAAU;8DACV,OAAO,GAAG;;;;;;8DAGb,8OAAC;oDAAI,WAAU;;wDACZ,OAAO,MAAM,CAAC,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,sBAC9B,8OAAC;gEAEC,WAAU;0EAET;+DAHI;;;;;wDAMR,OAAO,MAAM,CAAC,MAAM,GAAG,mBACtB,8OAAC;4DAAK,WAAU;;gEAAsD;gEAClE,OAAO,MAAM,CAAC,MAAM,GAAG;;;;;;;;;;;;;8DAK/B,8OAAC;oDAAI,WAAU;;sEACb,8OAAC,4JAAA,CAAA,UAAI;4DACH,MAAM,CAAC,OAAO,EAAE,OAAO,KAAK,EAAE;4DAC9B,WAAU;sEAEV,cAAA,8OAAC,uNAAA,CAAA,eAAY;gEAAC,WAAU;;;;;;;;;;;sEAE1B,8OAAC,4JAAA,CAAA,UAAI;4DACH,MAAM,OAAO,QAAQ;4DACrB,QAAO;4DACP,KAAI;4DACJ,WAAU;sEAEV,cAAA,8OAAC;gEAAI,WAAU;gEAAU,MAAK;gEAAe,SAAQ;0EACnD,cAAA,8OAAC;oEAAK,GAAE;;;;;;;;;;;;;;;;sEAGZ,8OAAC,4JAAA,CAAA,UAAI;4DACH,MAAM,OAAO,MAAM;4DACnB,QAAO;4DACP,KAAI;4DACJ,WAAU;sEAEV,cAAA,8OAAC;gEAAI,WAAU;gEAAU,MAAK;gEAAe,SAAQ;0EACnD,cAAA,8OAAC;oEAAK,UAAS;oEAAU,GAAE;oEAAmtB,UAAS;;;;;;;;;;;;;;;;;;;;;;;;;;;;uCAzE1vB,OAAO,EAAE;;;;;;;;;;;;;;;;kCAoFtB,8OAAC,0LAAA,CAAA,SAAM,CAAC,OAAO;wBACb,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAG;wBAC7B,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAE;wBAC5B,YAAY;4BAAE,UAAU;4BAAK,OAAO;wBAAI;wBACxC,WAAU;;0CAEV,8OAAC,iNAAA,CAAA,YAAS;gCAAC,WAAU;;;;;;0CACrB,8OAAC;gCAAG,WAAU;0CAAwC;;;;;;0CAGtD,8OAAC;gCAAE,WAAU;0CAAuC;;;;;;0CAIpD,8OAAC,4JAAA,CAAA,UAAI;gCAAC,MAAK;gCAAW,WAAU;0CAAc;;;;;;;;;;;;;;;;;;0BAKlD,8OAAC,4HAAA,CAAA,SAAM;;;;;;;;;;;AAGb", "debugId": null}}]}