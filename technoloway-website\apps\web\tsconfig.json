{"extends": "../../packages/config/tsconfig.json", "compilerOptions": {"plugins": [{"name": "next"}], "baseUrl": ".", "paths": {"@/*": ["./src/*"], "@/components/*": ["./src/components/*"], "@/lib/*": ["./src/lib/*"], "@/hooks/*": ["./src/hooks/*"], "@/types/*": ["./src/types/*"], "@/styles/*": ["./src/styles/*"]}}, "include": ["next-env.d.ts", "**/*.ts", "**/*.tsx", ".next/types/**/*.ts"], "exclude": ["node_modules"]}