import { <PERSON><PERSON><PERSON><PERSON> } from '@clerk/nextjs';
import { Inter } from 'next/font/google';
import { Metadata } from 'next';
import { TRPCProvider } from '@/lib/trpc/provider';
import { Toaster } from '@technoloway/ui';
import './globals.css';

const inter = Inter({ subsets: ['latin'] });

export const metadata: Metadata = {
  title: {
    default: 'Technoloway - Software Development Company',
    template: '%s | Technoloway',
  },
  description: 'Leading software development company specializing in modern web applications, mobile apps, and enterprise solutions.',
  keywords: ['software development', 'web development', 'mobile apps', 'enterprise solutions', 'TypeScript', 'React', 'Next.js'],
  authors: [{ name: 'Technoloway Team' }],
  creator: 'Technoloway',
  publisher: 'Technoloway',
  formatDetection: {
    email: false,
    address: false,
    telephone: false,
  },
  metadataBase: new URL(process.env.NEXT_PUBLIC_APP_URL || 'https://technoloway.com'),
  openGraph: {
    type: 'website',
    locale: 'en_US',
    url: '/',
    title: 'Technoloway - Software Development Company',
    description: 'Leading software development company specializing in modern web applications, mobile apps, and enterprise solutions.',
    siteName: 'Technoloway',
    images: [
      {
        url: '/og-image.jpg',
        width: 1200,
        height: 630,
        alt: 'Technoloway - Software Development Company',
      },
    ],
  },
  twitter: {
    card: 'summary_large_image',
    title: 'Technoloway - Software Development Company',
    description: 'Leading software development company specializing in modern web applications, mobile apps, and enterprise solutions.',
    images: ['/og-image.jpg'],
    creator: '@technoloway',
  },
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      'max-video-preview': -1,
      'max-image-preview': 'large',
      'max-snippet': -1,
    },
  },
  verification: {
    google: process.env.GOOGLE_SITE_VERIFICATION,
  },
};

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <ClerkProvider>
      <html lang="en" className="scroll-smooth">
        <body className={inter.className}>
          <TRPCProvider>
            {children}
            <Toaster />
          </TRPCProvider>
        </body>
      </html>
    </ClerkProvider>
  );
}
