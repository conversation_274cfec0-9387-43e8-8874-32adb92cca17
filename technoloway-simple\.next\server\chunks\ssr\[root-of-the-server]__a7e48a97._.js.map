{"version": 3, "sources": [], "sections": [{"offset": {"line": 39, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Project%20By%20AI/Technoloway/Technoloway%20%28Processing%29/technoloway-simple/src/components/header.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport Link from 'next/link';\nimport { usePathname } from 'next/navigation';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport { Bars3Icon, XMarkIcon } from '@heroicons/react/24/outline';\n\nconst navigation = [\n  { name: 'Home', href: '/' },\n  { name: 'About', href: '/about' },\n  { name: 'Services', href: '/services' },\n  { name: 'Projects', href: '/projects' },\n  { name: 'Technologies', href: '/technologies' },\n  { name: 'Contact', href: '/contact' },\n];\n\nexport function Header() {\n  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);\n  const pathname = usePathname();\n\n  const isActive = (href: string) => {\n    if (href === '/') {\n      return pathname === '/';\n    }\n    return pathname.startsWith(href);\n  };\n\n  return (\n    <header className=\"fixed inset-x-0 top-0 z-50 bg-white/80 backdrop-blur-md border-b border-gray-200\">\n      <nav className=\"container flex items-center justify-between py-4\" aria-label=\"Global\">\n        <div className=\"flex lg:flex-1\">\n          <Link href=\"/\" className=\"-m-1.5 p-1.5\">\n            <span className=\"sr-only\">Technoloway</span>\n            <div className=\"flex items-center space-x-2\">\n              <div className=\"h-8 w-8 bg-gradient-to-r from-blue-600 to-blue-400 rounded-lg flex items-center justify-center\">\n                <span className=\"text-white font-bold text-sm\">T</span>\n              </div>\n              <span className=\"text-xl font-bold text-gray-900\">Technoloway</span>\n            </div>\n          </Link>\n        </div>\n        \n        <div className=\"flex lg:hidden\">\n          <button\n            type=\"button\"\n            className=\"-m-2.5 inline-flex items-center justify-center rounded-md p-2.5 text-gray-700\"\n            onClick={() => setMobileMenuOpen(true)}\n          >\n            <span className=\"sr-only\">Open main menu</span>\n            <Bars3Icon className=\"h-6 w-6\" aria-hidden=\"true\" />\n          </button>\n        </div>\n        \n        <div className=\"hidden lg:flex lg:gap-x-8\">\n          {navigation.map((item) => (\n            <Link\n              key={item.name}\n              href={item.href}\n              className={`text-sm font-semibold leading-6 transition-colors hover:text-blue-600 ${\n                isActive(item.href) ? 'text-blue-600' : 'text-gray-900'\n              }`}\n            >\n              {item.name}\n            </Link>\n          ))}\n        </div>\n        \n        <div className=\"hidden lg:flex lg:flex-1 lg:justify-end\">\n          <Link href=\"/contact\" className=\"btn-primary\">\n            Get Started\n          </Link>\n        </div>\n      </nav>\n      \n      {/* Mobile menu */}\n      <AnimatePresence>\n        {mobileMenuOpen && (\n          <motion.div\n            initial={{ opacity: 0 }}\n            animate={{ opacity: 1 }}\n            exit={{ opacity: 0 }}\n            className=\"lg:hidden\"\n          >\n            <div className=\"fixed inset-0 z-50\" />\n            <motion.div\n              initial={{ x: '100%' }}\n              animate={{ x: 0 }}\n              exit={{ x: '100%' }}\n              transition={{ type: 'spring', damping: 25, stiffness: 200 }}\n              className=\"fixed inset-y-0 right-0 z-50 w-full overflow-y-auto bg-white px-6 py-6 sm:max-w-sm sm:ring-1 sm:ring-gray-900/10\"\n            >\n              <div className=\"flex items-center justify-between\">\n                <Link href=\"/\" className=\"-m-1.5 p-1.5\">\n                  <span className=\"sr-only\">Technoloway</span>\n                  <div className=\"flex items-center space-x-2\">\n                    <div className=\"h-8 w-8 bg-gradient-to-r from-blue-600 to-blue-400 rounded-lg flex items-center justify-center\">\n                      <span className=\"text-white font-bold text-sm\">T</span>\n                    </div>\n                    <span className=\"text-xl font-bold text-gray-900\">Technoloway</span>\n                  </div>\n                </Link>\n                <button\n                  type=\"button\"\n                  className=\"-m-2.5 rounded-md p-2.5 text-gray-700\"\n                  onClick={() => setMobileMenuOpen(false)}\n                >\n                  <span className=\"sr-only\">Close menu</span>\n                  <XMarkIcon className=\"h-6 w-6\" aria-hidden=\"true\" />\n                </button>\n              </div>\n              <div className=\"mt-6 flow-root\">\n                <div className=\"-my-6 divide-y divide-gray-500/10\">\n                  <div className=\"space-y-2 py-6\">\n                    {navigation.map((item) => (\n                      <Link\n                        key={item.name}\n                        href={item.href}\n                        className={`-mx-3 block rounded-lg px-3 py-2 text-base font-semibold leading-7 hover:bg-gray-50 ${\n                          isActive(item.href) ? 'text-blue-600' : 'text-gray-900'\n                        }`}\n                        onClick={() => setMobileMenuOpen(false)}\n                      >\n                        {item.name}\n                      </Link>\n                    ))}\n                  </div>\n                  <div className=\"py-6\">\n                    <Link\n                      href=\"/contact\"\n                      className=\"-mx-3 block rounded-lg px-3 py-2.5 text-base font-semibold leading-7 text-blue-600 hover:bg-gray-50\"\n                      onClick={() => setMobileMenuOpen(false)}\n                    >\n                      Get Started\n                    </Link>\n                  </div>\n                </div>\n              </div>\n            </motion.div>\n          </motion.div>\n        )}\n      </AnimatePresence>\n    </header>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AACA;AAAA;AANA;;;;;;;AAQA,MAAM,aAAa;IACjB;QAAE,MAAM;QAAQ,MAAM;IAAI;IAC1B;QAAE,MAAM;QAAS,MAAM;IAAS;IAChC;QAAE,MAAM;QAAY,MAAM;IAAY;IACtC;QAAE,MAAM;QAAY,MAAM;IAAY;IACtC;QAAE,MAAM;QAAgB,MAAM;IAAgB;IAC9C;QAAE,MAAM;QAAW,MAAM;IAAW;CACrC;AAEM,SAAS;IACd,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,WAAW,CAAA,GAAA,kIAAA,CAAA,cAAW,AAAD;IAE3B,MAAM,WAAW,CAAC;QAChB,IAAI,SAAS,KAAK;YAChB,OAAO,aAAa;QACtB;QACA,OAAO,SAAS,UAAU,CAAC;IAC7B;IAEA,qBACE,8OAAC;QAAO,WAAU;;0BAChB,8OAAC;gBAAI,WAAU;gBAAmD,cAAW;;kCAC3E,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC,4JAAA,CAAA,UAAI;4BAAC,MAAK;4BAAI,WAAU;;8CACvB,8OAAC;oCAAK,WAAU;8CAAU;;;;;;8CAC1B,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAK,WAAU;0DAA+B;;;;;;;;;;;sDAEjD,8OAAC;4CAAK,WAAU;sDAAkC;;;;;;;;;;;;;;;;;;;;;;;kCAKxD,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BACC,MAAK;4BACL,WAAU;4BACV,SAAS,IAAM,kBAAkB;;8CAEjC,8OAAC;oCAAK,WAAU;8CAAU;;;;;;8CAC1B,8OAAC,iNAAA,CAAA,YAAS;oCAAC,WAAU;oCAAU,eAAY;;;;;;;;;;;;;;;;;kCAI/C,8OAAC;wBAAI,WAAU;kCACZ,WAAW,GAAG,CAAC,CAAC,qBACf,8OAAC,4JAAA,CAAA,UAAI;gCAEH,MAAM,KAAK,IAAI;gCACf,WAAW,CAAC,sEAAsE,EAChF,SAAS,KAAK,IAAI,IAAI,kBAAkB,iBACxC;0CAED,KAAK,IAAI;+BANL,KAAK,IAAI;;;;;;;;;;kCAWpB,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC,4JAAA,CAAA,UAAI;4BAAC,MAAK;4BAAW,WAAU;sCAAc;;;;;;;;;;;;;;;;;0BAOlD,8OAAC,yLAAA,CAAA,kBAAe;0BACb,gCACC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;oBAAE;oBACtB,SAAS;wBAAE,SAAS;oBAAE;oBACtB,MAAM;wBAAE,SAAS;oBAAE;oBACnB,WAAU;;sCAEV,8OAAC;4BAAI,WAAU;;;;;;sCACf,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,SAAS;gCAAE,GAAG;4BAAO;4BACrB,SAAS;gCAAE,GAAG;4BAAE;4BAChB,MAAM;gCAAE,GAAG;4BAAO;4BAClB,YAAY;gCAAE,MAAM;gCAAU,SAAS;gCAAI,WAAW;4BAAI;4BAC1D,WAAU;;8CAEV,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,4JAAA,CAAA,UAAI;4CAAC,MAAK;4CAAI,WAAU;;8DACvB,8OAAC;oDAAK,WAAU;8DAAU;;;;;;8DAC1B,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;sEACb,cAAA,8OAAC;gEAAK,WAAU;0EAA+B;;;;;;;;;;;sEAEjD,8OAAC;4DAAK,WAAU;sEAAkC;;;;;;;;;;;;;;;;;;sDAGtD,8OAAC;4CACC,MAAK;4CACL,WAAU;4CACV,SAAS,IAAM,kBAAkB;;8DAEjC,8OAAC;oDAAK,WAAU;8DAAU;;;;;;8DAC1B,8OAAC,iNAAA,CAAA,YAAS;oDAAC,WAAU;oDAAU,eAAY;;;;;;;;;;;;;;;;;;8CAG/C,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;0DACZ,WAAW,GAAG,CAAC,CAAC,qBACf,8OAAC,4JAAA,CAAA,UAAI;wDAEH,MAAM,KAAK,IAAI;wDACf,WAAW,CAAC,oFAAoF,EAC9F,SAAS,KAAK,IAAI,IAAI,kBAAkB,iBACxC;wDACF,SAAS,IAAM,kBAAkB;kEAEhC,KAAK,IAAI;uDAPL,KAAK,IAAI;;;;;;;;;;0DAWpB,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC,4JAAA,CAAA,UAAI;oDACH,MAAK;oDACL,WAAU;oDACV,SAAS,IAAM,kBAAkB;8DAClC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAYrB", "debugId": null}}, {"offset": {"line": 431, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Project%20By%20AI/Technoloway/Technoloway%20%28Processing%29/technoloway-simple/src/components/footer.tsx"], "sourcesContent": ["import Link from 'next/link';\n\nexport function Footer() {\n  return (\n    <footer className=\"bg-gray-900 text-white\">\n      <div className=\"container py-16\">\n        <div className=\"grid grid-cols-1 md:grid-cols-4 gap-8\">\n          <div className=\"col-span-1 md:col-span-2\">\n            <div className=\"flex items-center space-x-2 mb-6\">\n              <div className=\"h-8 w-8 bg-gradient-to-r from-blue-600 to-blue-400 rounded-lg flex items-center justify-center\">\n                <span className=\"text-white font-bold text-sm\">T</span>\n              </div>\n              <span className=\"text-xl font-bold\">Technoloway</span>\n            </div>\n            <p className=\"text-gray-300 mb-6 max-w-md\">\n              Transforming ideas into digital reality. We build exceptional software \n              solutions that drive business growth and innovation.\n            </p>\n            <div className=\"space-y-2\">\n              <p className=\"text-gray-300\"><EMAIL></p>\n              <p className=\"text-gray-300\">+****************</p>\n              <p className=\"text-gray-300\">San Francisco, CA</p>\n            </div>\n          </div>\n          \n          <div>\n            <h3 className=\"text-lg font-semibold mb-4\">Services</h3>\n            <ul className=\"space-y-2 text-gray-300\">\n              <li><Link href=\"/#services\" className=\"hover:text-white transition-colors\">Web Development</Link></li>\n              <li><Link href=\"/#services\" className=\"hover:text-white transition-colors\">Mobile Apps</Link></li>\n              <li><Link href=\"/#services\" className=\"hover:text-white transition-colors\">Cloud Solutions</Link></li>\n              <li><Link href=\"/#services\" className=\"hover:text-white transition-colors\">API Development</Link></li>\n            </ul>\n          </div>\n          \n          <div>\n            <h3 className=\"text-lg font-semibold mb-4\">Company</h3>\n            <ul className=\"space-y-2 text-gray-300\">\n              <li><Link href=\"/team\" className=\"hover:text-white transition-colors\">About</Link></li>\n              <li><Link href=\"/team\" className=\"hover:text-white transition-colors\">Team</Link></li>\n              <li><Link href=\"/portfolio\" className=\"hover:text-white transition-colors\">Portfolio</Link></li>\n              <li><Link href=\"/blog\" className=\"hover:text-white transition-colors\">Blog</Link></li>\n              <li><Link href=\"/#contact\" className=\"hover:text-white transition-colors\">Contact</Link></li>\n            </ul>\n          </div>\n        </div>\n        \n        <div className=\"border-t border-gray-700 mt-12 pt-8 text-center\">\n          <p className=\"text-gray-400\">\n            &copy; {new Date().getFullYear()} Technoloway. All rights reserved.\n          </p>\n        </div>\n      </div>\n    </footer>\n  );\n}\n"], "names": [], "mappings": ";;;;AAAA;;;AAEO,SAAS;IACd,qBACE,8OAAC;QAAO,WAAU;kBAChB,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAK,WAAU;0DAA+B;;;;;;;;;;;sDAEjD,8OAAC;4CAAK,WAAU;sDAAoB;;;;;;;;;;;;8CAEtC,8OAAC;oCAAE,WAAU;8CAA8B;;;;;;8CAI3C,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAE,WAAU;sDAAgB;;;;;;sDAC7B,8OAAC;4CAAE,WAAU;sDAAgB;;;;;;sDAC7B,8OAAC;4CAAE,WAAU;sDAAgB;;;;;;;;;;;;;;;;;;sCAIjC,8OAAC;;8CACC,8OAAC;oCAAG,WAAU;8CAA6B;;;;;;8CAC3C,8OAAC;oCAAG,WAAU;;sDACZ,8OAAC;sDAAG,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAa,WAAU;0DAAqC;;;;;;;;;;;sDAC3E,8OAAC;sDAAG,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAa,WAAU;0DAAqC;;;;;;;;;;;sDAC3E,8OAAC;sDAAG,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAa,WAAU;0DAAqC;;;;;;;;;;;sDAC3E,8OAAC;sDAAG,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAa,WAAU;0DAAqC;;;;;;;;;;;;;;;;;;;;;;;sCAI/E,8OAAC;;8CACC,8OAAC;oCAAG,WAAU;8CAA6B;;;;;;8CAC3C,8OAAC;oCAAG,WAAU;;sDACZ,8OAAC;sDAAG,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAQ,WAAU;0DAAqC;;;;;;;;;;;sDACtE,8OAAC;sDAAG,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAQ,WAAU;0DAAqC;;;;;;;;;;;sDACtE,8OAAC;sDAAG,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAa,WAAU;0DAAqC;;;;;;;;;;;sDAC3E,8OAAC;sDAAG,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAQ,WAAU;0DAAqC;;;;;;;;;;;sDACtE,8OAAC;sDAAG,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAY,WAAU;0DAAqC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAKhF,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAE,WAAU;;4BAAgB;4BACnB,IAAI,OAAO,WAAW;4BAAG;;;;;;;;;;;;;;;;;;;;;;;AAM7C", "debugId": null}}, {"offset": {"line": 757, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Project%20By%20AI/Technoloway/Technoloway%20%28Processing%29/technoloway-simple/src/app/portfolio/page.tsx"], "sourcesContent": ["'use client';\n\nimport { motion } from 'framer-motion';\nimport Link from 'next/link';\nimport Image from 'next/image';\nimport {\n  ArrowTopRightOnSquareIcon,\n  CodeBracketIcon,\n  DevicePhoneMobileIcon,\n  GlobeAltIcon,\n  ChartBarIcon,\n  ShoppingCartIcon,\n  AcademicCapIcon\n} from '@heroicons/react/24/outline';\nimport { Header } from '@/components/header';\nimport { Footer } from '@/components/footer';\n\nconst projects = [\n  {\n    id: 1,\n    title: 'EcoCommerce Platform',\n    description: 'A sustainable e-commerce platform built for eco-friendly products with advanced analytics and inventory management.',\n    longDescription: 'Complete e-commerce solution featuring real-time inventory tracking, AI-powered product recommendations, and carbon footprint calculations for each purchase.',\n    category: 'E-commerce',\n    technologies: ['Next.js', 'TypeScript', 'PostgreSQL', 'Stripe', 'AWS'],\n    image: 'https://images.unsplash.com/photo-1556742049-0cfed4f6a45d?w=800&h=600&fit=crop',\n    liveUrl: 'https://ecocommerce-demo.com',\n    githubUrl: 'https://github.com/technoloway/ecocommerce',\n    featured: true,\n    results: {\n      metric1: { label: 'Revenue Increase', value: '150%' },\n      metric2: { label: 'User Engagement', value: '+85%' },\n      metric3: { label: 'Page Load Speed', value: '2.1s' },\n    },\n    icon: ShoppingCartIcon,\n    client: 'GreenTech Solutions',\n    duration: '6 months',\n    teamSize: '5 developers',\n  },\n  {\n    id: 2,\n    title: 'HealthTracker Mobile App',\n    description: 'Cross-platform mobile application for health monitoring with real-time data synchronization and AI insights.',\n    longDescription: 'Comprehensive health tracking app with wearable device integration, personalized health insights, and telemedicine features.',\n    category: 'Mobile App',\n    technologies: ['React Native', 'Node.js', 'MongoDB', 'Firebase', 'TensorFlow'],\n    image: 'https://images.unsplash.com/photo-**********-5c350d0d3c56?w=800&h=600&fit=crop',\n    liveUrl: 'https://healthtracker-app.com',\n    githubUrl: null,\n    featured: true,\n    results: {\n      metric1: { label: 'Active Users', value: '50K+' },\n      metric2: { label: 'App Store Rating', value: '4.8/5' },\n      metric3: { label: 'Data Accuracy', value: '99.2%' },\n    },\n    icon: DevicePhoneMobileIcon,\n    client: 'MedTech Innovations',\n    duration: '8 months',\n    teamSize: '4 developers',\n  },\n  {\n    id: 3,\n    title: 'FinanceFlow Dashboard',\n    description: 'Real-time financial analytics dashboard for investment firms with advanced charting and portfolio management.',\n    longDescription: 'Enterprise-grade financial dashboard with real-time market data, risk analysis, and automated reporting capabilities.',\n    category: 'Web Application',\n    technologies: ['React', 'D3.js', 'Python', 'Redis', 'Docker'],\n    image: 'https://images.unsplash.com/photo-**********-bebda4e38f71?w=800&h=600&fit=crop',\n    liveUrl: 'https://financeflow-demo.com',\n    githubUrl: 'https://github.com/technoloway/financeflow',\n    featured: false,\n    results: {\n      metric1: { label: 'Processing Speed', value: '10x faster' },\n      metric2: { label: 'Data Accuracy', value: '99.9%' },\n      metric3: { label: 'User Satisfaction', value: '95%' },\n    },\n    icon: ChartBarIcon,\n    client: 'Capital Investments LLC',\n    duration: '4 months',\n    teamSize: '6 developers',\n  },\n  {\n    id: 4,\n    title: 'EduConnect Learning Platform',\n    description: 'Online learning management system with interactive courses, progress tracking, and collaborative features.',\n    longDescription: 'Comprehensive LMS with video streaming, interactive quizzes, peer collaboration tools, and AI-powered learning recommendations.',\n    category: 'Education',\n    technologies: ['Vue.js', 'Laravel', 'MySQL', 'WebRTC', 'AWS S3'],\n    image: 'https://images.unsplash.com/photo-1501504905252-473c47e087f8?w=800&h=600&fit=crop',\n    liveUrl: 'https://educonnect-platform.com',\n    githubUrl: null,\n    featured: false,\n    results: {\n      metric1: { label: 'Student Enrollment', value: '25K+' },\n      metric2: { label: 'Course Completion', value: '+40%' },\n      metric3: { label: 'Platform Uptime', value: '99.8%' },\n    },\n    icon: AcademicCapIcon,\n    client: 'EduTech Academy',\n    duration: '10 months',\n    teamSize: '7 developers',\n  },\n  {\n    id: 5,\n    title: 'SmartCity IoT Platform',\n    description: 'IoT data management platform for smart city infrastructure with real-time monitoring and predictive analytics.',\n    longDescription: 'Scalable IoT platform managing thousands of sensors across urban infrastructure with machine learning-powered insights.',\n    category: 'IoT Platform',\n    technologies: ['Node.js', 'InfluxDB', 'Grafana', 'Kubernetes', 'MQTT'],\n    image: 'https://images.unsplash.com/photo-1480714378408-67cf0d13bc1f?w=800&h=600&fit=crop',\n    liveUrl: 'https://smartcity-demo.com',\n    githubUrl: 'https://github.com/technoloway/smartcity',\n    featured: false,\n    results: {\n      metric1: { label: 'Sensors Connected', value: '10K+' },\n      metric2: { label: 'Data Points/Day', value: '1M+' },\n      metric3: { label: 'Energy Savings', value: '30%' },\n    },\n    icon: GlobeAltIcon,\n    client: 'Metro City Council',\n    duration: '12 months',\n    teamSize: '8 developers',\n  },\n  {\n    id: 6,\n    title: 'CodeReview AI Assistant',\n    description: 'AI-powered code review tool that provides intelligent suggestions and identifies potential issues automatically.',\n    longDescription: 'Machine learning-based code analysis tool that integrates with popular version control systems to provide automated code reviews.',\n    category: 'Developer Tools',\n    technologies: ['Python', 'TensorFlow', 'FastAPI', 'PostgreSQL', 'Docker'],\n    image: 'https://images.unsplash.com/photo-1555066931-4365d14bab8c?w=800&h=600&fit=crop',\n    liveUrl: 'https://codereview-ai.com',\n    githubUrl: 'https://github.com/technoloway/codereview-ai',\n    featured: false,\n    results: {\n      metric1: { label: 'Bug Detection', value: '+75%' },\n      metric2: { label: 'Review Time', value: '-60%' },\n      metric3: { label: 'Code Quality', value: '+45%' },\n    },\n    icon: CodeBracketIcon,\n    client: 'DevTools Inc.',\n    duration: '5 months',\n    teamSize: '4 developers',\n  },\n];\n\nconst categories = ['All', 'E-commerce', 'Mobile App', 'Web Application', 'Education', 'IoT Platform', 'Developer Tools'];\n\nexport default function PortfolioPage() {\n  const featuredProjects = projects.filter(project => project.featured);\n  const allProjects = projects;\n\n  return (\n    <div className=\"min-h-screen bg-white\">\n      <Header />\n      {/* Header */}\n      <header className=\"bg-gray-50 py-20 pt-32\">\n        <div className=\"container\">\n          <motion.div\n            initial={{ opacity: 0, y: 20 }}\n            animate={{ opacity: 1, y: 0 }}\n            transition={{ duration: 0.6 }}\n            className=\"text-center\"\n          >\n            <h1 className=\"text-4xl font-bold tracking-tight text-gray-900 sm:text-5xl lg:text-6xl\">\n              Our <span className=\"gradient-text\">Portfolio</span>\n            </h1>\n            <p className=\"mt-6 text-xl text-gray-600 max-w-3xl mx-auto\">\n              Explore our successful projects and case studies. From startups to enterprise solutions, \n              we've helped businesses transform their ideas into powerful digital experiences.\n            </p>\n          </motion.div>\n        </div>\n      </header>\n\n      <main className=\"container py-16\">\n        {/* Featured Projects */}\n        <motion.section\n          initial={{ opacity: 0, y: 20 }}\n          animate={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.6, delay: 0.2 }}\n          className=\"mb-20\"\n        >\n          <h2 className=\"text-3xl font-bold text-gray-900 mb-12 text-center\">Featured Projects</h2>\n          <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-12\">\n            {featuredProjects.map((project, index) => (\n              <motion.div\n                key={project.id}\n                initial={{ opacity: 0, y: 20 }}\n                animate={{ opacity: 1, y: 0 }}\n                transition={{ duration: 0.6, delay: 0.1 * index }}\n                className=\"bg-white rounded-2xl shadow-lg overflow-hidden border border-gray-200 hover:shadow-xl transition-shadow\"\n              >\n                <div className=\"relative h-64\">\n                  <Image\n                    src={project.image}\n                    alt={project.title}\n                    fill\n                    className=\"object-cover\"\n                  />\n                  <div className=\"absolute top-4 left-4\">\n                    <span className=\"inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-blue-600 text-white\">\n                      Featured\n                    </span>\n                  </div>\n                </div>\n                \n                <div className=\"p-8\">\n                  <div className=\"flex items-center space-x-2 mb-4\">\n                    <project.icon className=\"w-6 h-6 text-blue-600\" />\n                    <span className=\"text-sm font-medium text-blue-600\">{project.category}</span>\n                  </div>\n                  \n                  <h3 className=\"text-2xl font-bold text-gray-900 mb-3\">\n                    {project.title}\n                  </h3>\n                  \n                  <p className=\"text-gray-600 mb-6\">\n                    {project.longDescription}\n                  </p>\n                  \n                  <div className=\"grid grid-cols-3 gap-4 mb-6\">\n                    {Object.entries(project.results).map(([key, result]) => (\n                      <div key={key} className=\"text-center\">\n                        <div className=\"text-2xl font-bold text-blue-600\">{result.value}</div>\n                        <div className=\"text-sm text-gray-500\">{result.label}</div>\n                      </div>\n                    ))}\n                  </div>\n                  \n                  <div className=\"flex flex-wrap gap-2 mb-6\">\n                    {project.technologies.map((tech) => (\n                      <span\n                        key={tech}\n                        className=\"px-3 py-1 bg-gray-100 text-gray-700 text-sm rounded-full\"\n                      >\n                        {tech}\n                      </span>\n                    ))}\n                  </div>\n                  \n                  <div className=\"flex items-center justify-between\">\n                    <div className=\"text-sm text-gray-500\">\n                      <p><strong>Client:</strong> {project.client}</p>\n                      <p><strong>Duration:</strong> {project.duration}</p>\n                    </div>\n                    \n                    <div className=\"flex space-x-3\">\n                      {project.liveUrl && (\n                        <Link\n                          href={project.liveUrl}\n                          target=\"_blank\"\n                          rel=\"noopener noreferrer\"\n                          className=\"inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors\"\n                        >\n                          <ArrowTopRightOnSquareIcon className=\"w-4 h-4 mr-2\" />\n                          Live Demo\n                        </Link>\n                      )}\n                      {project.githubUrl && (\n                        <Link\n                          href={project.githubUrl}\n                          target=\"_blank\"\n                          rel=\"noopener noreferrer\"\n                          className=\"inline-flex items-center px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors\"\n                        >\n                          <CodeBracketIcon className=\"w-4 h-4 mr-2\" />\n                          Code\n                        </Link>\n                      )}\n                    </div>\n                  </div>\n                </div>\n              </motion.div>\n            ))}\n          </div>\n        </motion.section>\n\n        {/* Category Filter */}\n        <motion.section\n          initial={{ opacity: 0, y: 20 }}\n          animate={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.6, delay: 0.4 }}\n          className=\"mb-12\"\n        >\n          <div className=\"flex flex-wrap gap-2 justify-center\">\n            {categories.map((category) => (\n              <button\n                key={category}\n                className=\"px-4 py-2 rounded-full text-sm font-medium border border-gray-300 hover:border-blue-500 hover:text-blue-600 transition-colors\"\n              >\n                {category}\n              </button>\n            ))}\n          </div>\n        </motion.section>\n\n        {/* All Projects Grid */}\n        <motion.section\n          initial={{ opacity: 0, y: 20 }}\n          animate={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.6, delay: 0.5 }}\n        >\n          <h2 className=\"text-3xl font-bold text-gray-900 mb-12 text-center\">All Projects</h2>\n          <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8\">\n            {allProjects.map((project, index) => (\n              <motion.div\n                key={project.id}\n                initial={{ opacity: 0, y: 20 }}\n                animate={{ opacity: 1, y: 0 }}\n                transition={{ duration: 0.6, delay: 0.1 * index }}\n                className=\"bg-white rounded-xl shadow-md overflow-hidden border border-gray-200 hover:shadow-lg transition-shadow group\"\n              >\n                <div className=\"relative h-48 overflow-hidden\">\n                  <Image\n                    src={project.image}\n                    alt={project.title}\n                    fill\n                    className=\"object-cover group-hover:scale-105 transition-transform duration-300\"\n                  />\n                  <div className=\"absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-20 transition-all duration-300 flex items-center justify-center\">\n                    <div className=\"opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex space-x-2\">\n                      {project.liveUrl && (\n                        <Link\n                          href={project.liveUrl}\n                          target=\"_blank\"\n                          rel=\"noopener noreferrer\"\n                          className=\"p-2 bg-white rounded-full text-gray-700 hover:text-blue-600\"\n                        >\n                          <ArrowTopRightOnSquareIcon className=\"w-5 h-5\" />\n                        </Link>\n                      )}\n                      {project.githubUrl && (\n                        <Link\n                          href={project.githubUrl}\n                          target=\"_blank\"\n                          rel=\"noopener noreferrer\"\n                          className=\"p-2 bg-white rounded-full text-gray-700 hover:text-blue-600\"\n                        >\n                          <CodeBracketIcon className=\"w-5 h-5\" />\n                        </Link>\n                      )}\n                    </div>\n                  </div>\n                </div>\n                \n                <div className=\"p-6\">\n                  <div className=\"flex items-center space-x-2 mb-3\">\n                    <project.icon className=\"w-5 h-5 text-blue-600\" />\n                    <span className=\"text-sm font-medium text-blue-600\">{project.category}</span>\n                  </div>\n                  \n                  <h3 className=\"text-lg font-semibold text-gray-900 mb-2\">\n                    {project.title}\n                  </h3>\n                  \n                  <p className=\"text-gray-600 text-sm mb-4 line-clamp-3\">\n                    {project.description}\n                  </p>\n                  \n                  <div className=\"flex flex-wrap gap-1 mb-4\">\n                    {project.technologies.slice(0, 3).map((tech) => (\n                      <span\n                        key={tech}\n                        className=\"px-2 py-1 bg-gray-100 text-gray-600 text-xs rounded\"\n                      >\n                        {tech}\n                      </span>\n                    ))}\n                    {project.technologies.length > 3 && (\n                      <span className=\"px-2 py-1 bg-gray-100 text-gray-600 text-xs rounded\">\n                        +{project.technologies.length - 3} more\n                      </span>\n                    )}\n                  </div>\n                  \n                  <div className=\"text-xs text-gray-500\">\n                    <p><strong>Client:</strong> {project.client}</p>\n                    <p><strong>Team:</strong> {project.teamSize}</p>\n                  </div>\n                </div>\n              </motion.div>\n            ))}\n          </div>\n        </motion.section>\n\n        {/* CTA Section */}\n        <motion.section\n          initial={{ opacity: 0, y: 20 }}\n          animate={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.6, delay: 0.7 }}\n          className=\"mt-20 bg-gradient-to-r from-blue-50 to-purple-50 rounded-2xl p-12 text-center\"\n        >\n          <h2 className=\"text-3xl font-bold text-gray-900 mb-4\">\n            Ready to Start Your Project?\n          </h2>\n          <p className=\"text-gray-600 mb-8 max-w-2xl mx-auto\">\n            Let's discuss how we can help bring your ideas to life. \n            Our team is ready to tackle your next challenge.\n          </p>\n          <Link href=\"/contact\" className=\"btn-primary\">\n            Start Your Project\n          </Link>\n        </motion.section>\n      </main>\n      <Footer />\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AASA;AACA;AAfA;;;;;;;;AAiBA,MAAM,WAAW;IACf;QACE,IAAI;QACJ,OAAO;QACP,aAAa;QACb,iBAAiB;QACjB,UAAU;QACV,cAAc;YAAC;YAAW;YAAc;YAAc;YAAU;SAAM;QACtE,OAAO;QACP,SAAS;QACT,WAAW;QACX,UAAU;QACV,SAAS;YACP,SAAS;gBAAE,OAAO;gBAAoB,OAAO;YAAO;YACpD,SAAS;gBAAE,OAAO;gBAAmB,OAAO;YAAO;YACnD,SAAS;gBAAE,OAAO;gBAAmB,OAAO;YAAO;QACrD;QACA,MAAM,+NAAA,CAAA,mBAAgB;QACtB,QAAQ;QACR,UAAU;QACV,UAAU;IACZ;IACA;QACE,IAAI;QACJ,OAAO;QACP,aAAa;QACb,iBAAiB;QACjB,UAAU;QACV,cAAc;YAAC;YAAgB;YAAW;YAAW;YAAY;SAAa;QAC9E,OAAO;QACP,SAAS;QACT,WAAW;QACX,UAAU;QACV,SAAS;YACP,SAAS;gBAAE,OAAO;gBAAgB,OAAO;YAAO;YAChD,SAAS;gBAAE,OAAO;gBAAoB,OAAO;YAAQ;YACrD,SAAS;gBAAE,OAAO;gBAAiB,OAAO;YAAQ;QACpD;QACA,MAAM,yOAAA,CAAA,wBAAqB;QAC3B,QAAQ;QACR,UAAU;QACV,UAAU;IACZ;IACA;QACE,IAAI;QACJ,OAAO;QACP,aAAa;QACb,iBAAiB;QACjB,UAAU;QACV,cAAc;YAAC;YAAS;YAAS;YAAU;YAAS;SAAS;QAC7D,OAAO;QACP,SAAS;QACT,WAAW;QACX,UAAU;QACV,SAAS;YACP,SAAS;gBAAE,OAAO;gBAAoB,OAAO;YAAa;YAC1D,SAAS;gBAAE,OAAO;gBAAiB,OAAO;YAAQ;YAClD,SAAS;gBAAE,OAAO;gBAAqB,OAAO;YAAM;QACtD;QACA,MAAM,uNAAA,CAAA,eAAY;QAClB,QAAQ;QACR,UAAU;QACV,UAAU;IACZ;IACA;QACE,IAAI;QACJ,OAAO;QACP,aAAa;QACb,iBAAiB;QACjB,UAAU;QACV,cAAc;YAAC;YAAU;YAAW;YAAS;YAAU;SAAS;QAChE,OAAO;QACP,SAAS;QACT,WAAW;QACX,UAAU;QACV,SAAS;YACP,SAAS;gBAAE,OAAO;gBAAsB,OAAO;YAAO;YACtD,SAAS;gBAAE,OAAO;gBAAqB,OAAO;YAAO;YACrD,SAAS;gBAAE,OAAO;gBAAmB,OAAO;YAAQ;QACtD;QACA,MAAM,6NAAA,CAAA,kBAAe;QACrB,QAAQ;QACR,UAAU;QACV,UAAU;IACZ;IACA;QACE,IAAI;QACJ,OAAO;QACP,aAAa;QACb,iBAAiB;QACjB,UAAU;QACV,cAAc;YAAC;YAAW;YAAY;YAAW;YAAc;SAAO;QACtE,OAAO;QACP,SAAS;QACT,WAAW;QACX,UAAU;QACV,SAAS;YACP,SAAS;gBAAE,OAAO;gBAAqB,OAAO;YAAO;YACrD,SAAS;gBAAE,OAAO;gBAAmB,OAAO;YAAM;YAClD,SAAS;gBAAE,OAAO;gBAAkB,OAAO;YAAM;QACnD;QACA,MAAM,uNAAA,CAAA,eAAY;QAClB,QAAQ;QACR,UAAU;QACV,UAAU;IACZ;IACA;QACE,IAAI;QACJ,OAAO;QACP,aAAa;QACb,iBAAiB;QACjB,UAAU;QACV,cAAc;YAAC;YAAU;YAAc;YAAW;YAAc;SAAS;QACzE,OAAO;QACP,SAAS;QACT,WAAW;QACX,UAAU;QACV,SAAS;YACP,SAAS;gBAAE,OAAO;gBAAiB,OAAO;YAAO;YACjD,SAAS;gBAAE,OAAO;gBAAe,OAAO;YAAO;YAC/C,SAAS;gBAAE,OAAO;gBAAgB,OAAO;YAAO;QAClD;QACA,MAAM,6NAAA,CAAA,kBAAe;QACrB,QAAQ;QACR,UAAU;QACV,UAAU;IACZ;CACD;AAED,MAAM,aAAa;IAAC;IAAO;IAAc;IAAc;IAAmB;IAAa;IAAgB;CAAkB;AAE1G,SAAS;IACtB,MAAM,mBAAmB,SAAS,MAAM,CAAC,CAAA,UAAW,QAAQ,QAAQ;IACpE,MAAM,cAAc;IAEpB,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC,4HAAA,CAAA,SAAM;;;;;0BAEP,8OAAC;gBAAO,WAAU;0BAChB,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAG;wBAC7B,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAE;wBAC5B,YAAY;4BAAE,UAAU;wBAAI;wBAC5B,WAAU;;0CAEV,8OAAC;gCAAG,WAAU;;oCAA0E;kDAClF,8OAAC;wCAAK,WAAU;kDAAgB;;;;;;;;;;;;0CAEtC,8OAAC;gCAAE,WAAU;0CAA+C;;;;;;;;;;;;;;;;;;;;;;0BAQlE,8OAAC;gBAAK,WAAU;;kCAEd,8OAAC,0LAAA,CAAA,SAAM,CAAC,OAAO;wBACb,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAG;wBAC7B,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAE;wBAC5B,YAAY;4BAAE,UAAU;4BAAK,OAAO;wBAAI;wBACxC,WAAU;;0CAEV,8OAAC;gCAAG,WAAU;0CAAqD;;;;;;0CACnE,8OAAC;gCAAI,WAAU;0CACZ,iBAAiB,GAAG,CAAC,CAAC,SAAS,sBAC9B,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wCAET,SAAS;4CAAE,SAAS;4CAAG,GAAG;wCAAG;wCAC7B,SAAS;4CAAE,SAAS;4CAAG,GAAG;wCAAE;wCAC5B,YAAY;4CAAE,UAAU;4CAAK,OAAO,MAAM;wCAAM;wCAChD,WAAU;;0DAEV,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,6HAAA,CAAA,UAAK;wDACJ,KAAK,QAAQ,KAAK;wDAClB,KAAK,QAAQ,KAAK;wDAClB,IAAI;wDACJ,WAAU;;;;;;kEAEZ,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC;4DAAK,WAAU;sEAA6F;;;;;;;;;;;;;;;;;0DAMjH,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;;0EACb,8OAAC,QAAQ,IAAI;gEAAC,WAAU;;;;;;0EACxB,8OAAC;gEAAK,WAAU;0EAAqC,QAAQ,QAAQ;;;;;;;;;;;;kEAGvE,8OAAC;wDAAG,WAAU;kEACX,QAAQ,KAAK;;;;;;kEAGhB,8OAAC;wDAAE,WAAU;kEACV,QAAQ,eAAe;;;;;;kEAG1B,8OAAC;wDAAI,WAAU;kEACZ,OAAO,OAAO,CAAC,QAAQ,OAAO,EAAE,GAAG,CAAC,CAAC,CAAC,KAAK,OAAO,iBACjD,8OAAC;gEAAc,WAAU;;kFACvB,8OAAC;wEAAI,WAAU;kFAAoC,OAAO,KAAK;;;;;;kFAC/D,8OAAC;wEAAI,WAAU;kFAAyB,OAAO,KAAK;;;;;;;+DAF5C;;;;;;;;;;kEAOd,8OAAC;wDAAI,WAAU;kEACZ,QAAQ,YAAY,CAAC,GAAG,CAAC,CAAC,qBACzB,8OAAC;gEAEC,WAAU;0EAET;+DAHI;;;;;;;;;;kEAQX,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;;0FAAE,8OAAC;0FAAO;;;;;;4EAAgB;4EAAE,QAAQ,MAAM;;;;;;;kFAC3C,8OAAC;;0FAAE,8OAAC;0FAAO;;;;;;4EAAkB;4EAAE,QAAQ,QAAQ;;;;;;;;;;;;;0EAGjD,8OAAC;gEAAI,WAAU;;oEACZ,QAAQ,OAAO,kBACd,8OAAC,4JAAA,CAAA,UAAI;wEACH,MAAM,QAAQ,OAAO;wEACrB,QAAO;wEACP,KAAI;wEACJ,WAAU;;0FAEV,8OAAC,iPAAA,CAAA,4BAAyB;gFAAC,WAAU;;;;;;4EAAiB;;;;;;;oEAIzD,QAAQ,SAAS,kBAChB,8OAAC,4JAAA,CAAA,UAAI;wEACH,MAAM,QAAQ,SAAS;wEACvB,QAAO;wEACP,KAAI;wEACJ,WAAU;;0FAEV,8OAAC,6NAAA,CAAA,kBAAe;gFAAC,WAAU;;;;;;4EAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;uCA/EjD,QAAQ,EAAE;;;;;;;;;;;;;;;;kCA4FvB,8OAAC,0LAAA,CAAA,SAAM,CAAC,OAAO;wBACb,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAG;wBAC7B,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAE;wBAC5B,YAAY;4BAAE,UAAU;4BAAK,OAAO;wBAAI;wBACxC,WAAU;kCAEV,cAAA,8OAAC;4BAAI,WAAU;sCACZ,WAAW,GAAG,CAAC,CAAC,yBACf,8OAAC;oCAEC,WAAU;8CAET;mCAHI;;;;;;;;;;;;;;;kCAUb,8OAAC,0LAAA,CAAA,SAAM,CAAC,OAAO;wBACb,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAG;wBAC7B,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAE;wBAC5B,YAAY;4BAAE,UAAU;4BAAK,OAAO;wBAAI;;0CAExC,8OAAC;gCAAG,WAAU;0CAAqD;;;;;;0CACnE,8OAAC;gCAAI,WAAU;0CACZ,YAAY,GAAG,CAAC,CAAC,SAAS,sBACzB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wCAET,SAAS;4CAAE,SAAS;4CAAG,GAAG;wCAAG;wCAC7B,SAAS;4CAAE,SAAS;4CAAG,GAAG;wCAAE;wCAC5B,YAAY;4CAAE,UAAU;4CAAK,OAAO,MAAM;wCAAM;wCAChD,WAAU;;0DAEV,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,6HAAA,CAAA,UAAK;wDACJ,KAAK,QAAQ,KAAK;wDAClB,KAAK,QAAQ,KAAK;wDAClB,IAAI;wDACJ,WAAU;;;;;;kEAEZ,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC;4DAAI,WAAU;;gEACZ,QAAQ,OAAO,kBACd,8OAAC,4JAAA,CAAA,UAAI;oEACH,MAAM,QAAQ,OAAO;oEACrB,QAAO;oEACP,KAAI;oEACJ,WAAU;8EAEV,cAAA,8OAAC,iPAAA,CAAA,4BAAyB;wEAAC,WAAU;;;;;;;;;;;gEAGxC,QAAQ,SAAS,kBAChB,8OAAC,4JAAA,CAAA,UAAI;oEACH,MAAM,QAAQ,SAAS;oEACvB,QAAO;oEACP,KAAI;oEACJ,WAAU;8EAEV,cAAA,8OAAC,6NAAA,CAAA,kBAAe;wEAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;0DAOrC,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;;0EACb,8OAAC,QAAQ,IAAI;gEAAC,WAAU;;;;;;0EACxB,8OAAC;gEAAK,WAAU;0EAAqC,QAAQ,QAAQ;;;;;;;;;;;;kEAGvE,8OAAC;wDAAG,WAAU;kEACX,QAAQ,KAAK;;;;;;kEAGhB,8OAAC;wDAAE,WAAU;kEACV,QAAQ,WAAW;;;;;;kEAGtB,8OAAC;wDAAI,WAAU;;4DACZ,QAAQ,YAAY,CAAC,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,qBACrC,8OAAC;oEAEC,WAAU;8EAET;mEAHI;;;;;4DAMR,QAAQ,YAAY,CAAC,MAAM,GAAG,mBAC7B,8OAAC;gEAAK,WAAU;;oEAAsD;oEAClE,QAAQ,YAAY,CAAC,MAAM,GAAG;oEAAE;;;;;;;;;;;;;kEAKxC,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;;kFAAE,8OAAC;kFAAO;;;;;;oEAAgB;oEAAE,QAAQ,MAAM;;;;;;;0EAC3C,8OAAC;;kFAAE,8OAAC;kFAAO;;;;;;oEAAc;oEAAE,QAAQ,QAAQ;;;;;;;;;;;;;;;;;;;;uCAvE1C,QAAQ,EAAE;;;;;;;;;;;;;;;;kCAgFvB,8OAAC,0LAAA,CAAA,SAAM,CAAC,OAAO;wBACb,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAG;wBAC7B,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAE;wBAC5B,YAAY;4BAAE,UAAU;4BAAK,OAAO;wBAAI;wBACxC,WAAU;;0CAEV,8OAAC;gCAAG,WAAU;0CAAwC;;;;;;0CAGtD,8OAAC;gCAAE,WAAU;0CAAuC;;;;;;0CAIpD,8OAAC,4JAAA,CAAA,UAAI;gCAAC,MAAK;gCAAW,WAAU;0CAAc;;;;;;;;;;;;;;;;;;0BAKlD,8OAAC,4HAAA,CAAA,SAAM;;;;;;;;;;;AAGb", "debugId": null}}]}