{"name": "@technoloway/database", "version": "1.0.0", "private": true, "scripts": {"db:generate": "prisma generate", "db:push": "prisma db push", "db:migrate": "prisma migrate dev", "db:studio": "prisma studio", "db:seed": "tsx seed.ts", "type-check": "tsc --noEmit"}, "dependencies": {"@prisma/client": "^5.7.0", "bcryptjs": "^2.4.3", "zod": "^3.22.4"}, "devDependencies": {"@types/bcryptjs": "^2.4.6", "@types/node": "^20.10.0", "prisma": "^5.7.0", "tsx": "^4.6.0", "typescript": "^5.3.0"}}