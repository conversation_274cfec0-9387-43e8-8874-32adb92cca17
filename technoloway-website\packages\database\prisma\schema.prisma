// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model User {
  id        String   @id @default(cuid())
  email     String   @unique
  clerkId   String   @unique
  firstName String?
  lastName  String?
  imageUrl  String?
  role      UserRole @default(USER)
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relations
  posts     Post[]
  comments  Comment[]
  projects  Project[]
  inquiries ContactInquiry[]

  @@map("users")
}

model Post {
  id          String      @id @default(cuid())
  title       String
  slug        String      @unique
  content     String
  excerpt     String?
  coverImage  String?
  published   Boolean     @default(false)
  publishedAt DateTime?
  createdAt   DateTime    @default(now())
  updatedAt   DateTime    @updatedAt
  authorId    String
  categoryId  String?

  // Relations
  author   User      @relation(fields: [authorId], references: [id], onDelete: Cascade)
  category Category? @relation(fields: [categoryId], references: [id])
  comments Comment[]
  tags     PostTag[]

  @@map("posts")
}

model Category {
  id          String   @id @default(cuid())
  name        String   @unique
  slug        String   @unique
  description String?
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // Relations
  posts Post[]

  @@map("categories")
}

model Tag {
  id        String   @id @default(cuid())
  name      String   @unique
  slug      String   @unique
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relations
  posts PostTag[]

  @@map("tags")
}

model PostTag {
  id     String @id @default(cuid())
  postId String
  tagId  String

  // Relations
  post Post @relation(fields: [postId], references: [id], onDelete: Cascade)
  tag  Tag  @relation(fields: [tagId], references: [id], onDelete: Cascade)

  @@unique([postId, tagId])
  @@map("post_tags")
}

model Comment {
  id        String   @id @default(cuid())
  content   String
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  authorId  String
  postId    String

  // Relations
  author User @relation(fields: [authorId], references: [id], onDelete: Cascade)
  post   Post @relation(fields: [postId], references: [id], onDelete: Cascade)

  @@map("comments")
}

model Project {
  id          String        @id @default(cuid())
  title       String
  description String
  imageUrl    String?
  projectUrl  String?
  githubUrl   String?
  technologies String[]
  status      ProjectStatus @default(ACTIVE)
  featured    Boolean       @default(false)
  createdAt   DateTime      @default(now())
  updatedAt   DateTime      @updatedAt
  authorId    String

  // Relations
  author User @relation(fields: [authorId], references: [id], onDelete: Cascade)

  @@map("projects")
}

model ContactInquiry {
  id          String        @id @default(cuid())
  name        String
  email       String
  company     String?
  phone       String?
  subject     String
  message     String
  status      InquiryStatus @default(PENDING)
  createdAt   DateTime      @default(now())
  updatedAt   DateTime      @updatedAt
  assignedToId String?

  // Relations
  assignedTo User? @relation(fields: [assignedToId], references: [id])

  @@map("contact_inquiries")
}

model Service {
  id          String   @id @default(cuid())
  title       String
  description String
  icon        String?
  features    String[]
  price       String?
  popular     Boolean  @default(false)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  @@map("services")
}

model Testimonial {
  id        String   @id @default(cuid())
  name      String
  position  String
  company   String
  content   String
  rating    Int      @default(5)
  imageUrl  String?
  featured  Boolean  @default(false)
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@map("testimonials")
}

enum UserRole {
  USER
  ADMIN
  EDITOR
}

enum ProjectStatus {
  ACTIVE
  COMPLETED
  ARCHIVED
}

enum InquiryStatus {
  PENDING
  IN_PROGRESS
  RESOLVED
  CLOSED
}
