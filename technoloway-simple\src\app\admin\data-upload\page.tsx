'use client';

import { useState, useCallback } from 'react';
import { motion } from 'framer-motion';
import {
  CloudArrowUpIcon,
  DocumentIcon,
  CheckCircleIcon,
  ExclamationTriangleIcon,
  XCircleIcon,
  TrashIcon,
  EyeIcon,
  ArrowDownTrayIcon,
} from '@heroicons/react/24/outline';

// Mock data - in real app, this would come from API
const uploadHistory = [
  {
    id: 1,
    fileName: 'team_members.csv',
    fileSize: 2048,
    uploadDate: '2024-01-22T10:30:00Z',
    status: 'Completed',
    recordsProcessed: 8,
    recordsSuccess: 8,
    recordsError: 0,
    dataType: 'Team Members',
    uploadedBy: 'Admin User',
    errors: [],
  },
  {
    id: 2,
    fileName: 'projects_data.json',
    fileSize: 15360,
    uploadDate: '2024-01-20T14:15:00Z',
    status: 'Completed',
    recordsProcessed: 25,
    recordsSuccess: 23,
    recordsError: 2,
    dataType: 'Projects',
    uploadedBy: 'Admin User',
    errors: [
      'Row 5: Invalid date format for project start date',
      'Row 12: Missing required field: client_id'
    ],
  },
  {
    id: 3,
    fileName: 'client_contacts.xlsx',
    fileSize: 8192,
    uploadDate: '2024-01-18T09:45:00Z',
    status: 'Failed',
    recordsProcessed: 0,
    recordsSuccess: 0,
    recordsError: 15,
    dataType: 'Clients',
    uploadedBy: 'Admin User',
    errors: [
      'Invalid file format: Expected CSV or JSON',
      'File contains unsupported characters'
    ],
  },
  {
    id: 4,
    fileName: 'testimonials.csv',
    fileSize: 4096,
    uploadDate: '2024-01-15T16:20:00Z',
    status: 'Processing',
    recordsProcessed: 5,
    recordsSuccess: 5,
    recordsError: 0,
    dataType: 'Testimonials',
    uploadedBy: 'Admin User',
    errors: [],
  },
];

const dataTypes = [
  { value: 'team-members', label: 'Team Members', description: 'Upload team member profiles and information' },
  { value: 'projects', label: 'Projects', description: 'Import project data and details' },
  { value: 'clients', label: 'Clients', description: 'Add client information and contacts' },
  { value: 'testimonials', label: 'Testimonials', description: 'Import customer testimonials and reviews' },
  { value: 'blog-posts', label: 'Blog Posts', description: 'Bulk upload blog content' },
  { value: 'services', label: 'Services', description: 'Import service offerings and pricing' },
  { value: 'technologies', label: 'Technologies', description: 'Upload technology stack information' },
];

export default function DataUploadPage() {
  const [selectedDataType, setSelectedDataType] = useState('');
  const [dragActive, setDragActive] = useState(false);
  const [uploadedFiles, setUploadedFiles] = useState<File[]>([]);
  const [uploadProgress, setUploadProgress] = useState<{ [key: string]: number }>({});
  const [selectedUpload, setSelectedUpload] = useState<any>(null);

  const handleDrag = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    if (e.type === 'dragenter' || e.type === 'dragover') {
      setDragActive(true);
    } else if (e.type === 'dragleave') {
      setDragActive(false);
    }
  }, []);

  const handleDrop = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setDragActive(false);
    
    if (e.dataTransfer.files && e.dataTransfer.files[0]) {
      const files = Array.from(e.dataTransfer.files);
      setUploadedFiles(prev => [...prev, ...files]);
      
      // Simulate upload progress
      files.forEach(file => {
        simulateUpload(file.name);
      });
    }
  }, []);

  const handleFileSelect = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files) {
      const files = Array.from(e.target.files);
      setUploadedFiles(prev => [...prev, ...files]);
      
      // Simulate upload progress
      files.forEach(file => {
        simulateUpload(file.name);
      });
    }
  };

  const simulateUpload = (fileName: string) => {
    let progress = 0;
    const interval = setInterval(() => {
      progress += Math.random() * 30;
      if (progress >= 100) {
        progress = 100;
        clearInterval(interval);
      }
      setUploadProgress(prev => ({ ...prev, [fileName]: progress }));
    }, 500);
  };

  const removeFile = (index: number) => {
    setUploadedFiles(prev => prev.filter((_, i) => i !== index));
  };

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'Completed': return 'bg-green-100 text-green-800';
      case 'Processing': return 'bg-blue-100 text-blue-800';
      case 'Failed': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'Completed': return CheckCircleIcon;
      case 'Processing': return CloudArrowUpIcon;
      case 'Failed': return XCircleIcon;
      default: return DocumentIcon;
    }
  };

  return (
    <div className="py-6">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 md:px-8">
        {/* Header */}
        <div className="mb-8">
          <h2 className="text-2xl font-bold leading-7 text-gray-900 sm:text-3xl sm:truncate">
            Data Upload
          </h2>
          <p className="mt-1 text-sm text-gray-500">
            Import data from CSV, JSON, or Excel files to populate your database
          </p>
        </div>

        {/* Upload Section */}
        <div className="mb-8 bg-white shadow rounded-lg">
          <div className="px-4 py-5 sm:p-6">
            <h3 className="text-lg leading-6 font-medium text-gray-900 mb-4">
              Upload New Data
            </h3>
            
            {/* Data Type Selection */}
            <div className="mb-6">
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Select Data Type
              </label>
              <select
                value={selectedDataType}
                onChange={(e) => setSelectedDataType(e.target.value)}
                className="block w-full pl-3 pr-10 py-2 text-base border border-gray-300 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm rounded-md"
              >
                <option value="">Choose data type...</option>
                {dataTypes.map(type => (
                  <option key={type.value} value={type.value}>{type.label}</option>
                ))}
              </select>
              {selectedDataType && (
                <p className="mt-1 text-sm text-gray-500">
                  {dataTypes.find(t => t.value === selectedDataType)?.description}
                </p>
              )}
            </div>

            {/* File Upload Area */}
            <div
              className={`relative border-2 border-dashed rounded-lg p-6 transition-colors ${
                dragActive 
                  ? 'border-blue-400 bg-blue-50' 
                  : 'border-gray-300 hover:border-gray-400'
              }`}
              onDragEnter={handleDrag}
              onDragLeave={handleDrag}
              onDragOver={handleDrag}
              onDrop={handleDrop}
            >
              <div className="text-center">
                <CloudArrowUpIcon className="mx-auto h-12 w-12 text-gray-400" />
                <div className="mt-4">
                  <label htmlFor="file-upload" className="cursor-pointer">
                    <span className="mt-2 block text-sm font-medium text-gray-900">
                      Drop files here or click to upload
                    </span>
                    <input
                      id="file-upload"
                      name="file-upload"
                      type="file"
                      className="sr-only"
                      multiple
                      accept=".csv,.json,.xlsx,.xls"
                      onChange={handleFileSelect}
                    />
                  </label>
                  <p className="mt-1 text-sm text-gray-500">
                    Supports CSV, JSON, and Excel files up to 10MB
                  </p>
                </div>
              </div>
            </div>

            {/* Uploaded Files */}
            {uploadedFiles.length > 0 && (
              <div className="mt-6">
                <h4 className="text-sm font-medium text-gray-900 mb-3">Uploaded Files</h4>
                <div className="space-y-3">
                  {uploadedFiles.map((file, index) => (
                    <div key={index} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                      <div className="flex items-center space-x-3">
                        <DocumentIcon className="h-8 w-8 text-gray-400" />
                        <div>
                          <p className="text-sm font-medium text-gray-900">{file.name}</p>
                          <p className="text-sm text-gray-500">{formatFileSize(file.size)}</p>
                        </div>
                      </div>
                      <div className="flex items-center space-x-3">
                        {uploadProgress[file.name] !== undefined && (
                          <div className="w-32">
                            <div className="bg-gray-200 rounded-full h-2">
                              <div
                                className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                                style={{ width: `${uploadProgress[file.name]}%` }}
                              ></div>
                            </div>
                            <p className="text-xs text-gray-500 mt-1">
                              {Math.round(uploadProgress[file.name])}%
                            </p>
                          </div>
                        )}
                        <button
                          onClick={() => removeFile(index)}
                          className="text-gray-400 hover:text-red-600 transition-colors"
                        >
                          <TrashIcon className="h-5 w-5" />
                        </button>
                      </div>
                    </div>
                  ))}
                </div>
                
                <div className="mt-4 flex justify-end">
                  <button
                    disabled={!selectedDataType}
                    className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    Process Upload
                  </button>
                </div>
              </div>
            )}
          </div>
        </div>

        {/* Upload History */}
        <div className="bg-white shadow rounded-lg">
          <div className="px-4 py-5 sm:p-6">
            <h3 className="text-lg leading-6 font-medium text-gray-900 mb-4">
              Upload History
            </h3>
            
            <div className="overflow-hidden">
              <ul className="divide-y divide-gray-200">
                {uploadHistory.map((upload, index) => {
                  const StatusIcon = getStatusIcon(upload.status);
                  return (
                    <motion.li
                      key={upload.id}
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ duration: 0.3, delay: index * 0.1 }}
                      className="py-4"
                    >
                      <div className="flex items-center justify-between">
                        <div className="flex items-start space-x-4">
                          <div className={`w-10 h-10 rounded-lg flex items-center justify-center ${getStatusColor(upload.status)}`}>
                            <StatusIcon className="w-6 h-6" />
                          </div>
                          
                          <div className="flex-1 min-w-0">
                            <div className="flex items-center space-x-2 mb-1">
                              <h4 className="text-sm font-medium text-gray-900">{upload.fileName}</h4>
                              <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(upload.status)}`}>
                                {upload.status}
                              </span>
                            </div>
                            
                            <div className="flex items-center space-x-4 text-sm text-gray-500 mb-2">
                              <span>{upload.dataType}</span>
                              <span>{formatFileSize(upload.fileSize)}</span>
                              <span>Uploaded: {formatDate(upload.uploadDate)}</span>
                              <span>By: {upload.uploadedBy}</span>
                            </div>
                            
                            <div className="flex items-center space-x-4 text-sm">
                              <span className="text-green-600">
                                ✓ {upload.recordsSuccess} successful
                              </span>
                              {upload.recordsError > 0 && (
                                <span className="text-red-600">
                                  ✗ {upload.recordsError} errors
                                </span>
                              )}
                              <span className="text-gray-500">
                                Total: {upload.recordsProcessed} records
                              </span>
                            </div>
                            
                            {upload.errors.length > 0 && (
                              <div className="mt-2">
                                <details className="text-sm">
                                  <summary className="cursor-pointer text-red-600 hover:text-red-800">
                                    View {upload.errors.length} error(s)
                                  </summary>
                                  <ul className="mt-1 ml-4 list-disc text-red-600">
                                    {upload.errors.map((error, i) => (
                                      <li key={i}>{error}</li>
                                    ))}
                                  </ul>
                                </details>
                              </div>
                            )}
                          </div>
                        </div>
                        
                        <div className="flex items-center space-x-2">
                          <button
                            onClick={() => setSelectedUpload(upload)}
                            className="text-gray-400 hover:text-blue-600 transition-colors"
                            title="View Details"
                          >
                            <EyeIcon className="h-5 w-5" />
                          </button>
                          <button
                            className="text-gray-400 hover:text-green-600 transition-colors"
                            title="Download Report"
                          >
                            <ArrowDownTrayIcon className="h-5 w-5" />
                          </button>
                          <button
                            className="text-gray-400 hover:text-red-600 transition-colors"
                            title="Delete"
                          >
                            <TrashIcon className="h-5 w-5" />
                          </button>
                        </div>
                      </div>
                    </motion.li>
                  );
                })}
              </ul>
            </div>
          </div>
        </div>

        {/* Upload Detail Modal */}
        {selectedUpload && (
          <div className="fixed inset-0 z-50 overflow-y-auto">
            <div className="flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
              <div className="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" onClick={() => setSelectedUpload(null)} />
              
              <div className="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-2xl sm:w-full">
                <div className="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
                  <div className="sm:flex sm:items-start">
                    <div className="mt-3 text-center sm:mt-0 sm:text-left w-full">
                      <h3 className="text-lg leading-6 font-medium text-gray-900 mb-4">
                        Upload Details: {selectedUpload.fileName}
                      </h3>
                      
                      <div className="space-y-4">
                        <div className="grid grid-cols-2 gap-4">
                          <div>
                            <label className="block text-sm font-medium text-gray-700">Status</label>
                            <span className={`mt-1 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(selectedUpload.status)}`}>
                              {selectedUpload.status}
                            </span>
                          </div>
                          <div>
                            <label className="block text-sm font-medium text-gray-700">Data Type</label>
                            <p className="mt-1 text-sm text-gray-900">{selectedUpload.dataType}</p>
                          </div>
                          <div>
                            <label className="block text-sm font-medium text-gray-700">File Size</label>
                            <p className="mt-1 text-sm text-gray-900">{formatFileSize(selectedUpload.fileSize)}</p>
                          </div>
                          <div>
                            <label className="block text-sm font-medium text-gray-700">Upload Date</label>
                            <p className="mt-1 text-sm text-gray-900">{formatDate(selectedUpload.uploadDate)}</p>
                          </div>
                        </div>
                        
                        <div>
                          <label className="block text-sm font-medium text-gray-700">Processing Summary</label>
                          <div className="mt-1 bg-gray-50 rounded-lg p-3">
                            <div className="grid grid-cols-3 gap-4 text-sm">
                              <div className="text-center">
                                <div className="text-lg font-semibold text-gray-900">{selectedUpload.recordsProcessed}</div>
                                <div className="text-gray-500">Total Records</div>
                              </div>
                              <div className="text-center">
                                <div className="text-lg font-semibold text-green-600">{selectedUpload.recordsSuccess}</div>
                                <div className="text-gray-500">Successful</div>
                              </div>
                              <div className="text-center">
                                <div className="text-lg font-semibold text-red-600">{selectedUpload.recordsError}</div>
                                <div className="text-gray-500">Errors</div>
                              </div>
                            </div>
                          </div>
                        </div>
                        
                        {selectedUpload.errors.length > 0 && (
                          <div>
                            <label className="block text-sm font-medium text-gray-700">Errors</label>
                            <div className="mt-1 bg-red-50 rounded-lg p-3">
                              <ul className="text-sm text-red-700 space-y-1">
                                {selectedUpload.errors.map((error: string, index: number) => (
                                  <li key={index}>• {error}</li>
                                ))}
                              </ul>
                            </div>
                          </div>
                        )}
                      </div>
                    </div>
                  </div>
                </div>
                <div className="bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
                  <button
                    type="button"
                    className="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-blue-600 text-base font-medium text-white hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 sm:ml-3 sm:w-auto sm:text-sm"
                  >
                    Download Report
                  </button>
                  <button
                    type="button"
                    className="mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 sm:mt-0 sm:w-auto sm:text-sm"
                    onClick={() => setSelectedUpload(null)}
                  >
                    Close
                  </button>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
