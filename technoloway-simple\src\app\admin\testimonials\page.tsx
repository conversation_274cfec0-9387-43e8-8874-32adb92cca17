'use client';

import { useState } from 'react';
import { motion } from 'framer-motion';
import {
  PlusIcon,
  PencilIcon,
  TrashIcon,
  EyeIcon,
  StarIcon,
  CheckCircleIcon,
  XCircleIcon,
  MagnifyingGlassIcon,
} from '@heroicons/react/24/outline';

// Mock data - in real app, this would come from API
const testimonials = [
  {
    id: 1,
    content: "Technoloway transformed our outdated system into a modern, scalable platform. Their expertise in cloud architecture and attention to detail exceeded our expectations.",
    author: "<PERSON>",
    role: "CTO",
    company: "GreenTech Solutions",
    image: "https://images.unsplash.com/photo-1494790108755-2616b612b786?w=100&h=100&fit=crop&crop=face",
    rating: 5,
    isPublished: true,
    isFeatured: true,
    projectId: 1,
    projectName: "EcoCommerce Platform",
    submittedAt: '2024-01-20T10:00:00Z',
    publishedAt: '2024-01-21T14:30:00Z',
    createdAt: '2024-01-20T10:00:00Z',
    updatedAt: '2024-01-21T14:30:00Z',
  },
  {
    id: 2,
    content: "The mobile app they developed for us has been a game-changer. User engagement increased by 85% and our revenue grew by 150% in just 6 months.",
    author: "<PERSON>",
    role: "CEO",
    company: "MedTech Innovations",
    image: "https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=100&h=100&fit=crop&crop=face",
    rating: 5,
    isPublished: true,
    isFeatured: true,
    projectId: 2,
    projectName: "HealthTracker Mobile App",
    submittedAt: '2024-01-18T11:00:00Z',
    publishedAt: '2024-01-19T16:45:00Z',
    createdAt: '2024-01-18T11:00:00Z',
    updatedAt: '2024-01-19T16:45:00Z',
  },
  {
    id: 3,
    content: "Working with Technoloway was seamless. They delivered our e-commerce platform on time and within budget. The results speak for themselves - 200% increase in online sales.",
    author: "Emily Rodriguez",
    role: "Founder",
    company: "EcoCommerce",
    image: "https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=100&h=100&fit=crop&crop=face",
    rating: 5,
    isPublished: true,
    isFeatured: false,
    projectId: 3,
    projectName: "EcoCommerce Platform",
    submittedAt: '2024-01-15T09:00:00Z',
    publishedAt: '2024-01-16T13:20:00Z',
    createdAt: '2024-01-15T09:00:00Z',
    updatedAt: '2024-01-16T13:20:00Z',
  },
  {
    id: 4,
    content: "Their security audit identified critical vulnerabilities we didn't know existed. The team's expertise in cybersecurity gave us peace of mind and protected our business.",
    author: "David Park",
    role: "CISO",
    company: "FinanceFlow Corp",
    image: "https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=100&h=100&fit=crop&crop=face",
    rating: 5,
    isPublished: false,
    isFeatured: false,
    projectId: 4,
    projectName: "Security Audit",
    submittedAt: '2024-01-12T14:00:00Z',
    publishedAt: null,
    createdAt: '2024-01-12T14:00:00Z',
    updatedAt: '2024-01-13T10:15:00Z',
  },
  {
    id: 5,
    content: "The analytics dashboard they built provides incredible insights into our business. Data-driven decisions have never been easier to make.",
    author: "Lisa Wang",
    role: "VP of Operations",
    company: "DataFlow Analytics",
    image: "https://images.unsplash.com/photo-1517841905240-472988babdf9?w=100&h=100&fit=crop&crop=face",
    rating: 4,
    isPublished: true,
    isFeatured: false,
    projectId: 5,
    projectName: "Analytics Dashboard",
    submittedAt: '2024-01-10T16:00:00Z',
    publishedAt: '2024-01-11T12:30:00Z',
    createdAt: '2024-01-10T16:00:00Z',
    updatedAt: '2024-01-11T12:30:00Z',
  },
];

const statuses = ['All', 'Published', 'Unpublished', 'Featured'];
const ratings = ['All', '5 Stars', '4 Stars', '3 Stars', '2 Stars', '1 Star'];

export default function TestimonialsPage() {
  const [testimonialsList, setTestimonialsList] = useState(testimonials);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedStatus, setSelectedStatus] = useState('All');
  const [selectedRating, setSelectedRating] = useState('All');
  const [selectedTestimonial, setSelectedTestimonial] = useState<any>(null);
  const [showAddModal, setShowAddModal] = useState(false);

  const filteredTestimonials = testimonialsList.filter(testimonial => {
    const matchesSearch = testimonial.author.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         testimonial.company.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         testimonial.content.toLowerCase().includes(searchTerm.toLowerCase());
    
    let matchesStatus = true;
    if (selectedStatus === 'Published') matchesStatus = testimonial.isPublished;
    else if (selectedStatus === 'Unpublished') matchesStatus = !testimonial.isPublished;
    else if (selectedStatus === 'Featured') matchesStatus = testimonial.isFeatured;
    
    let matchesRating = true;
    if (selectedRating !== 'All') {
      const ratingValue = parseInt(selectedRating.charAt(0));
      matchesRating = testimonial.rating === ratingValue;
    }
    
    return matchesSearch && matchesStatus && matchesRating;
  });

  const handleTogglePublished = (id: number) => {
    setTestimonialsList(prev => prev.map(testimonial => 
      testimonial.id === id 
        ? { 
            ...testimonial, 
            isPublished: !testimonial.isPublished,
            publishedAt: !testimonial.isPublished ? new Date().toISOString() : null
          }
        : testimonial
    ));
  };

  const handleToggleFeatured = (id: number) => {
    setTestimonialsList(prev => prev.map(testimonial => 
      testimonial.id === id 
        ? { ...testimonial, isFeatured: !testimonial.isFeatured }
        : testimonial
    ));
  };

  const handleDelete = (id: number) => {
    if (confirm('Are you sure you want to delete this testimonial?')) {
      setTestimonialsList(prev => prev.filter(testimonial => testimonial.id !== id));
    }
  };

  const formatDate = (dateString: string | null) => {
    if (!dateString) return 'Not published';
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  const renderStars = (rating: number) => {
    return [...Array(5)].map((_, i) => (
      <StarIcon
        key={i}
        className={`w-4 h-4 ${
          i < rating ? 'text-yellow-400 fill-current' : 'text-gray-300'
        }`}
      />
    ));
  };

  return (
    <div className="py-6">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 md:px-8">
        {/* Header */}
        <div className="md:flex md:items-center md:justify-between mb-8">
          <div className="flex-1 min-w-0">
            <h2 className="text-2xl font-bold leading-7 text-gray-900 sm:text-3xl sm:truncate">
              Testimonials
            </h2>
            <p className="mt-1 text-sm text-gray-500">
              Manage client testimonials and reviews for your services
            </p>
          </div>
          <div className="mt-4 flex md:mt-0 md:ml-4">
            <button
              onClick={() => setShowAddModal(true)}
              className="ml-3 inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
            >
              <PlusIcon className="-ml-1 mr-2 h-5 w-5" />
              Add Testimonial
            </button>
          </div>
        </div>

        {/* Stats */}
        <div className="mb-6 grid grid-cols-1 gap-5 sm:grid-cols-4">
          <div className="bg-white overflow-hidden shadow rounded-lg">
            <div className="p-5">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <div className="w-8 h-8 bg-blue-500 rounded-md flex items-center justify-center">
                    <span className="text-white text-sm font-bold">{testimonialsList.length}</span>
                  </div>
                </div>
                <div className="ml-5 w-0 flex-1">
                  <dl>
                    <dt className="text-sm font-medium text-gray-500 truncate">Total Testimonials</dt>
                    <dd className="text-lg font-medium text-gray-900">Collected</dd>
                  </dl>
                </div>
              </div>
            </div>
          </div>

          <div className="bg-white overflow-hidden shadow rounded-lg">
            <div className="p-5">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <div className="w-8 h-8 bg-green-500 rounded-md flex items-center justify-center">
                    <span className="text-white text-sm font-bold">
                      {testimonialsList.filter(t => t.isPublished).length}
                    </span>
                  </div>
                </div>
                <div className="ml-5 w-0 flex-1">
                  <dl>
                    <dt className="text-sm font-medium text-gray-500 truncate">Published</dt>
                    <dd className="text-lg font-medium text-gray-900">Live</dd>
                  </dl>
                </div>
              </div>
            </div>
          </div>

          <div className="bg-white overflow-hidden shadow rounded-lg">
            <div className="p-5">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <div className="w-8 h-8 bg-yellow-500 rounded-md flex items-center justify-center">
                    <span className="text-white text-sm font-bold">
                      {testimonialsList.filter(t => t.isFeatured).length}
                    </span>
                  </div>
                </div>
                <div className="ml-5 w-0 flex-1">
                  <dl>
                    <dt className="text-sm font-medium text-gray-500 truncate">Featured</dt>
                    <dd className="text-lg font-medium text-gray-900">Highlighted</dd>
                  </dl>
                </div>
              </div>
            </div>
          </div>

          <div className="bg-white overflow-hidden shadow rounded-lg">
            <div className="p-5">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <div className="w-8 h-8 bg-purple-500 rounded-md flex items-center justify-center">
                    <span className="text-white text-sm font-bold">
                      {(testimonialsList.reduce((sum, t) => sum + t.rating, 0) / testimonialsList.length).toFixed(1)}
                    </span>
                  </div>
                </div>
                <div className="ml-5 w-0 flex-1">
                  <dl>
                    <dt className="text-sm font-medium text-gray-500 truncate">Avg Rating</dt>
                    <dd className="text-lg font-medium text-gray-900">Stars</dd>
                  </dl>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Filters */}
        <div className="mb-6 bg-white shadow rounded-lg">
          <div className="px-4 py-5 sm:p-6">
            <div className="grid grid-cols-1 gap-4 sm:grid-cols-3">
              {/* Search */}
              <div className="relative">
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <MagnifyingGlassIcon className="h-5 w-5 text-gray-400" />
                </div>
                <input
                  type="text"
                  placeholder="Search testimonials..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                />
              </div>

              {/* Status Filter */}
              <div>
                <select
                  value={selectedStatus}
                  onChange={(e) => setSelectedStatus(e.target.value)}
                  className="block w-full pl-3 pr-10 py-2 text-base border border-gray-300 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm rounded-md"
                >
                  {statuses.map(status => (
                    <option key={status} value={status}>{status}</option>
                  ))}
                </select>
              </div>

              {/* Rating Filter */}
              <div>
                <select
                  value={selectedRating}
                  onChange={(e) => setSelectedRating(e.target.value)}
                  className="block w-full pl-3 pr-10 py-2 text-base border border-gray-300 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm rounded-md"
                >
                  {ratings.map(rating => (
                    <option key={rating} value={rating}>{rating}</option>
                  ))}
                </select>
              </div>
            </div>
          </div>
        </div>

        {/* Testimonials List */}
        <div className="space-y-6">
          {filteredTestimonials.map((testimonial, index) => (
            <motion.div
              key={testimonial.id}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.3, delay: index * 0.1 }}
              className="bg-white shadow rounded-lg overflow-hidden"
            >
              <div className="p-6">
                <div className="flex items-start justify-between mb-4">
                  <div className="flex items-start space-x-4">
                    <div className="flex-shrink-0">
                      <img
                        className="h-12 w-12 rounded-full object-cover"
                        src={testimonial.image}
                        alt={testimonial.author}
                      />
                    </div>
                    <div className="flex-1">
                      <div className="flex items-center space-x-2 mb-1">
                        <h3 className="text-lg font-medium text-gray-900">
                          {testimonial.author}
                        </h3>
                        <div className="flex items-center space-x-1">
                          {testimonial.isPublished ? (
                            <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                              <CheckCircleIcon className="w-4 h-4 mr-1" />
                              Published
                            </span>
                          ) : (
                            <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                              <XCircleIcon className="w-4 h-4 mr-1" />
                              Draft
                            </span>
                          )}
                          {testimonial.isFeatured && (
                            <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                              <StarIcon className="w-4 h-4 mr-1" />
                              Featured
                            </span>
                          )}
                        </div>
                      </div>
                      <p className="text-sm text-gray-500 mb-2">
                        {testimonial.role} at {testimonial.company}
                      </p>
                      <div className="flex items-center space-x-1 mb-3">
                        {renderStars(testimonial.rating)}
                        <span className="text-sm text-gray-500 ml-2">
                          {testimonial.rating}/5 stars
                        </span>
                      </div>
                    </div>
                  </div>
                  
                  <div className="flex items-center space-x-2">
                    <button
                      onClick={() => setSelectedTestimonial(testimonial)}
                      className="text-gray-400 hover:text-blue-600 transition-colors"
                      title="View Details"
                    >
                      <EyeIcon className="h-5 w-5" />
                    </button>
                    <button
                      className="text-gray-400 hover:text-blue-600 transition-colors"
                      title="Edit"
                    >
                      <PencilIcon className="h-5 w-5" />
                    </button>
                    <button
                      onClick={() => handleTogglePublished(testimonial.id)}
                      className={`text-gray-400 hover:text-green-600 transition-colors ${
                        testimonial.isPublished ? 'text-green-600' : ''
                      }`}
                      title={testimonial.isPublished ? 'Unpublish' : 'Publish'}
                    >
                      <CheckCircleIcon className="h-5 w-5" />
                    </button>
                    <button
                      onClick={() => handleToggleFeatured(testimonial.id)}
                      className={`text-gray-400 hover:text-yellow-600 transition-colors ${
                        testimonial.isFeatured ? 'text-yellow-600' : ''
                      }`}
                      title={testimonial.isFeatured ? 'Remove from Featured' : 'Add to Featured'}
                    >
                      <StarIcon className="h-5 w-5" />
                    </button>
                    <button
                      onClick={() => handleDelete(testimonial.id)}
                      className="text-gray-400 hover:text-red-600 transition-colors"
                      title="Delete"
                    >
                      <TrashIcon className="h-5 w-5" />
                    </button>
                  </div>
                </div>

                <blockquote className="text-gray-700 mb-4 text-lg leading-relaxed">
                  "{testimonial.content}"
                </blockquote>

                <div className="flex items-center justify-between text-sm text-gray-500">
                  <span>Project: {testimonial.projectName}</span>
                  <div className="flex items-center space-x-4">
                    <span>Submitted: {formatDate(testimonial.submittedAt)}</span>
                    {testimonial.publishedAt && (
                      <span>Published: {formatDate(testimonial.publishedAt)}</span>
                    )}
                  </div>
                </div>
              </div>
            </motion.div>
          ))}
        </div>

        {/* Empty State */}
        {filteredTestimonials.length === 0 && (
          <div className="bg-white shadow rounded-lg">
            <div className="text-center py-12">
              <StarIcon className="mx-auto h-12 w-12 text-gray-400" />
              <h3 className="mt-2 text-sm font-medium text-gray-900">No testimonials found</h3>
              <p className="mt-1 text-sm text-gray-500">
                Try adjusting your search or filter criteria.
              </p>
            </div>
          </div>
        )}

        {/* Testimonial Detail Modal */}
        {selectedTestimonial && (
          <div className="fixed inset-0 z-50 overflow-y-auto">
            <div className="flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
              <div className="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" onClick={() => setSelectedTestimonial(null)} />
              
              <div className="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-2xl sm:w-full">
                <div className="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
                  <div className="sm:flex sm:items-start">
                    <div className="mt-3 text-center sm:mt-0 sm:text-left w-full">
                      <div className="flex items-center space-x-3 mb-4">
                        <img
                          className="h-12 w-12 rounded-full object-cover"
                          src={selectedTestimonial.image}
                          alt={selectedTestimonial.author}
                        />
                        <div>
                          <h3 className="text-lg leading-6 font-medium text-gray-900">
                            {selectedTestimonial.author}
                          </h3>
                          <p className="text-sm text-gray-500">
                            {selectedTestimonial.role} at {selectedTestimonial.company}
                          </p>
                        </div>
                      </div>
                      
                      <div className="space-y-4">
                        <div>
                          <label className="block text-sm font-medium text-gray-700">Rating</label>
                          <div className="mt-1 flex items-center space-x-1">
                            {renderStars(selectedTestimonial.rating)}
                            <span className="text-sm text-gray-500 ml-2">
                              {selectedTestimonial.rating}/5 stars
                            </span>
                          </div>
                        </div>
                        
                        <div>
                          <label className="block text-sm font-medium text-gray-700">Testimonial</label>
                          <blockquote className="mt-1 text-sm text-gray-900 italic">
                            "{selectedTestimonial.content}"
                          </blockquote>
                        </div>
                        
                        <div>
                          <label className="block text-sm font-medium text-gray-700">Project</label>
                          <p className="mt-1 text-sm text-gray-900">{selectedTestimonial.projectName}</p>
                        </div>
                        
                        <div className="grid grid-cols-2 gap-4">
                          <div>
                            <label className="block text-sm font-medium text-gray-700">Status</label>
                            <p className="mt-1 text-sm text-gray-900">
                              {selectedTestimonial.isPublished ? 'Published' : 'Draft'}
                            </p>
                          </div>
                          <div>
                            <label className="block text-sm font-medium text-gray-700">Featured</label>
                            <p className="mt-1 text-sm text-gray-900">
                              {selectedTestimonial.isFeatured ? 'Yes' : 'No'}
                            </p>
                          </div>
                        </div>
                        
                        <div>
                          <label className="block text-sm font-medium text-gray-700">Submitted</label>
                          <p className="mt-1 text-sm text-gray-900">{formatDate(selectedTestimonial.submittedAt)}</p>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
                <div className="bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
                  <button
                    type="button"
                    className="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-blue-600 text-base font-medium text-white hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 sm:ml-3 sm:w-auto sm:text-sm"
                  >
                    Edit Testimonial
                  </button>
                  <button
                    type="button"
                    className="mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 sm:mt-0 sm:w-auto sm:text-sm"
                    onClick={() => setSelectedTestimonial(null)}
                  >
                    Close
                  </button>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
