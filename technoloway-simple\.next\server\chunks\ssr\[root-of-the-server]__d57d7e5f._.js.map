{"version": 3, "sources": [], "sections": [{"offset": {"line": 39, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Project%20By%20AI/Technoloway/Technoloway%20%28Processing%29/technoloway-simple/src/components/header.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport Link from 'next/link';\nimport { usePathname } from 'next/navigation';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport { Bars3Icon, XMarkIcon } from '@heroicons/react/24/outline';\n\nconst navigation = [\n  { name: 'Home', href: '/' },\n  { name: 'About', href: '/about' },\n  { name: 'Services', href: '/services' },\n  { name: 'Projects', href: '/projects' },\n  { name: 'Technologies', href: '/technologies' },\n  { name: 'Contact', href: '/contact' },\n];\n\nexport function Header() {\n  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);\n  const pathname = usePathname();\n\n  const isActive = (href: string) => {\n    if (href === '/') {\n      return pathname === '/';\n    }\n    return pathname.startsWith(href);\n  };\n\n  return (\n    <header className=\"fixed inset-x-0 top-0 z-50 bg-white/80 backdrop-blur-md border-b border-gray-200\">\n      <nav className=\"container flex items-center justify-between py-4\" aria-label=\"Global\">\n        <div className=\"flex lg:flex-1\">\n          <Link href=\"/\" className=\"-m-1.5 p-1.5\">\n            <span className=\"sr-only\">Technoloway</span>\n            <div className=\"flex items-center space-x-2\">\n              <div className=\"h-8 w-8 bg-gradient-to-r from-blue-600 to-blue-400 rounded-lg flex items-center justify-center\">\n                <span className=\"text-white font-bold text-sm\">T</span>\n              </div>\n              <span className=\"text-xl font-bold text-gray-900\">Technoloway</span>\n            </div>\n          </Link>\n        </div>\n        \n        <div className=\"flex lg:hidden\">\n          <button\n            type=\"button\"\n            className=\"-m-2.5 inline-flex items-center justify-center rounded-md p-2.5 text-gray-700\"\n            onClick={() => setMobileMenuOpen(true)}\n          >\n            <span className=\"sr-only\">Open main menu</span>\n            <Bars3Icon className=\"h-6 w-6\" aria-hidden=\"true\" />\n          </button>\n        </div>\n        \n        <div className=\"hidden lg:flex lg:gap-x-8\">\n          {navigation.map((item) => (\n            <Link\n              key={item.name}\n              href={item.href}\n              className={`text-sm font-semibold leading-6 transition-colors hover:text-blue-600 ${\n                isActive(item.href) ? 'text-blue-600' : 'text-gray-900'\n              }`}\n            >\n              {item.name}\n            </Link>\n          ))}\n        </div>\n        \n        <div className=\"hidden lg:flex lg:flex-1 lg:justify-end\">\n          <Link href=\"/contact\" className=\"btn-primary\">\n            Get Started\n          </Link>\n        </div>\n      </nav>\n      \n      {/* Mobile menu */}\n      <AnimatePresence>\n        {mobileMenuOpen && (\n          <motion.div\n            initial={{ opacity: 0 }}\n            animate={{ opacity: 1 }}\n            exit={{ opacity: 0 }}\n            className=\"lg:hidden\"\n          >\n            <div className=\"fixed inset-0 z-50\" />\n            <motion.div\n              initial={{ x: '100%' }}\n              animate={{ x: 0 }}\n              exit={{ x: '100%' }}\n              transition={{ type: 'spring', damping: 25, stiffness: 200 }}\n              className=\"fixed inset-y-0 right-0 z-50 w-full overflow-y-auto bg-white px-6 py-6 sm:max-w-sm sm:ring-1 sm:ring-gray-900/10\"\n            >\n              <div className=\"flex items-center justify-between\">\n                <Link href=\"/\" className=\"-m-1.5 p-1.5\">\n                  <span className=\"sr-only\">Technoloway</span>\n                  <div className=\"flex items-center space-x-2\">\n                    <div className=\"h-8 w-8 bg-gradient-to-r from-blue-600 to-blue-400 rounded-lg flex items-center justify-center\">\n                      <span className=\"text-white font-bold text-sm\">T</span>\n                    </div>\n                    <span className=\"text-xl font-bold text-gray-900\">Technoloway</span>\n                  </div>\n                </Link>\n                <button\n                  type=\"button\"\n                  className=\"-m-2.5 rounded-md p-2.5 text-gray-700\"\n                  onClick={() => setMobileMenuOpen(false)}\n                >\n                  <span className=\"sr-only\">Close menu</span>\n                  <XMarkIcon className=\"h-6 w-6\" aria-hidden=\"true\" />\n                </button>\n              </div>\n              <div className=\"mt-6 flow-root\">\n                <div className=\"-my-6 divide-y divide-gray-500/10\">\n                  <div className=\"space-y-2 py-6\">\n                    {navigation.map((item) => (\n                      <Link\n                        key={item.name}\n                        href={item.href}\n                        className={`-mx-3 block rounded-lg px-3 py-2 text-base font-semibold leading-7 hover:bg-gray-50 ${\n                          isActive(item.href) ? 'text-blue-600' : 'text-gray-900'\n                        }`}\n                        onClick={() => setMobileMenuOpen(false)}\n                      >\n                        {item.name}\n                      </Link>\n                    ))}\n                  </div>\n                  <div className=\"py-6\">\n                    <Link\n                      href=\"/contact\"\n                      className=\"-mx-3 block rounded-lg px-3 py-2.5 text-base font-semibold leading-7 text-blue-600 hover:bg-gray-50\"\n                      onClick={() => setMobileMenuOpen(false)}\n                    >\n                      Get Started\n                    </Link>\n                  </div>\n                </div>\n              </div>\n            </motion.div>\n          </motion.div>\n        )}\n      </AnimatePresence>\n    </header>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AACA;AAAA;AANA;;;;;;;AAQA,MAAM,aAAa;IACjB;QAAE,MAAM;QAAQ,MAAM;IAAI;IAC1B;QAAE,MAAM;QAAS,MAAM;IAAS;IAChC;QAAE,MAAM;QAAY,MAAM;IAAY;IACtC;QAAE,MAAM;QAAY,MAAM;IAAY;IACtC;QAAE,MAAM;QAAgB,MAAM;IAAgB;IAC9C;QAAE,MAAM;QAAW,MAAM;IAAW;CACrC;AAEM,SAAS;IACd,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,WAAW,CAAA,GAAA,kIAAA,CAAA,cAAW,AAAD;IAE3B,MAAM,WAAW,CAAC;QAChB,IAAI,SAAS,KAAK;YAChB,OAAO,aAAa;QACtB;QACA,OAAO,SAAS,UAAU,CAAC;IAC7B;IAEA,qBACE,8OAAC;QAAO,WAAU;;0BAChB,8OAAC;gBAAI,WAAU;gBAAmD,cAAW;;kCAC3E,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC,4JAAA,CAAA,UAAI;4BAAC,MAAK;4BAAI,WAAU;;8CACvB,8OAAC;oCAAK,WAAU;8CAAU;;;;;;8CAC1B,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAK,WAAU;0DAA+B;;;;;;;;;;;sDAEjD,8OAAC;4CAAK,WAAU;sDAAkC;;;;;;;;;;;;;;;;;;;;;;;kCAKxD,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BACC,MAAK;4BACL,WAAU;4BACV,SAAS,IAAM,kBAAkB;;8CAEjC,8OAAC;oCAAK,WAAU;8CAAU;;;;;;8CAC1B,8OAAC,iNAAA,CAAA,YAAS;oCAAC,WAAU;oCAAU,eAAY;;;;;;;;;;;;;;;;;kCAI/C,8OAAC;wBAAI,WAAU;kCACZ,WAAW,GAAG,CAAC,CAAC,qBACf,8OAAC,4JAAA,CAAA,UAAI;gCAEH,MAAM,KAAK,IAAI;gCACf,WAAW,CAAC,sEAAsE,EAChF,SAAS,KAAK,IAAI,IAAI,kBAAkB,iBACxC;0CAED,KAAK,IAAI;+BANL,KAAK,IAAI;;;;;;;;;;kCAWpB,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC,4JAAA,CAAA,UAAI;4BAAC,MAAK;4BAAW,WAAU;sCAAc;;;;;;;;;;;;;;;;;0BAOlD,8OAAC,yLAAA,CAAA,kBAAe;0BACb,gCACC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;oBAAE;oBACtB,SAAS;wBAAE,SAAS;oBAAE;oBACtB,MAAM;wBAAE,SAAS;oBAAE;oBACnB,WAAU;;sCAEV,8OAAC;4BAAI,WAAU;;;;;;sCACf,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,SAAS;gCAAE,GAAG;4BAAO;4BACrB,SAAS;gCAAE,GAAG;4BAAE;4BAChB,MAAM;gCAAE,GAAG;4BAAO;4BAClB,YAAY;gCAAE,MAAM;gCAAU,SAAS;gCAAI,WAAW;4BAAI;4BAC1D,WAAU;;8CAEV,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,4JAAA,CAAA,UAAI;4CAAC,MAAK;4CAAI,WAAU;;8DACvB,8OAAC;oDAAK,WAAU;8DAAU;;;;;;8DAC1B,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;sEACb,cAAA,8OAAC;gEAAK,WAAU;0EAA+B;;;;;;;;;;;sEAEjD,8OAAC;4DAAK,WAAU;sEAAkC;;;;;;;;;;;;;;;;;;sDAGtD,8OAAC;4CACC,MAAK;4CACL,WAAU;4CACV,SAAS,IAAM,kBAAkB;;8DAEjC,8OAAC;oDAAK,WAAU;8DAAU;;;;;;8DAC1B,8OAAC,iNAAA,CAAA,YAAS;oDAAC,WAAU;oDAAU,eAAY;;;;;;;;;;;;;;;;;;8CAG/C,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;0DACZ,WAAW,GAAG,CAAC,CAAC,qBACf,8OAAC,4JAAA,CAAA,UAAI;wDAEH,MAAM,KAAK,IAAI;wDACf,WAAW,CAAC,oFAAoF,EAC9F,SAAS,KAAK,IAAI,IAAI,kBAAkB,iBACxC;wDACF,SAAS,IAAM,kBAAkB;kEAEhC,KAAK,IAAI;uDAPL,KAAK,IAAI;;;;;;;;;;0DAWpB,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC,4JAAA,CAAA,UAAI;oDACH,MAAK;oDACL,WAAU;oDACV,SAAS,IAAM,kBAAkB;8DAClC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAYrB", "debugId": null}}, {"offset": {"line": 431, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Project%20By%20AI/Technoloway/Technoloway%20%28Processing%29/technoloway-simple/src/components/footer.tsx"], "sourcesContent": ["import Link from 'next/link';\n\nexport function Footer() {\n  return (\n    <footer className=\"bg-gray-900 text-white\">\n      <div className=\"container py-16\">\n        <div className=\"grid grid-cols-1 md:grid-cols-4 gap-8\">\n          <div className=\"col-span-1 md:col-span-2\">\n            <div className=\"flex items-center space-x-2 mb-6\">\n              <div className=\"h-8 w-8 bg-gradient-to-r from-blue-600 to-blue-400 rounded-lg flex items-center justify-center\">\n                <span className=\"text-white font-bold text-sm\">T</span>\n              </div>\n              <span className=\"text-xl font-bold\">Technoloway</span>\n            </div>\n            <p className=\"text-gray-300 mb-6 max-w-md\">\n              Transforming ideas into digital reality. We build exceptional software \n              solutions that drive business growth and innovation.\n            </p>\n            <div className=\"space-y-2\">\n              <p className=\"text-gray-300\"><EMAIL></p>\n              <p className=\"text-gray-300\">+****************</p>\n              <p className=\"text-gray-300\">San Francisco, CA</p>\n            </div>\n          </div>\n          \n          <div>\n            <h3 className=\"text-lg font-semibold mb-4\">Services</h3>\n            <ul className=\"space-y-2 text-gray-300\">\n              <li><Link href=\"/#services\" className=\"hover:text-white transition-colors\">Web Development</Link></li>\n              <li><Link href=\"/#services\" className=\"hover:text-white transition-colors\">Mobile Apps</Link></li>\n              <li><Link href=\"/#services\" className=\"hover:text-white transition-colors\">Cloud Solutions</Link></li>\n              <li><Link href=\"/#services\" className=\"hover:text-white transition-colors\">API Development</Link></li>\n            </ul>\n          </div>\n          \n          <div>\n            <h3 className=\"text-lg font-semibold mb-4\">Company</h3>\n            <ul className=\"space-y-2 text-gray-300\">\n              <li><Link href=\"/team\" className=\"hover:text-white transition-colors\">About</Link></li>\n              <li><Link href=\"/team\" className=\"hover:text-white transition-colors\">Team</Link></li>\n              <li><Link href=\"/portfolio\" className=\"hover:text-white transition-colors\">Portfolio</Link></li>\n              <li><Link href=\"/blog\" className=\"hover:text-white transition-colors\">Blog</Link></li>\n              <li><Link href=\"/#contact\" className=\"hover:text-white transition-colors\">Contact</Link></li>\n            </ul>\n          </div>\n        </div>\n        \n        <div className=\"border-t border-gray-700 mt-12 pt-8 text-center\">\n          <p className=\"text-gray-400\">\n            &copy; {new Date().getFullYear()} Technoloway. All rights reserved.\n          </p>\n        </div>\n      </div>\n    </footer>\n  );\n}\n"], "names": [], "mappings": ";;;;AAAA;;;AAEO,SAAS;IACd,qBACE,8OAAC;QAAO,WAAU;kBAChB,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAK,WAAU;0DAA+B;;;;;;;;;;;sDAEjD,8OAAC;4CAAK,WAAU;sDAAoB;;;;;;;;;;;;8CAEtC,8OAAC;oCAAE,WAAU;8CAA8B;;;;;;8CAI3C,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAE,WAAU;sDAAgB;;;;;;sDAC7B,8OAAC;4CAAE,WAAU;sDAAgB;;;;;;sDAC7B,8OAAC;4CAAE,WAAU;sDAAgB;;;;;;;;;;;;;;;;;;sCAIjC,8OAAC;;8CACC,8OAAC;oCAAG,WAAU;8CAA6B;;;;;;8CAC3C,8OAAC;oCAAG,WAAU;;sDACZ,8OAAC;sDAAG,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAa,WAAU;0DAAqC;;;;;;;;;;;sDAC3E,8OAAC;sDAAG,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAa,WAAU;0DAAqC;;;;;;;;;;;sDAC3E,8OAAC;sDAAG,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAa,WAAU;0DAAqC;;;;;;;;;;;sDAC3E,8OAAC;sDAAG,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAa,WAAU;0DAAqC;;;;;;;;;;;;;;;;;;;;;;;sCAI/E,8OAAC;;8CACC,8OAAC;oCAAG,WAAU;8CAA6B;;;;;;8CAC3C,8OAAC;oCAAG,WAAU;;sDACZ,8OAAC;sDAAG,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAQ,WAAU;0DAAqC;;;;;;;;;;;sDACtE,8OAAC;sDAAG,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAQ,WAAU;0DAAqC;;;;;;;;;;;sDACtE,8OAAC;sDAAG,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAa,WAAU;0DAAqC;;;;;;;;;;;sDAC3E,8OAAC;sDAAG,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAQ,WAAU;0DAAqC;;;;;;;;;;;sDACtE,8OAAC;sDAAG,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAY,WAAU;0DAAqC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAKhF,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAE,WAAU;;4BAAgB;4BACnB,IAAI,OAAO,WAAW;4BAAG;;;;;;;;;;;;;;;;;;;;;;;AAM7C", "debugId": null}}, {"offset": {"line": 757, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Project%20By%20AI/Technoloway/Technoloway%20%28Processing%29/technoloway-simple/src/app/about/page.tsx"], "sourcesContent": ["'use client';\n\nimport { motion } from 'framer-motion';\nimport {\n  UserGroupIcon,\n  LightBulbIcon,\n  ShieldCheckIcon,\n  HeartIcon,\n  TrophyIcon,\n  GlobeAltIcon,\n  RocketLaunchIcon,\n  SparklesIcon,\n} from '@heroicons/react/24/outline';\nimport { Header } from '@/components/header';\nimport { Footer } from '@/components/footer';\n\nconst values = [\n  {\n    name: 'Innovation',\n    description: 'We stay ahead of technology trends and embrace cutting-edge solutions to deliver exceptional results.',\n    icon: LightBulbIcon,\n  },\n  {\n    name: 'Quality',\n    description: 'We deliver excellence in every project, ensuring robust, scalable, and maintainable solutions.',\n    icon: ShieldCheckIcon,\n  },\n  {\n    name: 'Partnership',\n    description: 'We work as an extension of your team, fostering collaboration and transparent communication.',\n    icon: HeartIcon,\n  },\n  {\n    name: 'Growth',\n    description: 'We are committed to continuous learning and helping our clients achieve sustainable growth.',\n    icon: RocketLaunchIcon,\n  },\n];\n\nconst stats = [\n  { name: 'Years of Experience', value: '10+' },\n  { name: 'Projects Completed', value: '500+' },\n  { name: '<PERSON> Clients', value: '200+' },\n  { name: 'Team Members', value: '50+' },\n  { name: 'Countries Served', value: '25+' },\n  { name: 'Technologies Mastered', value: '100+' },\n];\n\nconst milestones = [\n  {\n    year: '2014',\n    title: 'Company Founded',\n    description: 'Started as a small team with a big vision to democratize technology.',\n  },\n  {\n    year: '2016',\n    title: 'First Major Client',\n    description: 'Secured our first enterprise client and delivered a game-changing solution.',\n  },\n  {\n    year: '2018',\n    title: 'Team Expansion',\n    description: 'Grew to 25+ team members and opened our second office.',\n  },\n  {\n    year: '2020',\n    title: 'Global Reach',\n    description: 'Expanded internationally and started serving clients across 5 continents.',\n  },\n  {\n    year: '2022',\n    title: 'Innovation Hub',\n    description: 'Launched our R&D division focusing on AI and emerging technologies.',\n  },\n  {\n    year: '2024',\n    title: 'Industry Leader',\n    description: 'Recognized as a leading software development company with 500+ successful projects.',\n  },\n];\n\nexport default function AboutPage() {\n  return (\n    <div className=\"min-h-screen bg-white\">\n      <Header />\n\n      <main className=\"pt-20\">\n        {/* Hero Section */}\n        <section className=\"relative py-24 bg-gradient-to-br from-gray-50 via-white to-gray-100\">\n          <div className=\"container\">\n            <motion.div\n              initial={{ opacity: 0, y: 20 }}\n              animate={{ opacity: 1, y: 0 }}\n              transition={{ duration: 0.6 }}\n              className=\"text-center max-w-4xl mx-auto\"\n            >\n              <motion.div\n                initial={{ opacity: 0, scale: 0.95 }}\n                animate={{ opacity: 1, scale: 1 }}\n                transition={{ duration: 0.5, delay: 0.1 }}\n                className=\"mb-8\"\n              >\n                <span className=\"inline-flex items-center rounded-full bg-blue-50 px-6 py-2 text-sm font-medium text-blue-700 ring-1 ring-inset ring-blue-700/10\">\n                  <SparklesIcon className=\"w-4 h-4 mr-2\" />\n                  Our Story\n                </span>\n              </motion.div>\n\n              <motion.h1\n                initial={{ opacity: 0, y: 20 }}\n                animate={{ opacity: 1, y: 0 }}\n                transition={{ duration: 0.6, delay: 0.2 }}\n                className=\"text-4xl font-bold tracking-tight text-gray-900 sm:text-6xl lg:text-7xl\"\n              >\n                Building the Future of{' '}\n                <span className=\"gradient-text\">Software</span>\n              </motion.h1>\n\n              <motion.p\n                initial={{ opacity: 0, y: 20 }}\n                animate={{ opacity: 1, y: 0 }}\n                transition={{ duration: 0.6, delay: 0.3 }}\n                className=\"mt-6 text-lg leading-8 text-gray-600 sm:text-xl lg:text-2xl\"\n              >\n                Founded in 2014, Technoloway has been at the forefront of software innovation,\n                helping businesses transform their ideas into digital reality. We specialize in\n                creating scalable, modern solutions that drive growth and efficiency.\n              </motion.p>\n            </motion.div>\n          </div>\n        </section>\n\n        {/* Stats Section */}\n        <section className=\"py-16 bg-white\">\n          <div className=\"container\">\n            <div className=\"grid grid-cols-2 gap-8 md:grid-cols-3 lg:grid-cols-6\">\n              {stats.map((stat, index) => (\n                <motion.div\n                  key={stat.name}\n                  initial={{ opacity: 0, scale: 0.8 }}\n                  whileInView={{ opacity: 1, scale: 1 }}\n                  transition={{ duration: 0.5, delay: index * 0.1 }}\n                  viewport={{ once: true }}\n                  className=\"text-center\"\n                >\n                  <div className=\"text-3xl font-bold text-blue-600 sm:text-4xl\">\n                    {stat.value}\n                  </div>\n                  <div className=\"mt-2 text-sm text-gray-600\">\n                    {stat.name}\n                  </div>\n                </motion.div>\n              ))}\n            </div>\n          </div>\n        </section>\n\n        {/* Mission Section */}\n        <section className=\"py-24 bg-gray-50\">\n          <div className=\"container\">\n            <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-16 items-center\">\n              <motion.div\n                initial={{ opacity: 0, x: -20 }}\n                whileInView={{ opacity: 1, x: 0 }}\n                transition={{ duration: 0.6 }}\n                viewport={{ once: true }}\n              >\n                <h2 className=\"text-3xl font-bold tracking-tight text-gray-900 sm:text-4xl\">\n                  Our <span className=\"gradient-text\">Mission</span>\n                </h2>\n                <p className=\"mt-6 text-lg text-gray-600 leading-8\">\n                  To democratize cutting-edge technology and make it accessible to businesses of all sizes.\n                  We believe that every company deserves world-class software solutions that can compete\n                  with industry giants.\n                </p>\n                <p className=\"mt-4 text-lg text-gray-600 leading-8\">\n                  Our mission is to bridge the gap between complex technology and business needs,\n                  delivering solutions that are not only powerful but also intuitive and scalable.\n                </p>\n              </motion.div>\n\n              <motion.div\n                initial={{ opacity: 0, x: 20 }}\n                whileInView={{ opacity: 1, x: 0 }}\n                transition={{ duration: 0.6 }}\n                viewport={{ once: true }}\n                className=\"relative\"\n              >\n                <div className=\"aspect-square bg-gradient-to-br from-blue-100 to-purple-100 rounded-2xl p-8 flex items-center justify-center\">\n                  <div className=\"text-center\">\n                    <GlobeAltIcon className=\"w-24 h-24 text-blue-600 mx-auto mb-4\" />\n                    <h3 className=\"text-xl font-semibold text-gray-900 mb-2\">Global Impact</h3>\n                    <p className=\"text-gray-600\">\n                      Serving clients across 25+ countries with innovative solutions\n                    </p>\n                  </div>\n                </div>\n              </motion.div>\n            </div>\n          </div>\n        </section>\n\n        {/* Values Section */}\n        <section className=\"py-24 bg-white\">\n          <div className=\"container\">\n            <motion.div\n              initial={{ opacity: 0, y: 20 }}\n              whileInView={{ opacity: 1, y: 0 }}\n              transition={{ duration: 0.6 }}\n              viewport={{ once: true }}\n              className=\"text-center mb-16\"\n            >\n              <h2 className=\"text-3xl font-bold tracking-tight text-gray-900 sm:text-4xl\">\n                Our <span className=\"gradient-text\">Values</span>\n              </h2>\n              <p className=\"mt-4 text-lg text-gray-600 max-w-3xl mx-auto\">\n                These core values guide everything we do and shape how we work with our clients and each other.\n              </p>\n            </motion.div>\n\n            <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8\">\n              {values.map((value, index) => (\n                <motion.div\n                  key={value.name}\n                  initial={{ opacity: 0, y: 20 }}\n                  whileInView={{ opacity: 1, y: 0 }}\n                  transition={{ duration: 0.6, delay: index * 0.1 }}\n                  viewport={{ once: true }}\n                  className=\"text-center group\"\n                >\n                  <div className=\"flex items-center justify-center w-16 h-16 bg-blue-100 rounded-2xl mx-auto mb-6 group-hover:bg-blue-200 transition-colors\">\n                    <value.icon className=\"w-8 h-8 text-blue-600\" />\n                  </div>\n                  <h3 className=\"text-xl font-semibold text-gray-900 mb-3\">\n                    {value.name}\n                  </h3>\n                  <p className=\"text-gray-600 leading-relaxed\">\n                    {value.description}\n                  </p>\n                </motion.div>\n              ))}\n            </div>\n          </div>\n        </section>\n\n        {/* Timeline Section */}\n        <section className=\"py-24 bg-gray-50\">\n          <div className=\"container\">\n            <motion.div\n              initial={{ opacity: 0, y: 20 }}\n              whileInView={{ opacity: 1, y: 0 }}\n              transition={{ duration: 0.6 }}\n              viewport={{ once: true }}\n              className=\"text-center mb-16\"\n            >\n              <h2 className=\"text-3xl font-bold tracking-tight text-gray-900 sm:text-4xl\">\n                Our <span className=\"gradient-text\">Journey</span>\n              </h2>\n              <p className=\"mt-4 text-lg text-gray-600 max-w-3xl mx-auto\">\n                From a small startup to an industry leader, here are the key milestones that shaped our company.\n              </p>\n            </motion.div>\n\n            <div className=\"relative\">\n              {/* Timeline line */}\n              <div className=\"absolute left-1/2 transform -translate-x-px h-full w-0.5 bg-gray-300\" />\n\n              <div className=\"space-y-12\">\n                {milestones.map((milestone, index) => (\n                  <motion.div\n                    key={milestone.year}\n                    initial={{ opacity: 0, y: 20 }}\n                    whileInView={{ opacity: 1, y: 0 }}\n                    transition={{ duration: 0.6, delay: index * 0.1 }}\n                    viewport={{ once: true }}\n                    className={`relative flex items-center ${\n                      index % 2 === 0 ? 'justify-start' : 'justify-end'\n                    }`}\n                  >\n                    {/* Timeline dot */}\n                    <div className=\"absolute left-1/2 transform -translate-x-1/2 w-4 h-4 bg-blue-600 rounded-full border-4 border-white shadow-lg\" />\n\n                    <div className={`w-5/12 ${index % 2 === 0 ? 'pr-8 text-right' : 'pl-8 text-left'}`}>\n                      <div className=\"bg-white p-6 rounded-lg shadow-lg border border-gray-200\">\n                        <div className=\"text-2xl font-bold text-blue-600 mb-2\">\n                          {milestone.year}\n                        </div>\n                        <h3 className=\"text-lg font-semibold text-gray-900 mb-2\">\n                          {milestone.title}\n                        </h3>\n                        <p className=\"text-gray-600\">\n                          {milestone.description}\n                        </p>\n                      </div>\n                    </div>\n                  </motion.div>\n                ))}\n              </div>\n            </div>\n          </div>\n        </section>\n\n        {/* CTA Section */}\n        <section className=\"py-24 bg-gradient-to-r from-blue-600 to-purple-600\">\n          <div className=\"container\">\n            <motion.div\n              initial={{ opacity: 0, y: 20 }}\n              whileInView={{ opacity: 1, y: 0 }}\n              transition={{ duration: 0.6 }}\n              viewport={{ once: true }}\n              className=\"text-center\"\n            >\n              <h2 className=\"text-3xl font-bold tracking-tight text-white sm:text-4xl\">\n                Ready to Start Your Journey?\n              </h2>\n              <p className=\"mt-4 text-lg text-blue-100 max-w-3xl mx-auto\">\n                Join hundreds of satisfied clients who have transformed their businesses with our solutions.\n                Let's build something amazing together.\n              </p>\n              <div className=\"mt-8 flex flex-col sm:flex-row gap-4 justify-center\">\n                <a\n                  href=\"/contact\"\n                  className=\"inline-flex items-center px-8 py-3 border border-transparent text-base font-medium rounded-md text-blue-600 bg-white hover:bg-gray-50 transition-colors\"\n                >\n                  Get Started Today\n                </a>\n                <a\n                  href=\"/portfolio\"\n                  className=\"inline-flex items-center px-8 py-3 border-2 border-white text-base font-medium rounded-md text-white hover:bg-white hover:text-blue-600 transition-colors\"\n                >\n                  View Our Work\n                </a>\n              </div>\n            </motion.div>\n          </div>\n        </section>\n      </main>\n\n      <Footer />\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAUA;AACA;AAdA;;;;;;AAgBA,MAAM,SAAS;IACb;QACE,MAAM;QACN,aAAa;QACb,MAAM,yNAAA,CAAA,gBAAa;IACrB;IACA;QACE,MAAM;QACN,aAAa;QACb,MAAM,6NAAA,CAAA,kBAAe;IACvB;IACA;QACE,MAAM;QACN,aAAa;QACb,MAAM,iNAAA,CAAA,YAAS;IACjB;IACA;QACE,MAAM;QACN,aAAa;QACb,MAAM,+NAAA,CAAA,mBAAgB;IACxB;CACD;AAED,MAAM,QAAQ;IACZ;QAAE,MAAM;QAAuB,OAAO;IAAM;IAC5C;QAAE,MAAM;QAAsB,OAAO;IAAO;IAC5C;QAAE,MAAM;QAAiB,OAAO;IAAO;IACvC;QAAE,MAAM;QAAgB,OAAO;IAAM;IACrC;QAAE,MAAM;QAAoB,OAAO;IAAM;IACzC;QAAE,MAAM;QAAyB,OAAO;IAAO;CAChD;AAED,MAAM,aAAa;IACjB;QACE,MAAM;QACN,OAAO;QACP,aAAa;IACf;IACA;QACE,MAAM;QACN,OAAO;QACP,aAAa;IACf;IACA;QACE,MAAM;QACN,OAAO;QACP,aAAa;IACf;IACA;QACE,MAAM;QACN,OAAO;QACP,aAAa;IACf;IACA;QACE,MAAM;QACN,OAAO;QACP,aAAa;IACf;IACA;QACE,MAAM;QACN,OAAO;QACP,aAAa;IACf;CACD;AAEc,SAAS;IACtB,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC,4HAAA,CAAA,SAAM;;;;;0BAEP,8OAAC;gBAAK,WAAU;;kCAEd,8OAAC;wBAAQ,WAAU;kCACjB,cAAA,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gCACT,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAG;gCAC7B,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAE;gCAC5B,YAAY;oCAAE,UAAU;gCAAI;gCAC5B,WAAU;;kDAEV,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wCACT,SAAS;4CAAE,SAAS;4CAAG,OAAO;wCAAK;wCACnC,SAAS;4CAAE,SAAS;4CAAG,OAAO;wCAAE;wCAChC,YAAY;4CAAE,UAAU;4CAAK,OAAO;wCAAI;wCACxC,WAAU;kDAEV,cAAA,8OAAC;4CAAK,WAAU;;8DACd,8OAAC,uNAAA,CAAA,eAAY;oDAAC,WAAU;;;;;;gDAAiB;;;;;;;;;;;;kDAK7C,8OAAC,0LAAA,CAAA,SAAM,CAAC,EAAE;wCACR,SAAS;4CAAE,SAAS;4CAAG,GAAG;wCAAG;wCAC7B,SAAS;4CAAE,SAAS;4CAAG,GAAG;wCAAE;wCAC5B,YAAY;4CAAE,UAAU;4CAAK,OAAO;wCAAI;wCACxC,WAAU;;4CACX;4CACwB;0DACvB,8OAAC;gDAAK,WAAU;0DAAgB;;;;;;;;;;;;kDAGlC,8OAAC,0LAAA,CAAA,SAAM,CAAC,CAAC;wCACP,SAAS;4CAAE,SAAS;4CAAG,GAAG;wCAAG;wCAC7B,SAAS;4CAAE,SAAS;4CAAG,GAAG;wCAAE;wCAC5B,YAAY;4CAAE,UAAU;4CAAK,OAAO;wCAAI;wCACxC,WAAU;kDACX;;;;;;;;;;;;;;;;;;;;;;kCAUP,8OAAC;wBAAQ,WAAU;kCACjB,cAAA,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;0CACZ,MAAM,GAAG,CAAC,CAAC,MAAM,sBAChB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wCAET,SAAS;4CAAE,SAAS;4CAAG,OAAO;wCAAI;wCAClC,aAAa;4CAAE,SAAS;4CAAG,OAAO;wCAAE;wCACpC,YAAY;4CAAE,UAAU;4CAAK,OAAO,QAAQ;wCAAI;wCAChD,UAAU;4CAAE,MAAM;wCAAK;wCACvB,WAAU;;0DAEV,8OAAC;gDAAI,WAAU;0DACZ,KAAK,KAAK;;;;;;0DAEb,8OAAC;gDAAI,WAAU;0DACZ,KAAK,IAAI;;;;;;;uCAXP,KAAK,IAAI;;;;;;;;;;;;;;;;;;;;kCAoBxB,8OAAC;wBAAQ,WAAU;kCACjB,cAAA,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wCACT,SAAS;4CAAE,SAAS;4CAAG,GAAG,CAAC;wCAAG;wCAC9B,aAAa;4CAAE,SAAS;4CAAG,GAAG;wCAAE;wCAChC,YAAY;4CAAE,UAAU;wCAAI;wCAC5B,UAAU;4CAAE,MAAM;wCAAK;;0DAEvB,8OAAC;gDAAG,WAAU;;oDAA8D;kEACtE,8OAAC;wDAAK,WAAU;kEAAgB;;;;;;;;;;;;0DAEtC,8OAAC;gDAAE,WAAU;0DAAuC;;;;;;0DAKpD,8OAAC;gDAAE,WAAU;0DAAuC;;;;;;;;;;;;kDAMtD,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wCACT,SAAS;4CAAE,SAAS;4CAAG,GAAG;wCAAG;wCAC7B,aAAa;4CAAE,SAAS;4CAAG,GAAG;wCAAE;wCAChC,YAAY;4CAAE,UAAU;wCAAI;wCAC5B,UAAU;4CAAE,MAAM;wCAAK;wCACvB,WAAU;kDAEV,cAAA,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,uNAAA,CAAA,eAAY;wDAAC,WAAU;;;;;;kEACxB,8OAAC;wDAAG,WAAU;kEAA2C;;;;;;kEACzD,8OAAC;wDAAE,WAAU;kEAAgB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAWzC,8OAAC;wBAAQ,WAAU;kCACjB,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oCACT,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAG;oCAC7B,aAAa;wCAAE,SAAS;wCAAG,GAAG;oCAAE;oCAChC,YAAY;wCAAE,UAAU;oCAAI;oCAC5B,UAAU;wCAAE,MAAM;oCAAK;oCACvB,WAAU;;sDAEV,8OAAC;4CAAG,WAAU;;gDAA8D;8DACtE,8OAAC;oDAAK,WAAU;8DAAgB;;;;;;;;;;;;sDAEtC,8OAAC;4CAAE,WAAU;sDAA+C;;;;;;;;;;;;8CAK9D,8OAAC;oCAAI,WAAU;8CACZ,OAAO,GAAG,CAAC,CAAC,OAAO,sBAClB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4CAET,SAAS;gDAAE,SAAS;gDAAG,GAAG;4CAAG;4CAC7B,aAAa;gDAAE,SAAS;gDAAG,GAAG;4CAAE;4CAChC,YAAY;gDAAE,UAAU;gDAAK,OAAO,QAAQ;4CAAI;4CAChD,UAAU;gDAAE,MAAM;4CAAK;4CACvB,WAAU;;8DAEV,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC,MAAM,IAAI;wDAAC,WAAU;;;;;;;;;;;8DAExB,8OAAC;oDAAG,WAAU;8DACX,MAAM,IAAI;;;;;;8DAEb,8OAAC;oDAAE,WAAU;8DACV,MAAM,WAAW;;;;;;;2CAdf,MAAM,IAAI;;;;;;;;;;;;;;;;;;;;;kCAuBzB,8OAAC;wBAAQ,WAAU;kCACjB,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oCACT,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAG;oCAC7B,aAAa;wCAAE,SAAS;wCAAG,GAAG;oCAAE;oCAChC,YAAY;wCAAE,UAAU;oCAAI;oCAC5B,UAAU;wCAAE,MAAM;oCAAK;oCACvB,WAAU;;sDAEV,8OAAC;4CAAG,WAAU;;gDAA8D;8DACtE,8OAAC;oDAAK,WAAU;8DAAgB;;;;;;;;;;;;sDAEtC,8OAAC;4CAAE,WAAU;sDAA+C;;;;;;;;;;;;8CAK9D,8OAAC;oCAAI,WAAU;;sDAEb,8OAAC;4CAAI,WAAU;;;;;;sDAEf,8OAAC;4CAAI,WAAU;sDACZ,WAAW,GAAG,CAAC,CAAC,WAAW,sBAC1B,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oDAET,SAAS;wDAAE,SAAS;wDAAG,GAAG;oDAAG;oDAC7B,aAAa;wDAAE,SAAS;wDAAG,GAAG;oDAAE;oDAChC,YAAY;wDAAE,UAAU;wDAAK,OAAO,QAAQ;oDAAI;oDAChD,UAAU;wDAAE,MAAM;oDAAK;oDACvB,WAAW,CAAC,2BAA2B,EACrC,QAAQ,MAAM,IAAI,kBAAkB,eACpC;;sEAGF,8OAAC;4DAAI,WAAU;;;;;;sEAEf,8OAAC;4DAAI,WAAW,CAAC,OAAO,EAAE,QAAQ,MAAM,IAAI,oBAAoB,kBAAkB;sEAChF,cAAA,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEAAI,WAAU;kFACZ,UAAU,IAAI;;;;;;kFAEjB,8OAAC;wEAAG,WAAU;kFACX,UAAU,KAAK;;;;;;kFAElB,8OAAC;wEAAE,WAAU;kFACV,UAAU,WAAW;;;;;;;;;;;;;;;;;;mDArBvB,UAAU,IAAI;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAiC/B,8OAAC;wBAAQ,WAAU;kCACjB,cAAA,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gCACT,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAG;gCAC7B,aAAa;oCAAE,SAAS;oCAAG,GAAG;gCAAE;gCAChC,YAAY;oCAAE,UAAU;gCAAI;gCAC5B,UAAU;oCAAE,MAAM;gCAAK;gCACvB,WAAU;;kDAEV,8OAAC;wCAAG,WAAU;kDAA2D;;;;;;kDAGzE,8OAAC;wCAAE,WAAU;kDAA+C;;;;;;kDAI5D,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDACC,MAAK;gDACL,WAAU;0DACX;;;;;;0DAGD,8OAAC;gDACC,MAAK;gDACL,WAAU;0DACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BASX,8OAAC,4HAAA,CAAA,SAAM;;;;;;;;;;;AAGb", "debugId": null}}]}