import { createTRPCRouter } from './trpc';
import { postsRouter } from './routers/posts';
import { projectsRouter } from './routers/projects';
import { contactRouter } from './routers/contact';
import { servicesRouter } from './routers/services';
import { testimonialsRouter } from './routers/testimonials';
import { usersRouter } from './routers/users';

export const appRouter = createTRPCRouter({
  posts: postsRouter,
  projects: projectsRouter,
  contact: contactRouter,
  services: servicesRouter,
  testimonials: testimonialsRouter,
  users: usersRouter,
});

export type AppRouter = typeof appRouter;
