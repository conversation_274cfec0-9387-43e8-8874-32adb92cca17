'use client';

import { useState } from 'react';
import { motion } from 'framer-motion';
import {
  ChatBubbleLeftRightIcon,
  CogIcon,
  ChartBarIcon,
  UserIcon,
  ClockIcon,
  CheckCircleIcon,
  ExclamationTriangleIcon,
  PlusIcon,
  PencilIcon,
  TrashIcon,
} from '@heroicons/react/24/outline';

// Mock data - in real app, this would come from API
const chatbotStats = {
  totalConversations: 1247,
  activeUsers: 89,
  avgResponseTime: 2.3,
  satisfactionRate: 94.2,
  resolvedQueries: 1156,
  escalatedQueries: 91,
};

const recentConversations = [
  {
    id: 1,
    userId: 'user_123',
    userName: '<PERSON>',
    startTime: '2024-01-22T14:30:00Z',
    endTime: '2024-01-22T14:45:00Z',
    messageCount: 8,
    status: 'Resolved',
    topic: 'Pricing Information',
    satisfaction: 5,
    escalated: false,
  },
  {
    id: 2,
    userId: 'user_456',
    userName: '<PERSON>',
    startTime: '2024-01-22T13:15:00Z',
    endTime: '2024-01-22T13:35:00Z',
    messageCount: 12,
    status: 'Escalated',
    topic: 'Technical Support',
    satisfaction: null,
    escalated: true,
  },
  {
    id: 3,
    userId: 'user_789',
    userName: 'Mike Chen',
    startTime: '2024-01-22T12:00:00Z',
    endTime: '2024-01-22T12:10:00Z',
    messageCount: 5,
    status: 'Resolved',
    topic: 'Service Inquiry',
    satisfaction: 4,
    escalated: false,
  },
];

const knowledgeBase = [
  {
    id: 1,
    question: 'What services do you offer?',
    answer: 'We offer web development, mobile app development, cloud solutions, and digital consulting services.',
    category: 'Services',
    usage: 156,
    lastUpdated: '2024-01-20T10:00:00Z',
    isActive: true,
  },
  {
    id: 2,
    question: 'How much does a website cost?',
    answer: 'Website costs vary based on complexity. Basic websites start at $5,000, while complex applications can range from $15,000 to $50,000+.',
    category: 'Pricing',
    usage: 203,
    lastUpdated: '2024-01-18T14:30:00Z',
    isActive: true,
  },
  {
    id: 3,
    question: 'What is your development process?',
    answer: 'Our process includes discovery, planning, design, development, testing, and deployment phases with regular client communication.',
    category: 'Process',
    usage: 89,
    lastUpdated: '2024-01-15T09:15:00Z',
    isActive: true,
  },
  {
    id: 4,
    question: 'Do you provide ongoing support?',
    answer: 'Yes, we offer various support packages including maintenance, updates, and technical support for all our projects.',
    category: 'Support',
    usage: 134,
    lastUpdated: '2024-01-12T16:45:00Z',
    isActive: true,
  },
];

const categories = ['All', 'Services', 'Pricing', 'Process', 'Support', 'Technical'];

export default function ChatbotPage() {
  const [activeTab, setActiveTab] = useState('overview');
  const [selectedCategory, setSelectedCategory] = useState('All');
  const [selectedConversation, setSelectedConversation] = useState<any>(null);
  const [showAddKnowledge, setShowAddKnowledge] = useState(false);

  const filteredKnowledge = knowledgeBase.filter(item => 
    selectedCategory === 'All' || item.category === selectedCategory
  );

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  const formatDuration = (start: string, end: string) => {
    const duration = new Date(end).getTime() - new Date(start).getTime();
    const minutes = Math.floor(duration / 60000);
    return `${minutes} min`;
  };

  const renderStars = (rating: number) => {
    return [...Array(5)].map((_, i) => (
      <span key={i} className={`text-sm ${i < rating ? 'text-yellow-400' : 'text-gray-300'}`}>
        ★
      </span>
    ));
  };

  return (
    <div className="py-6">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 md:px-8">
        {/* Header */}
        <div className="mb-8">
          <h2 className="text-2xl font-bold leading-7 text-gray-900 sm:text-3xl sm:truncate">
            Chatbot Management
          </h2>
          <p className="mt-1 text-sm text-gray-500">
            Monitor chatbot performance, manage conversations, and update knowledge base
          </p>
        </div>

        {/* Tabs */}
        <div className="mb-6">
          <nav className="flex space-x-8">
            {[
              { id: 'overview', name: 'Overview', icon: ChartBarIcon },
              { id: 'conversations', name: 'Conversations', icon: ChatBubbleLeftRightIcon },
              { id: 'knowledge', name: 'Knowledge Base', icon: CogIcon },
            ].map((tab) => {
              const Icon = tab.icon;
              return (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id)}
                  className={`flex items-center space-x-2 py-2 px-1 border-b-2 font-medium text-sm ${
                    activeTab === tab.id
                      ? 'border-blue-500 text-blue-600'
                      : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                  }`}
                >
                  <Icon className="h-5 w-5" />
                  <span>{tab.name}</span>
                </button>
              );
            })}
          </nav>
        </div>

        {/* Overview Tab */}
        {activeTab === 'overview' && (
          <div className="space-y-6">
            {/* Stats Grid */}
            <div className="grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-3">
              <div className="bg-white overflow-hidden shadow rounded-lg">
                <div className="p-5">
                  <div className="flex items-center">
                    <div className="flex-shrink-0">
                      <div className="w-8 h-8 bg-blue-500 rounded-md flex items-center justify-center">
                        <ChatBubbleLeftRightIcon className="h-5 w-5 text-white" />
                      </div>
                    </div>
                    <div className="ml-5 w-0 flex-1">
                      <dl>
                        <dt className="text-sm font-medium text-gray-500 truncate">Total Conversations</dt>
                        <dd className="text-lg font-medium text-gray-900">{chatbotStats.totalConversations}</dd>
                      </dl>
                    </div>
                  </div>
                </div>
              </div>

              <div className="bg-white overflow-hidden shadow rounded-lg">
                <div className="p-5">
                  <div className="flex items-center">
                    <div className="flex-shrink-0">
                      <div className="w-8 h-8 bg-green-500 rounded-md flex items-center justify-center">
                        <UserIcon className="h-5 w-5 text-white" />
                      </div>
                    </div>
                    <div className="ml-5 w-0 flex-1">
                      <dl>
                        <dt className="text-sm font-medium text-gray-500 truncate">Active Users</dt>
                        <dd className="text-lg font-medium text-gray-900">{chatbotStats.activeUsers}</dd>
                      </dl>
                    </div>
                  </div>
                </div>
              </div>

              <div className="bg-white overflow-hidden shadow rounded-lg">
                <div className="p-5">
                  <div className="flex items-center">
                    <div className="flex-shrink-0">
                      <div className="w-8 h-8 bg-yellow-500 rounded-md flex items-center justify-center">
                        <ClockIcon className="h-5 w-5 text-white" />
                      </div>
                    </div>
                    <div className="ml-5 w-0 flex-1">
                      <dl>
                        <dt className="text-sm font-medium text-gray-500 truncate">Avg Response Time</dt>
                        <dd className="text-lg font-medium text-gray-900">{chatbotStats.avgResponseTime}s</dd>
                      </dl>
                    </div>
                  </div>
                </div>
              </div>

              <div className="bg-white overflow-hidden shadow rounded-lg">
                <div className="p-5">
                  <div className="flex items-center">
                    <div className="flex-shrink-0">
                      <div className="w-8 h-8 bg-purple-500 rounded-md flex items-center justify-center">
                        <CheckCircleIcon className="h-5 w-5 text-white" />
                      </div>
                    </div>
                    <div className="ml-5 w-0 flex-1">
                      <dl>
                        <dt className="text-sm font-medium text-gray-500 truncate">Satisfaction Rate</dt>
                        <dd className="text-lg font-medium text-gray-900">{chatbotStats.satisfactionRate}%</dd>
                      </dl>
                    </div>
                  </div>
                </div>
              </div>

              <div className="bg-white overflow-hidden shadow rounded-lg">
                <div className="p-5">
                  <div className="flex items-center">
                    <div className="flex-shrink-0">
                      <div className="w-8 h-8 bg-green-600 rounded-md flex items-center justify-center">
                        <span className="text-white text-sm font-bold">{chatbotStats.resolvedQueries}</span>
                      </div>
                    </div>
                    <div className="ml-5 w-0 flex-1">
                      <dl>
                        <dt className="text-sm font-medium text-gray-500 truncate">Resolved Queries</dt>
                        <dd className="text-lg font-medium text-gray-900">Auto-resolved</dd>
                      </dl>
                    </div>
                  </div>
                </div>
              </div>

              <div className="bg-white overflow-hidden shadow rounded-lg">
                <div className="p-5">
                  <div className="flex items-center">
                    <div className="flex-shrink-0">
                      <div className="w-8 h-8 bg-red-500 rounded-md flex items-center justify-center">
                        <span className="text-white text-sm font-bold">{chatbotStats.escalatedQueries}</span>
                      </div>
                    </div>
                    <div className="ml-5 w-0 flex-1">
                      <dl>
                        <dt className="text-sm font-medium text-gray-500 truncate">Escalated</dt>
                        <dd className="text-lg font-medium text-gray-900">To Human</dd>
                      </dl>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Recent Activity */}
            <div className="bg-white shadow rounded-lg">
              <div className="px-4 py-5 sm:p-6">
                <h3 className="text-lg leading-6 font-medium text-gray-900 mb-4">
                  Recent Activity
                </h3>
                <div className="space-y-3">
                  <div className="flex items-center space-x-3 text-sm">
                    <div className="w-2 h-2 bg-green-400 rounded-full"></div>
                    <span className="text-gray-600">Resolved 15 queries in the last hour</span>
                    <span className="text-gray-400">2 minutes ago</span>
                  </div>
                  <div className="flex items-center space-x-3 text-sm">
                    <div className="w-2 h-2 bg-blue-400 rounded-full"></div>
                    <span className="text-gray-600">Knowledge base updated with 3 new entries</span>
                    <span className="text-gray-400">1 hour ago</span>
                  </div>
                  <div className="flex items-center space-x-3 text-sm">
                    <div className="w-2 h-2 bg-yellow-400 rounded-full"></div>
                    <span className="text-gray-600">2 conversations escalated to human agents</span>
                    <span className="text-gray-400">3 hours ago</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Conversations Tab */}
        {activeTab === 'conversations' && (
          <div className="bg-white shadow rounded-lg">
            <div className="px-4 py-5 sm:p-6">
              <h3 className="text-lg leading-6 font-medium text-gray-900 mb-4">
                Recent Conversations
              </h3>
              
              <div className="overflow-hidden">
                <ul className="divide-y divide-gray-200">
                  {recentConversations.map((conversation, index) => (
                    <motion.li
                      key={conversation.id}
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ duration: 0.3, delay: index * 0.1 }}
                      className="py-4 cursor-pointer hover:bg-gray-50"
                      onClick={() => setSelectedConversation(conversation)}
                    >
                      <div className="flex items-center justify-between">
                        <div className="flex items-start space-x-4">
                          <div className="flex-shrink-0">
                            <div className={`w-10 h-10 rounded-full flex items-center justify-center ${
                              conversation.status === 'Resolved' ? 'bg-green-100' : 'bg-yellow-100'
                            }`}>
                              {conversation.status === 'Resolved' ? (
                                <CheckCircleIcon className="w-6 h-6 text-green-600" />
                              ) : (
                                <ExclamationTriangleIcon className="w-6 h-6 text-yellow-600" />
                              )}
                            </div>
                          </div>
                          
                          <div className="flex-1 min-w-0">
                            <div className="flex items-center space-x-2 mb-1">
                              <h4 className="text-sm font-medium text-gray-900">{conversation.userName}</h4>
                              <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                                conversation.status === 'Resolved' 
                                  ? 'bg-green-100 text-green-800'
                                  : 'bg-yellow-100 text-yellow-800'
                              }`}>
                                {conversation.status}
                              </span>
                              {conversation.escalated && (
                                <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                                  Escalated
                                </span>
                              )}
                            </div>
                            
                            <div className="flex items-center space-x-4 text-sm text-gray-500 mb-1">
                              <span>Topic: {conversation.topic}</span>
                              <span>{conversation.messageCount} messages</span>
                              <span>Duration: {formatDuration(conversation.startTime, conversation.endTime)}</span>
                            </div>
                            
                            <div className="flex items-center space-x-4 text-sm text-gray-500">
                              <span>Started: {formatDate(conversation.startTime)}</span>
                              {conversation.satisfaction && (
                                <div className="flex items-center space-x-1">
                                  <span>Rating:</span>
                                  {renderStars(conversation.satisfaction)}
                                </div>
                              )}
                            </div>
                          </div>
                        </div>
                      </div>
                    </motion.li>
                  ))}
                </ul>
              </div>
            </div>
          </div>
        )}

        {/* Knowledge Base Tab */}
        {activeTab === 'knowledge' && (
          <div className="space-y-6">
            {/* Header with Add Button */}
            <div className="flex justify-between items-center">
              <div>
                <h3 className="text-lg leading-6 font-medium text-gray-900">
                  Knowledge Base
                </h3>
                <p className="mt-1 text-sm text-gray-500">
                  Manage chatbot responses and frequently asked questions
                </p>
              </div>
              <button
                onClick={() => setShowAddKnowledge(true)}
                className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
              >
                <PlusIcon className="-ml-1 mr-2 h-5 w-5" />
                Add Entry
              </button>
            </div>

            {/* Category Filter */}
            <div className="bg-white shadow rounded-lg">
              <div className="px-4 py-5 sm:p-6">
                <div className="flex flex-wrap gap-2">
                  {categories.map(category => (
                    <button
                      key={category}
                      onClick={() => setSelectedCategory(category)}
                      className={`px-4 py-2 rounded-full text-sm font-medium transition-colors ${
                        selectedCategory === category
                          ? 'bg-blue-600 text-white'
                          : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                      }`}
                    >
                      {category}
                    </button>
                  ))}
                </div>
              </div>
            </div>

            {/* Knowledge Base Entries */}
            <div className="bg-white shadow rounded-lg">
              <div className="px-4 py-5 sm:p-6">
                <div className="space-y-4">
                  {filteredKnowledge.map((entry, index) => (
                    <motion.div
                      key={entry.id}
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ duration: 0.3, delay: index * 0.1 }}
                      className="border border-gray-200 rounded-lg p-4"
                    >
                      <div className="flex items-start justify-between">
                        <div className="flex-1">
                          <div className="flex items-center space-x-2 mb-2">
                            <h4 className="text-sm font-medium text-gray-900">{entry.question}</h4>
                            <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                              {entry.category}
                            </span>
                          </div>
                          
                          <p className="text-sm text-gray-600 mb-3">{entry.answer}</p>
                          
                          <div className="flex items-center space-x-4 text-sm text-gray-500">
                            <span>Used {entry.usage} times</span>
                            <span>Updated: {formatDate(entry.lastUpdated)}</span>
                            <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${
                              entry.isActive ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'
                            }`}>
                              {entry.isActive ? 'Active' : 'Inactive'}
                            </span>
                          </div>
                        </div>
                        
                        <div className="flex items-center space-x-2 ml-4">
                          <button className="text-gray-400 hover:text-blue-600 transition-colors">
                            <PencilIcon className="h-5 w-5" />
                          </button>
                          <button className="text-gray-400 hover:text-red-600 transition-colors">
                            <TrashIcon className="h-5 w-5" />
                          </button>
                        </div>
                      </div>
                    </motion.div>
                  ))}
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Conversation Detail Modal */}
        {selectedConversation && (
          <div className="fixed inset-0 z-50 overflow-y-auto">
            <div className="flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
              <div className="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" onClick={() => setSelectedConversation(null)} />
              
              <div className="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-2xl sm:w-full">
                <div className="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
                  <div className="sm:flex sm:items-start">
                    <div className="mt-3 text-center sm:mt-0 sm:text-left w-full">
                      <h3 className="text-lg leading-6 font-medium text-gray-900 mb-4">
                        Conversation Details
                      </h3>
                      
                      <div className="space-y-4">
                        <div className="grid grid-cols-2 gap-4">
                          <div>
                            <label className="block text-sm font-medium text-gray-700">User</label>
                            <p className="mt-1 text-sm text-gray-900">{selectedConversation.userName}</p>
                          </div>
                          <div>
                            <label className="block text-sm font-medium text-gray-700">Topic</label>
                            <p className="mt-1 text-sm text-gray-900">{selectedConversation.topic}</p>
                          </div>
                          <div>
                            <label className="block text-sm font-medium text-gray-700">Status</label>
                            <span className={`mt-1 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                              selectedConversation.status === 'Resolved' 
                                ? 'bg-green-100 text-green-800'
                                : 'bg-yellow-100 text-yellow-800'
                            }`}>
                              {selectedConversation.status}
                            </span>
                          </div>
                          <div>
                            <label className="block text-sm font-medium text-gray-700">Duration</label>
                            <p className="mt-1 text-sm text-gray-900">
                              {formatDuration(selectedConversation.startTime, selectedConversation.endTime)}
                            </p>
                          </div>
                        </div>
                        
                        <div>
                          <label className="block text-sm font-medium text-gray-700">Messages</label>
                          <p className="mt-1 text-sm text-gray-900">{selectedConversation.messageCount} messages exchanged</p>
                        </div>
                        
                        {selectedConversation.satisfaction && (
                          <div>
                            <label className="block text-sm font-medium text-gray-700">User Satisfaction</label>
                            <div className="mt-1 flex items-center space-x-1">
                              {renderStars(selectedConversation.satisfaction)}
                              <span className="ml-2 text-sm text-gray-600">
                                ({selectedConversation.satisfaction}/5)
                              </span>
                            </div>
                          </div>
                        )}
                        
                        <div>
                          <label className="block text-sm font-medium text-gray-700">Timeline</label>
                          <div className="mt-1 text-sm text-gray-900">
                            <p>Started: {formatDate(selectedConversation.startTime)}</p>
                            <p>Ended: {formatDate(selectedConversation.endTime)}</p>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
                <div className="bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
                  <button
                    type="button"
                    className="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-blue-600 text-base font-medium text-white hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 sm:ml-3 sm:w-auto sm:text-sm"
                  >
                    View Full Transcript
                  </button>
                  <button
                    type="button"
                    className="mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 sm:mt-0 sm:w-auto sm:text-sm"
                    onClick={() => setSelectedConversation(null)}
                  >
                    Close
                  </button>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
