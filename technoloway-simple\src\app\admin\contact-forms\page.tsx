'use client';

import { useState } from 'react';
import { motion } from 'framer-motion';
import {
  EnvelopeIcon,
  PhoneIcon,
  UserIcon,
  CalendarIcon,
  EyeIcon,
  TrashIcon,
  MagnifyingGlassIcon,
  CheckCircleIcon,
  ExclamationCircleIcon,
  ClockIcon,
} from '@heroicons/react/24/outline';

// Mock data - in real app, this would come from API
const contactForms = [
  {
    id: 1,
    name: '<PERSON>',
    email: '<EMAIL>',
    phone: '+****************',
    company: 'Tech Innovations Inc.',
    subject: 'Web Development Project Inquiry',
    message: 'Hi, I\'m interested in developing a modern e-commerce platform for our business. We need a scalable solution that can handle high traffic and integrate with our existing systems.',
    status: 'Unread',
    priority: 'High',
    submittedAt: '2024-01-22T10:30:00Z',
    source: 'Website Contact Form',
    budget: '$50,000 - $100,000',
    timeline: '3-6 months',
  },
  {
    id: 2,
    name: '<PERSON>',
    email: '<EMAIL>',
    phone: '+****************',
    company: 'StartupCo',
    subject: 'Mobile App Development',
    message: 'We\'re a healthcare startup looking to develop a mobile app for patient monitoring. The app needs to be HIPAA compliant and work on both iOS and Android.',
    status: 'Read',
    priority: 'Medium',
    submittedAt: '2024-01-21T14:15:00Z',
    source: 'Website Contact Form',
    budget: '$25,000 - $50,000',
    timeline: '2-4 months',
  },
  {
    id: 3,
    name: '<PERSON>',
    email: '<EMAIL>',
    phone: '+****************',
    company: 'Enterprise Solutions Ltd.',
    subject: 'Enterprise Software Consultation',
    message: 'Our company is looking to modernize our legacy systems. We need consultation on the best approach and technologies to use for our digital transformation.',
    status: 'Replied',
    priority: 'High',
    submittedAt: '2024-01-20T09:45:00Z',
    source: 'Website Contact Form',
    budget: '$100,000+',
    timeline: '6-12 months',
  },
  {
    id: 4,
    name: 'Emily Rodriguez',
    email: '<EMAIL>',
    phone: '+****************',
    company: 'Community Nonprofit',
    subject: 'Website Redesign',
    message: 'We\'re a nonprofit organization that needs a website redesign to better showcase our mission and make it easier for people to donate and volunteer.',
    status: 'Unread',
    priority: 'Low',
    submittedAt: '2024-01-19T16:20:00Z',
    source: 'Website Contact Form',
    budget: '$5,000 - $15,000',
    timeline: '1-2 months',
  },
];

const statuses = ['All', 'Unread', 'Read', 'Replied', 'Archived'];
const priorities = ['All', 'Low', 'Medium', 'High', 'Critical'];

const getStatusColor = (status: string) => {
  switch (status) {
    case 'Unread': return 'bg-red-100 text-red-800';
    case 'Read': return 'bg-yellow-100 text-yellow-800';
    case 'Replied': return 'bg-green-100 text-green-800';
    case 'Archived': return 'bg-gray-100 text-gray-800';
    default: return 'bg-gray-100 text-gray-800';
  }
};

const getPriorityColor = (priority: string) => {
  switch (priority) {
    case 'Critical': return 'bg-red-100 text-red-800';
    case 'High': return 'bg-orange-100 text-orange-800';
    case 'Medium': return 'bg-yellow-100 text-yellow-800';
    case 'Low': return 'bg-green-100 text-green-800';
    default: return 'bg-gray-100 text-gray-800';
  }
};

const getStatusIcon = (status: string) => {
  switch (status) {
    case 'Unread': return ExclamationCircleIcon;
    case 'Read': return ClockIcon;
    case 'Replied': return CheckCircleIcon;
    default: return ClockIcon;
  }
};

export default function ContactFormsPage() {
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedStatus, setSelectedStatus] = useState('All');
  const [selectedPriority, setSelectedPriority] = useState('All');
  const [selectedForm, setSelectedForm] = useState<any>(null);

  const filteredForms = contactForms.filter(form => {
    const matchesSearch = form.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         form.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         form.company.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         form.subject.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesStatus = selectedStatus === 'All' || form.status === selectedStatus;
    const matchesPriority = selectedPriority === 'All' || form.priority === selectedPriority;
    
    return matchesSearch && matchesStatus && matchesPriority;
  });

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  return (
    <div className="py-6">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 md:px-8">
        {/* Header */}
        <div className="mb-8">
          <h2 className="text-2xl font-bold leading-7 text-gray-900 sm:text-3xl sm:truncate">
            Contact Forms
          </h2>
          <p className="mt-1 text-sm text-gray-500">
            Manage and respond to customer inquiries and project requests
          </p>
        </div>

        {/* Stats Cards */}
        <div className="mb-6 grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4">
          <div className="bg-white overflow-hidden shadow rounded-lg">
            <div className="p-5">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <div className="w-8 h-8 bg-blue-500 rounded-md flex items-center justify-center">
                    <span className="text-white text-sm font-bold">{contactForms.length}</span>
                  </div>
                </div>
                <div className="ml-5 w-0 flex-1">
                  <dl>
                    <dt className="text-sm font-medium text-gray-500 truncate">Total Forms</dt>
                    <dd className="text-lg font-medium text-gray-900">All Time</dd>
                  </dl>
                </div>
              </div>
            </div>
          </div>

          <div className="bg-white overflow-hidden shadow rounded-lg">
            <div className="p-5">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <div className="w-8 h-8 bg-red-500 rounded-md flex items-center justify-center">
                    <span className="text-white text-sm font-bold">
                      {contactForms.filter(f => f.status === 'Unread').length}
                    </span>
                  </div>
                </div>
                <div className="ml-5 w-0 flex-1">
                  <dl>
                    <dt className="text-sm font-medium text-gray-500 truncate">Unread</dt>
                    <dd className="text-lg font-medium text-gray-900">Needs Attention</dd>
                  </dl>
                </div>
              </div>
            </div>
          </div>

          <div className="bg-white overflow-hidden shadow rounded-lg">
            <div className="p-5">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <div className="w-8 h-8 bg-green-500 rounded-md flex items-center justify-center">
                    <span className="text-white text-sm font-bold">
                      {contactForms.filter(f => f.status === 'Replied').length}
                    </span>
                  </div>
                </div>
                <div className="ml-5 w-0 flex-1">
                  <dl>
                    <dt className="text-sm font-medium text-gray-500 truncate">Replied</dt>
                    <dd className="text-lg font-medium text-gray-900">Responded</dd>
                  </dl>
                </div>
              </div>
            </div>
          </div>

          <div className="bg-white overflow-hidden shadow rounded-lg">
            <div className="p-5">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <div className="w-8 h-8 bg-orange-500 rounded-md flex items-center justify-center">
                    <span className="text-white text-sm font-bold">
                      {contactForms.filter(f => f.priority === 'High').length}
                    </span>
                  </div>
                </div>
                <div className="ml-5 w-0 flex-1">
                  <dl>
                    <dt className="text-sm font-medium text-gray-500 truncate">High Priority</dt>
                    <dd className="text-lg font-medium text-gray-900">Urgent</dd>
                  </dl>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Filters */}
        <div className="mb-6 bg-white shadow rounded-lg">
          <div className="px-4 py-5 sm:p-6">
            <div className="grid grid-cols-1 gap-4 sm:grid-cols-3">
              {/* Search */}
              <div className="relative">
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <MagnifyingGlassIcon className="h-5 w-5 text-gray-400" />
                </div>
                <input
                  type="text"
                  placeholder="Search forms..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                />
              </div>

              {/* Status Filter */}
              <div>
                <select
                  value={selectedStatus}
                  onChange={(e) => setSelectedStatus(e.target.value)}
                  className="block w-full pl-3 pr-10 py-2 text-base border border-gray-300 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm rounded-md"
                >
                  {statuses.map(status => (
                    <option key={status} value={status}>{status}</option>
                  ))}
                </select>
              </div>

              {/* Priority Filter */}
              <div>
                <select
                  value={selectedPriority}
                  onChange={(e) => setSelectedPriority(e.target.value)}
                  className="block w-full pl-3 pr-10 py-2 text-base border border-gray-300 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm rounded-md"
                >
                  {priorities.map(priority => (
                    <option key={priority} value={priority}>{priority}</option>
                  ))}
                </select>
              </div>
            </div>
          </div>
        </div>

        {/* Contact Forms List */}
        <div className="bg-white shadow overflow-hidden sm:rounded-md">
          <ul className="divide-y divide-gray-200">
            {filteredForms.map((form, index) => {
              const StatusIcon = getStatusIcon(form.status);
              return (
                <motion.li
                  key={form.id}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.3, delay: index * 0.1 }}
                  className="hover:bg-gray-50 cursor-pointer"
                  onClick={() => setSelectedForm(form)}
                >
                  <div className="px-4 py-4 sm:px-6">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-3">
                        <div className={`flex-shrink-0 w-8 h-8 rounded-full flex items-center justify-center ${getStatusColor(form.status)}`}>
                          <StatusIcon className="h-4 w-4" />
                        </div>
                        <div className="flex-1 min-w-0">
                          <div className="flex items-center space-x-2">
                            <p className="text-sm font-medium text-gray-900 truncate">
                              {form.name}
                            </p>
                            <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getPriorityColor(form.priority)}`}>
                              {form.priority}
                            </span>
                          </div>
                          <p className="text-sm text-gray-500">{form.company}</p>
                        </div>
                      </div>
                      <div className="flex items-center space-x-4">
                        <div className="text-right">
                          <p className="text-sm font-medium text-gray-900">{form.subject}</p>
                          <p className="text-sm text-gray-500">{formatDate(form.submittedAt)}</p>
                        </div>
                        <div className="flex items-center space-x-2">
                          <button className="text-gray-400 hover:text-blue-600 transition-colors">
                            <EyeIcon className="h-5 w-5" />
                          </button>
                          <button className="text-gray-400 hover:text-red-600 transition-colors">
                            <TrashIcon className="h-5 w-5" />
                          </button>
                        </div>
                      </div>
                    </div>
                    
                    <div className="mt-2">
                      <div className="flex items-center space-x-4 text-sm text-gray-500">
                        <div className="flex items-center">
                          <EnvelopeIcon className="h-4 w-4 mr-1" />
                          {form.email}
                        </div>
                        <div className="flex items-center">
                          <PhoneIcon className="h-4 w-4 mr-1" />
                          {form.phone}
                        </div>
                        <div className="flex items-center">
                          <span className="font-medium">Budget:</span>
                          <span className="ml-1">{form.budget}</span>
                        </div>
                        <div className="flex items-center">
                          <span className="font-medium">Timeline:</span>
                          <span className="ml-1">{form.timeline}</span>
                        </div>
                      </div>
                      <p className="mt-2 text-sm text-gray-600 line-clamp-2">
                        {form.message}
                      </p>
                    </div>
                  </div>
                </motion.li>
              );
            })}
          </ul>
        </div>

        {/* Empty State */}
        {filteredForms.length === 0 && (
          <div className="bg-white shadow rounded-lg">
            <div className="text-center py-12">
              <EnvelopeIcon className="mx-auto h-12 w-12 text-gray-400" />
              <h3 className="mt-2 text-sm font-medium text-gray-900">No contact forms found</h3>
              <p className="mt-1 text-sm text-gray-500">
                Try adjusting your search or filter criteria.
              </p>
            </div>
          </div>
        )}

        {/* Form Detail Modal */}
        {selectedForm && (
          <div className="fixed inset-0 z-50 overflow-y-auto">
            <div className="flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
              <div className="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" onClick={() => setSelectedForm(null)} />
              
              <div className="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full">
                <div className="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
                  <div className="sm:flex sm:items-start">
                    <div className="mt-3 text-center sm:mt-0 sm:text-left w-full">
                      <h3 className="text-lg leading-6 font-medium text-gray-900 mb-4">
                        Contact Form Details
                      </h3>
                      
                      <div className="space-y-4">
                        <div>
                          <label className="block text-sm font-medium text-gray-700">Name</label>
                          <p className="mt-1 text-sm text-gray-900">{selectedForm.name}</p>
                        </div>
                        
                        <div>
                          <label className="block text-sm font-medium text-gray-700">Company</label>
                          <p className="mt-1 text-sm text-gray-900">{selectedForm.company}</p>
                        </div>
                        
                        <div>
                          <label className="block text-sm font-medium text-gray-700">Email</label>
                          <p className="mt-1 text-sm text-gray-900">{selectedForm.email}</p>
                        </div>
                        
                        <div>
                          <label className="block text-sm font-medium text-gray-700">Phone</label>
                          <p className="mt-1 text-sm text-gray-900">{selectedForm.phone}</p>
                        </div>
                        
                        <div>
                          <label className="block text-sm font-medium text-gray-700">Subject</label>
                          <p className="mt-1 text-sm text-gray-900">{selectedForm.subject}</p>
                        </div>
                        
                        <div>
                          <label className="block text-sm font-medium text-gray-700">Message</label>
                          <p className="mt-1 text-sm text-gray-900">{selectedForm.message}</p>
                        </div>
                        
                        <div className="grid grid-cols-2 gap-4">
                          <div>
                            <label className="block text-sm font-medium text-gray-700">Budget</label>
                            <p className="mt-1 text-sm text-gray-900">{selectedForm.budget}</p>
                          </div>
                          <div>
                            <label className="block text-sm font-medium text-gray-700">Timeline</label>
                            <p className="mt-1 text-sm text-gray-900">{selectedForm.timeline}</p>
                          </div>
                        </div>
                        
                        <div>
                          <label className="block text-sm font-medium text-gray-700">Submitted</label>
                          <p className="mt-1 text-sm text-gray-900">{formatDate(selectedForm.submittedAt)}</p>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
                <div className="bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
                  <button
                    type="button"
                    className="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-blue-600 text-base font-medium text-white hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 sm:ml-3 sm:w-auto sm:text-sm"
                  >
                    Reply
                  </button>
                  <button
                    type="button"
                    className="mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm"
                    onClick={() => setSelectedForm(null)}
                  >
                    Close
                  </button>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
