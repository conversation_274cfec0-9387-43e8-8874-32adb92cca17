{"version": 3, "sources": [], "sections": [{"offset": {"line": 39, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Project%20By%20AI/Technoloway/Technoloway%20%28Processing%29/technoloway-simple/src/components/header.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport Link from 'next/link';\nimport { usePathname } from 'next/navigation';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport { Bars3Icon, XMarkIcon } from '@heroicons/react/24/outline';\n\nconst navigation = [\n  { name: 'Home', href: '/' },\n  { name: 'Services', href: '/#services' },\n  { name: 'Portfolio', href: '/portfolio' },\n  { name: 'Team', href: '/team' },\n  { name: 'Blog', href: '/blog' },\n  { name: 'Contact', href: '/#contact' },\n];\n\nexport function Header() {\n  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);\n  const pathname = usePathname();\n\n  const isActive = (href: string) => {\n    if (href === '/') {\n      return pathname === '/';\n    }\n    return pathname.startsWith(href);\n  };\n\n  return (\n    <header className=\"fixed inset-x-0 top-0 z-50 bg-white/80 backdrop-blur-md border-b border-gray-200\">\n      <nav className=\"container flex items-center justify-between py-4\" aria-label=\"Global\">\n        <div className=\"flex lg:flex-1\">\n          <Link href=\"/\" className=\"-m-1.5 p-1.5\">\n            <span className=\"sr-only\">Technoloway</span>\n            <div className=\"flex items-center space-x-2\">\n              <div className=\"h-8 w-8 bg-gradient-to-r from-blue-600 to-blue-400 rounded-lg flex items-center justify-center\">\n                <span className=\"text-white font-bold text-sm\">T</span>\n              </div>\n              <span className=\"text-xl font-bold text-gray-900\">Technoloway</span>\n            </div>\n          </Link>\n        </div>\n        \n        <div className=\"flex lg:hidden\">\n          <button\n            type=\"button\"\n            className=\"-m-2.5 inline-flex items-center justify-center rounded-md p-2.5 text-gray-700\"\n            onClick={() => setMobileMenuOpen(true)}\n          >\n            <span className=\"sr-only\">Open main menu</span>\n            <Bars3Icon className=\"h-6 w-6\" aria-hidden=\"true\" />\n          </button>\n        </div>\n        \n        <div className=\"hidden lg:flex lg:gap-x-8\">\n          {navigation.map((item) => (\n            <Link\n              key={item.name}\n              href={item.href}\n              className={`text-sm font-semibold leading-6 transition-colors hover:text-blue-600 ${\n                isActive(item.href) ? 'text-blue-600' : 'text-gray-900'\n              }`}\n            >\n              {item.name}\n            </Link>\n          ))}\n        </div>\n        \n        <div className=\"hidden lg:flex lg:flex-1 lg:justify-end\">\n          <Link href=\"/#contact\" className=\"btn-primary\">\n            Get Started\n          </Link>\n        </div>\n      </nav>\n      \n      {/* Mobile menu */}\n      <AnimatePresence>\n        {mobileMenuOpen && (\n          <motion.div\n            initial={{ opacity: 0 }}\n            animate={{ opacity: 1 }}\n            exit={{ opacity: 0 }}\n            className=\"lg:hidden\"\n          >\n            <div className=\"fixed inset-0 z-50\" />\n            <motion.div\n              initial={{ x: '100%' }}\n              animate={{ x: 0 }}\n              exit={{ x: '100%' }}\n              transition={{ type: 'spring', damping: 25, stiffness: 200 }}\n              className=\"fixed inset-y-0 right-0 z-50 w-full overflow-y-auto bg-white px-6 py-6 sm:max-w-sm sm:ring-1 sm:ring-gray-900/10\"\n            >\n              <div className=\"flex items-center justify-between\">\n                <Link href=\"/\" className=\"-m-1.5 p-1.5\">\n                  <span className=\"sr-only\">Technoloway</span>\n                  <div className=\"flex items-center space-x-2\">\n                    <div className=\"h-8 w-8 bg-gradient-to-r from-blue-600 to-blue-400 rounded-lg flex items-center justify-center\">\n                      <span className=\"text-white font-bold text-sm\">T</span>\n                    </div>\n                    <span className=\"text-xl font-bold text-gray-900\">Technoloway</span>\n                  </div>\n                </Link>\n                <button\n                  type=\"button\"\n                  className=\"-m-2.5 rounded-md p-2.5 text-gray-700\"\n                  onClick={() => setMobileMenuOpen(false)}\n                >\n                  <span className=\"sr-only\">Close menu</span>\n                  <XMarkIcon className=\"h-6 w-6\" aria-hidden=\"true\" />\n                </button>\n              </div>\n              <div className=\"mt-6 flow-root\">\n                <div className=\"-my-6 divide-y divide-gray-500/10\">\n                  <div className=\"space-y-2 py-6\">\n                    {navigation.map((item) => (\n                      <Link\n                        key={item.name}\n                        href={item.href}\n                        className={`-mx-3 block rounded-lg px-3 py-2 text-base font-semibold leading-7 hover:bg-gray-50 ${\n                          isActive(item.href) ? 'text-blue-600' : 'text-gray-900'\n                        }`}\n                        onClick={() => setMobileMenuOpen(false)}\n                      >\n                        {item.name}\n                      </Link>\n                    ))}\n                  </div>\n                  <div className=\"py-6\">\n                    <Link\n                      href=\"/#contact\"\n                      className=\"-mx-3 block rounded-lg px-3 py-2.5 text-base font-semibold leading-7 text-blue-600 hover:bg-gray-50\"\n                      onClick={() => setMobileMenuOpen(false)}\n                    >\n                      Get Started\n                    </Link>\n                  </div>\n                </div>\n              </div>\n            </motion.div>\n          </motion.div>\n        )}\n      </AnimatePresence>\n    </header>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AACA;AAAA;AANA;;;;;;;AAQA,MAAM,aAAa;IACjB;QAAE,MAAM;QAAQ,MAAM;IAAI;IAC1B;QAAE,MAAM;QAAY,MAAM;IAAa;IACvC;QAAE,MAAM;QAAa,MAAM;IAAa;IACxC;QAAE,MAAM;QAAQ,MAAM;IAAQ;IAC9B;QAAE,MAAM;QAAQ,MAAM;IAAQ;IAC9B;QAAE,MAAM;QAAW,MAAM;IAAY;CACtC;AAEM,SAAS;IACd,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,WAAW,CAAA,GAAA,kIAAA,CAAA,cAAW,AAAD;IAE3B,MAAM,WAAW,CAAC;QAChB,IAAI,SAAS,KAAK;YAChB,OAAO,aAAa;QACtB;QACA,OAAO,SAAS,UAAU,CAAC;IAC7B;IAEA,qBACE,8OAAC;QAAO,WAAU;;0BAChB,8OAAC;gBAAI,WAAU;gBAAmD,cAAW;;kCAC3E,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC,4JAAA,CAAA,UAAI;4BAAC,MAAK;4BAAI,WAAU;;8CACvB,8OAAC;oCAAK,WAAU;8CAAU;;;;;;8CAC1B,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAK,WAAU;0DAA+B;;;;;;;;;;;sDAEjD,8OAAC;4CAAK,WAAU;sDAAkC;;;;;;;;;;;;;;;;;;;;;;;kCAKxD,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BACC,MAAK;4BACL,WAAU;4BACV,SAAS,IAAM,kBAAkB;;8CAEjC,8OAAC;oCAAK,WAAU;8CAAU;;;;;;8CAC1B,8OAAC,iNAAA,CAAA,YAAS;oCAAC,WAAU;oCAAU,eAAY;;;;;;;;;;;;;;;;;kCAI/C,8OAAC;wBAAI,WAAU;kCACZ,WAAW,GAAG,CAAC,CAAC,qBACf,8OAAC,4JAAA,CAAA,UAAI;gCAEH,MAAM,KAAK,IAAI;gCACf,WAAW,CAAC,sEAAsE,EAChF,SAAS,KAAK,IAAI,IAAI,kBAAkB,iBACxC;0CAED,KAAK,IAAI;+BANL,KAAK,IAAI;;;;;;;;;;kCAWpB,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC,4JAAA,CAAA,UAAI;4BAAC,MAAK;4BAAY,WAAU;sCAAc;;;;;;;;;;;;;;;;;0BAOnD,8OAAC,yLAAA,CAAA,kBAAe;0BACb,gCACC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;oBAAE;oBACtB,SAAS;wBAAE,SAAS;oBAAE;oBACtB,MAAM;wBAAE,SAAS;oBAAE;oBACnB,WAAU;;sCAEV,8OAAC;4BAAI,WAAU;;;;;;sCACf,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,SAAS;gCAAE,GAAG;4BAAO;4BACrB,SAAS;gCAAE,GAAG;4BAAE;4BAChB,MAAM;gCAAE,GAAG;4BAAO;4BAClB,YAAY;gCAAE,MAAM;gCAAU,SAAS;gCAAI,WAAW;4BAAI;4BAC1D,WAAU;;8CAEV,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,4JAAA,CAAA,UAAI;4CAAC,MAAK;4CAAI,WAAU;;8DACvB,8OAAC;oDAAK,WAAU;8DAAU;;;;;;8DAC1B,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;sEACb,cAAA,8OAAC;gEAAK,WAAU;0EAA+B;;;;;;;;;;;sEAEjD,8OAAC;4DAAK,WAAU;sEAAkC;;;;;;;;;;;;;;;;;;sDAGtD,8OAAC;4CACC,MAAK;4CACL,WAAU;4CACV,SAAS,IAAM,kBAAkB;;8DAEjC,8OAAC;oDAAK,WAAU;8DAAU;;;;;;8DAC1B,8OAAC,iNAAA,CAAA,YAAS;oDAAC,WAAU;oDAAU,eAAY;;;;;;;;;;;;;;;;;;8CAG/C,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;0DACZ,WAAW,GAAG,CAAC,CAAC,qBACf,8OAAC,4JAAA,CAAA,UAAI;wDAEH,MAAM,KAAK,IAAI;wDACf,WAAW,CAAC,oFAAoF,EAC9F,SAAS,KAAK,IAAI,IAAI,kBAAkB,iBACxC;wDACF,SAAS,IAAM,kBAAkB;kEAEhC,KAAK,IAAI;uDAPL,KAAK,IAAI;;;;;;;;;;0DAWpB,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC,4JAAA,CAAA,UAAI;oDACH,MAAK;oDACL,WAAU;oDACV,SAAS,IAAM,kBAAkB;8DAClC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAYrB", "debugId": null}}, {"offset": {"line": 431, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Project%20By%20AI/Technoloway/Technoloway%20%28Processing%29/technoloway-simple/src/components/footer.tsx"], "sourcesContent": ["import Link from 'next/link';\n\nexport function Footer() {\n  return (\n    <footer className=\"bg-gray-900 text-white\">\n      <div className=\"container py-16\">\n        <div className=\"grid grid-cols-1 md:grid-cols-4 gap-8\">\n          <div className=\"col-span-1 md:col-span-2\">\n            <div className=\"flex items-center space-x-2 mb-6\">\n              <div className=\"h-8 w-8 bg-gradient-to-r from-blue-600 to-blue-400 rounded-lg flex items-center justify-center\">\n                <span className=\"text-white font-bold text-sm\">T</span>\n              </div>\n              <span className=\"text-xl font-bold\">Technoloway</span>\n            </div>\n            <p className=\"text-gray-300 mb-6 max-w-md\">\n              Transforming ideas into digital reality. We build exceptional software \n              solutions that drive business growth and innovation.\n            </p>\n            <div className=\"space-y-2\">\n              <p className=\"text-gray-300\"><EMAIL></p>\n              <p className=\"text-gray-300\">+****************</p>\n              <p className=\"text-gray-300\">San Francisco, CA</p>\n            </div>\n          </div>\n          \n          <div>\n            <h3 className=\"text-lg font-semibold mb-4\">Services</h3>\n            <ul className=\"space-y-2 text-gray-300\">\n              <li><Link href=\"/#services\" className=\"hover:text-white transition-colors\">Web Development</Link></li>\n              <li><Link href=\"/#services\" className=\"hover:text-white transition-colors\">Mobile Apps</Link></li>\n              <li><Link href=\"/#services\" className=\"hover:text-white transition-colors\">Cloud Solutions</Link></li>\n              <li><Link href=\"/#services\" className=\"hover:text-white transition-colors\">API Development</Link></li>\n            </ul>\n          </div>\n          \n          <div>\n            <h3 className=\"text-lg font-semibold mb-4\">Company</h3>\n            <ul className=\"space-y-2 text-gray-300\">\n              <li><Link href=\"/team\" className=\"hover:text-white transition-colors\">About</Link></li>\n              <li><Link href=\"/team\" className=\"hover:text-white transition-colors\">Team</Link></li>\n              <li><Link href=\"/portfolio\" className=\"hover:text-white transition-colors\">Portfolio</Link></li>\n              <li><Link href=\"/blog\" className=\"hover:text-white transition-colors\">Blog</Link></li>\n              <li><Link href=\"/#contact\" className=\"hover:text-white transition-colors\">Contact</Link></li>\n            </ul>\n          </div>\n        </div>\n        \n        <div className=\"border-t border-gray-700 mt-12 pt-8 text-center\">\n          <p className=\"text-gray-400\">\n            &copy; {new Date().getFullYear()} Technoloway. All rights reserved.\n          </p>\n        </div>\n      </div>\n    </footer>\n  );\n}\n"], "names": [], "mappings": ";;;;AAAA;;;AAEO,SAAS;IACd,qBACE,8OAAC;QAAO,WAAU;kBAChB,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAK,WAAU;0DAA+B;;;;;;;;;;;sDAEjD,8OAAC;4CAAK,WAAU;sDAAoB;;;;;;;;;;;;8CAEtC,8OAAC;oCAAE,WAAU;8CAA8B;;;;;;8CAI3C,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAE,WAAU;sDAAgB;;;;;;sDAC7B,8OAAC;4CAAE,WAAU;sDAAgB;;;;;;sDAC7B,8OAAC;4CAAE,WAAU;sDAAgB;;;;;;;;;;;;;;;;;;sCAIjC,8OAAC;;8CACC,8OAAC;oCAAG,WAAU;8CAA6B;;;;;;8CAC3C,8OAAC;oCAAG,WAAU;;sDACZ,8OAAC;sDAAG,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAa,WAAU;0DAAqC;;;;;;;;;;;sDAC3E,8OAAC;sDAAG,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAa,WAAU;0DAAqC;;;;;;;;;;;sDAC3E,8OAAC;sDAAG,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAa,WAAU;0DAAqC;;;;;;;;;;;sDAC3E,8OAAC;sDAAG,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAa,WAAU;0DAAqC;;;;;;;;;;;;;;;;;;;;;;;sCAI/E,8OAAC;;8CACC,8OAAC;oCAAG,WAAU;8CAA6B;;;;;;8CAC3C,8OAAC;oCAAG,WAAU;;sDACZ,8OAAC;sDAAG,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAQ,WAAU;0DAAqC;;;;;;;;;;;sDACtE,8OAAC;sDAAG,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAQ,WAAU;0DAAqC;;;;;;;;;;;sDACtE,8OAAC;sDAAG,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAa,WAAU;0DAAqC;;;;;;;;;;;sDAC3E,8OAAC;sDAAG,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAQ,WAAU;0DAAqC;;;;;;;;;;;sDACtE,8OAAC;sDAAG,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAY,WAAU;0DAAqC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAKhF,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAE,WAAU;;4BAAgB;4BACnB,IAAI,OAAO,WAAW;4BAAG;;;;;;;;;;;;;;;;;;;;;;;AAM7C", "debugId": null}}, {"offset": {"line": 757, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Project%20By%20AI/Technoloway/Technoloway%20%28Processing%29/technoloway-simple/src/app/blog/page.tsx"], "sourcesContent": ["'use client';\n\nimport { motion } from 'framer-motion';\nimport Link from 'next/link';\nimport Image from 'next/image';\nimport {\n  CalendarIcon,\n  ClockIcon,\n  UserIcon,\n  ArrowRightIcon,\n  TagIcon\n} from '@heroicons/react/24/outline';\nimport { Header } from '@/components/header';\nimport { Footer } from '@/components/footer';\n\nconst blogPosts = [\n  {\n    id: 1,\n    title: 'The Future of Web Development: Trends to Watch in 2024',\n    excerpt: 'Explore the latest trends shaping web development, from AI integration to progressive web apps and the rise of edge computing.',\n    content: 'Full article content would go here...',\n    author: '<PERSON>',\n    authorRole: 'Lead Developer',\n    publishedAt: '2024-01-15',\n    readTime: '8 min read',\n    category: 'Web Development',\n    tags: ['React', 'Next.js', 'AI', 'Trends'],\n    image: 'https://images.unsplash.com/photo-1461749280684-dccba630e2f6?w=800&h=400&fit=crop',\n    featured: true,\n  },\n  {\n    id: 2,\n    title: 'Building Scalable Mobile Apps with React Native',\n    excerpt: 'Learn best practices for creating performant and scalable mobile applications using React Native and modern development patterns.',\n    content: 'Full article content would go here...',\n    author: '<PERSON>',\n    authorRole: 'Mobile Developer',\n    publishedAt: '2024-01-10',\n    readTime: '6 min read',\n    category: 'Mobile Development',\n    tags: ['React Native', 'Mobile', 'Performance'],\n    image: 'https://images.unsplash.com/photo-1512941937669-90a1b58e7e9c?w=800&h=400&fit=crop',\n    featured: false,\n  },\n  {\n    id: 3,\n    title: 'Cloud Architecture Best Practices for Startups',\n    excerpt: 'Discover how to design cost-effective and scalable cloud infrastructure that grows with your startup.',\n    content: 'Full article content would go here...',\n    author: 'David Rodriguez',\n    authorRole: 'Cloud Architect',\n    publishedAt: '2024-01-05',\n    readTime: '10 min read',\n    category: 'Cloud Computing',\n    tags: ['AWS', 'Architecture', 'Startups'],\n    image: 'https://images.unsplash.com/photo-1451187580459-43490279c0fa?w=800&h=400&fit=crop',\n    featured: false,\n  },\n  {\n    id: 4,\n    title: 'TypeScript Tips for Better Code Quality',\n    excerpt: 'Advanced TypeScript techniques to improve code quality, maintainability, and developer experience.',\n    content: 'Full article content would go here...',\n    author: 'Emily Davis',\n    authorRole: 'Senior Developer',\n    publishedAt: '2024-01-01',\n    readTime: '7 min read',\n    category: 'Programming',\n    tags: ['TypeScript', 'Code Quality', 'Best Practices'],\n    image: 'https://images.unsplash.com/photo-1516116216624-53e697fedbea?w=800&h=400&fit=crop',\n    featured: false,\n  },\n  {\n    id: 5,\n    title: 'API Security: Protecting Your Digital Assets',\n    excerpt: 'Essential security practices for API development, including authentication, authorization, and data protection.',\n    content: 'Full article content would go here...',\n    author: 'Alex Thompson',\n    authorRole: 'Security Engineer',\n    publishedAt: '2023-12-28',\n    readTime: '9 min read',\n    category: 'Security',\n    tags: ['API', 'Security', 'Authentication'],\n    image: 'https://images.unsplash.com/photo-1563013544-824ae1b704d3?w=800&h=400&fit=crop',\n    featured: false,\n  },\n  {\n    id: 6,\n    title: 'The Art of Code Review: Building Better Teams',\n    excerpt: 'How effective code reviews can improve code quality, knowledge sharing, and team collaboration.',\n    content: 'Full article content would go here...',\n    author: 'Lisa Wang',\n    authorRole: 'Engineering Manager',\n    publishedAt: '2023-12-20',\n    readTime: '5 min read',\n    category: 'Team Management',\n    tags: ['Code Review', 'Team', 'Collaboration'],\n    image: 'https://images.unsplash.com/photo-1522071820081-009f0129c71c?w=800&h=400&fit=crop',\n    featured: false,\n  },\n];\n\nconst categories = ['All', 'Web Development', 'Mobile Development', 'Cloud Computing', 'Programming', 'Security', 'Team Management'];\n\nexport default function BlogPage() {\n  const featuredPost = blogPosts.find(post => post.featured);\n  const regularPosts = blogPosts.filter(post => !post.featured);\n\n  return (\n    <div className=\"min-h-screen bg-white\">\n      <Header />\n      {/* Header */}\n      <header className=\"bg-gray-50 py-20 pt-32\">\n        <div className=\"container\">\n          <motion.div\n            initial={{ opacity: 0, y: 20 }}\n            animate={{ opacity: 1, y: 0 }}\n            transition={{ duration: 0.6 }}\n            className=\"text-center\"\n          >\n            <h1 className=\"text-4xl font-bold tracking-tight text-gray-900 sm:text-5xl lg:text-6xl\">\n              Our <span className=\"gradient-text\">Blog</span>\n            </h1>\n            <p className=\"mt-6 text-xl text-gray-600 max-w-3xl mx-auto\">\n              Insights, tutorials, and thoughts on software development, technology trends, \n              and best practices from our team of experts.\n            </p>\n          </motion.div>\n        </div>\n      </header>\n\n      <main className=\"container py-16\">\n        {/* Featured Post */}\n        {featuredPost && (\n          <motion.section\n            initial={{ opacity: 0, y: 20 }}\n            animate={{ opacity: 1, y: 0 }}\n            transition={{ duration: 0.6, delay: 0.2 }}\n            className=\"mb-16\"\n          >\n            <h2 className=\"text-2xl font-bold text-gray-900 mb-8\">Featured Article</h2>\n            <div className=\"bg-white rounded-2xl shadow-lg overflow-hidden border border-gray-200\">\n              <div className=\"md:flex\">\n                <div className=\"md:w-1/2\">\n                  <div className=\"relative h-64 md:h-full\">\n                    <Image\n                      src={featuredPost.image}\n                      alt={featuredPost.title}\n                      fill\n                      className=\"object-cover\"\n                    />\n                  </div>\n                </div>\n                <div className=\"md:w-1/2 p-8\">\n                  <div className=\"flex items-center space-x-4 mb-4\">\n                    <span className=\"inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-blue-100 text-blue-800\">\n                      <TagIcon className=\"w-4 h-4 mr-1\" />\n                      {featuredPost.category}\n                    </span>\n                    <span className=\"text-sm text-gray-500\">Featured</span>\n                  </div>\n                  \n                  <h3 className=\"text-2xl font-bold text-gray-900 mb-4\">\n                    {featuredPost.title}\n                  </h3>\n                  \n                  <p className=\"text-gray-600 mb-6\">\n                    {featuredPost.excerpt}\n                  </p>\n                  \n                  <div className=\"flex items-center justify-between\">\n                    <div className=\"flex items-center space-x-4 text-sm text-gray-500\">\n                      <div className=\"flex items-center\">\n                        <UserIcon className=\"w-4 h-4 mr-1\" />\n                        {featuredPost.author}\n                      </div>\n                      <div className=\"flex items-center\">\n                        <CalendarIcon className=\"w-4 h-4 mr-1\" />\n                        {new Date(featuredPost.publishedAt).toLocaleDateString()}\n                      </div>\n                      <div className=\"flex items-center\">\n                        <ClockIcon className=\"w-4 h-4 mr-1\" />\n                        {featuredPost.readTime}\n                      </div>\n                    </div>\n                    \n                    <Link \n                      href={`/blog/${featuredPost.id}`}\n                      className=\"inline-flex items-center text-blue-600 hover:text-blue-700 font-medium\"\n                    >\n                      Read More\n                      <ArrowRightIcon className=\"w-4 h-4 ml-1\" />\n                    </Link>\n                  </div>\n                </div>\n              </div>\n            </div>\n          </motion.section>\n        )}\n\n        {/* Category Filter */}\n        <motion.section\n          initial={{ opacity: 0, y: 20 }}\n          animate={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.6, delay: 0.3 }}\n          className=\"mb-12\"\n        >\n          <div className=\"flex flex-wrap gap-2\">\n            {categories.map((category) => (\n              <button\n                key={category}\n                className=\"px-4 py-2 rounded-full text-sm font-medium border border-gray-300 hover:border-blue-500 hover:text-blue-600 transition-colors\"\n              >\n                {category}\n              </button>\n            ))}\n          </div>\n        </motion.section>\n\n        {/* Blog Posts Grid */}\n        <motion.section\n          initial={{ opacity: 0, y: 20 }}\n          animate={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.6, delay: 0.4 }}\n        >\n          <h2 className=\"text-2xl font-bold text-gray-900 mb-8\">Latest Articles</h2>\n          <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8\">\n            {regularPosts.map((post, index) => (\n              <motion.article\n                key={post.id}\n                initial={{ opacity: 0, y: 20 }}\n                animate={{ opacity: 1, y: 0 }}\n                transition={{ duration: 0.6, delay: 0.1 * index }}\n                className=\"bg-white rounded-xl shadow-md overflow-hidden border border-gray-200 hover:shadow-lg transition-shadow\"\n              >\n                <div className=\"relative h-48\">\n                  <Image\n                    src={post.image}\n                    alt={post.title}\n                    fill\n                    className=\"object-cover\"\n                  />\n                </div>\n                \n                <div className=\"p-6\">\n                  <div className=\"flex items-center space-x-2 mb-3\">\n                    <span className=\"inline-flex items-center px-2 py-1 rounded text-xs font-medium bg-gray-100 text-gray-800\">\n                      {post.category}\n                    </span>\n                  </div>\n                  \n                  <h3 className=\"text-lg font-semibold text-gray-900 mb-2 line-clamp-2\">\n                    {post.title}\n                  </h3>\n                  \n                  <p className=\"text-gray-600 text-sm mb-4 line-clamp-3\">\n                    {post.excerpt}\n                  </p>\n                  \n                  <div className=\"flex items-center justify-between text-xs text-gray-500\">\n                    <div className=\"flex items-center space-x-3\">\n                      <span>{post.author}</span>\n                      <span>{new Date(post.publishedAt).toLocaleDateString()}</span>\n                    </div>\n                    <span>{post.readTime}</span>\n                  </div>\n                  \n                  <div className=\"mt-4\">\n                    <Link \n                      href={`/blog/${post.id}`}\n                      className=\"inline-flex items-center text-blue-600 hover:text-blue-700 font-medium text-sm\"\n                    >\n                      Read Article\n                      <ArrowRightIcon className=\"w-3 h-3 ml-1\" />\n                    </Link>\n                  </div>\n                </div>\n              </motion.article>\n            ))}\n          </div>\n        </motion.section>\n\n        {/* Newsletter Signup */}\n        <motion.section\n          initial={{ opacity: 0, y: 20 }}\n          animate={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.6, delay: 0.6 }}\n          className=\"mt-20 bg-gradient-to-r from-blue-50 to-purple-50 rounded-2xl p-8 text-center\"\n        >\n          <h2 className=\"text-2xl font-bold text-gray-900 mb-4\">\n            Stay Updated\n          </h2>\n          <p className=\"text-gray-600 mb-6 max-w-2xl mx-auto\">\n            Subscribe to our newsletter to get the latest insights on software development, \n            technology trends, and best practices delivered to your inbox.\n          </p>\n          <div className=\"flex flex-col sm:flex-row gap-4 max-w-md mx-auto\">\n            <input\n              type=\"email\"\n              placeholder=\"Enter your email\"\n              className=\"flex-1 px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n            />\n            <button className=\"btn-primary whitespace-nowrap\">\n              Subscribe\n            </button>\n          </div>\n        </motion.section>\n      </main>\n      <Footer />\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAOA;AACA;AAbA;;;;;;;;AAeA,MAAM,YAAY;IAChB;QACE,IAAI;QACJ,OAAO;QACP,SAAS;QACT,SAAS;QACT,QAAQ;QACR,YAAY;QACZ,aAAa;QACb,UAAU;QACV,UAAU;QACV,MAAM;YAAC;YAAS;YAAW;YAAM;SAAS;QAC1C,OAAO;QACP,UAAU;IACZ;IACA;QACE,IAAI;QACJ,OAAO;QACP,SAAS;QACT,SAAS;QACT,QAAQ;QACR,YAAY;QACZ,aAAa;QACb,UAAU;QACV,UAAU;QACV,MAAM;YAAC;YAAgB;YAAU;SAAc;QAC/C,OAAO;QACP,UAAU;IACZ;IACA;QACE,IAAI;QACJ,OAAO;QACP,SAAS;QACT,SAAS;QACT,QAAQ;QACR,YAAY;QACZ,aAAa;QACb,UAAU;QACV,UAAU;QACV,MAAM;YAAC;YAAO;YAAgB;SAAW;QACzC,OAAO;QACP,UAAU;IACZ;IACA;QACE,IAAI;QACJ,OAAO;QACP,SAAS;QACT,SAAS;QACT,QAAQ;QACR,YAAY;QACZ,aAAa;QACb,UAAU;QACV,UAAU;QACV,MAAM;YAAC;YAAc;YAAgB;SAAiB;QACtD,OAAO;QACP,UAAU;IACZ;IACA;QACE,IAAI;QACJ,OAAO;QACP,SAAS;QACT,SAAS;QACT,QAAQ;QACR,YAAY;QACZ,aAAa;QACb,UAAU;QACV,UAAU;QACV,MAAM;YAAC;YAAO;YAAY;SAAiB;QAC3C,OAAO;QACP,UAAU;IACZ;IACA;QACE,IAAI;QACJ,OAAO;QACP,SAAS;QACT,SAAS;QACT,QAAQ;QACR,YAAY;QACZ,aAAa;QACb,UAAU;QACV,UAAU;QACV,MAAM;YAAC;YAAe;YAAQ;SAAgB;QAC9C,OAAO;QACP,UAAU;IACZ;CACD;AAED,MAAM,aAAa;IAAC;IAAO;IAAmB;IAAsB;IAAmB;IAAe;IAAY;CAAkB;AAErH,SAAS;IACtB,MAAM,eAAe,UAAU,IAAI,CAAC,CAAA,OAAQ,KAAK,QAAQ;IACzD,MAAM,eAAe,UAAU,MAAM,CAAC,CAAA,OAAQ,CAAC,KAAK,QAAQ;IAE5D,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC,4HAAA,CAAA,SAAM;;;;;0BAEP,8OAAC;gBAAO,WAAU;0BAChB,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAG;wBAC7B,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAE;wBAC5B,YAAY;4BAAE,UAAU;wBAAI;wBAC5B,WAAU;;0CAEV,8OAAC;gCAAG,WAAU;;oCAA0E;kDAClF,8OAAC;wCAAK,WAAU;kDAAgB;;;;;;;;;;;;0CAEtC,8OAAC;gCAAE,WAAU;0CAA+C;;;;;;;;;;;;;;;;;;;;;;0BAQlE,8OAAC;gBAAK,WAAU;;oBAEb,8BACC,8OAAC,0LAAA,CAAA,SAAM,CAAC,OAAO;wBACb,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAG;wBAC7B,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAE;wBAC5B,YAAY;4BAAE,UAAU;4BAAK,OAAO;wBAAI;wBACxC,WAAU;;0CAEV,8OAAC;gCAAG,WAAU;0CAAwC;;;;;;0CACtD,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC,6HAAA,CAAA,UAAK;oDACJ,KAAK,aAAa,KAAK;oDACvB,KAAK,aAAa,KAAK;oDACvB,IAAI;oDACJ,WAAU;;;;;;;;;;;;;;;;sDAIhB,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAK,WAAU;;8EACd,8OAAC,6MAAA,CAAA,UAAO;oEAAC,WAAU;;;;;;gEAClB,aAAa,QAAQ;;;;;;;sEAExB,8OAAC;4DAAK,WAAU;sEAAwB;;;;;;;;;;;;8DAG1C,8OAAC;oDAAG,WAAU;8DACX,aAAa,KAAK;;;;;;8DAGrB,8OAAC;oDAAE,WAAU;8DACV,aAAa,OAAO;;;;;;8DAGvB,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAI,WAAU;;sFACb,8OAAC,+MAAA,CAAA,WAAQ;4EAAC,WAAU;;;;;;wEACnB,aAAa,MAAM;;;;;;;8EAEtB,8OAAC;oEAAI,WAAU;;sFACb,8OAAC,uNAAA,CAAA,eAAY;4EAAC,WAAU;;;;;;wEACvB,IAAI,KAAK,aAAa,WAAW,EAAE,kBAAkB;;;;;;;8EAExD,8OAAC;oEAAI,WAAU;;sFACb,8OAAC,iNAAA,CAAA,YAAS;4EAAC,WAAU;;;;;;wEACpB,aAAa,QAAQ;;;;;;;;;;;;;sEAI1B,8OAAC,4JAAA,CAAA,UAAI;4DACH,MAAM,CAAC,MAAM,EAAE,aAAa,EAAE,EAAE;4DAChC,WAAU;;gEACX;8EAEC,8OAAC,2NAAA,CAAA,iBAAc;oEAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAUxC,8OAAC,0LAAA,CAAA,SAAM,CAAC,OAAO;wBACb,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAG;wBAC7B,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAE;wBAC5B,YAAY;4BAAE,UAAU;4BAAK,OAAO;wBAAI;wBACxC,WAAU;kCAEV,cAAA,8OAAC;4BAAI,WAAU;sCACZ,WAAW,GAAG,CAAC,CAAC,yBACf,8OAAC;oCAEC,WAAU;8CAET;mCAHI;;;;;;;;;;;;;;;kCAUb,8OAAC,0LAAA,CAAA,SAAM,CAAC,OAAO;wBACb,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAG;wBAC7B,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAE;wBAC5B,YAAY;4BAAE,UAAU;4BAAK,OAAO;wBAAI;;0CAExC,8OAAC;gCAAG,WAAU;0CAAwC;;;;;;0CACtD,8OAAC;gCAAI,WAAU;0CACZ,aAAa,GAAG,CAAC,CAAC,MAAM,sBACvB,8OAAC,0LAAA,CAAA,SAAM,CAAC,OAAO;wCAEb,SAAS;4CAAE,SAAS;4CAAG,GAAG;wCAAG;wCAC7B,SAAS;4CAAE,SAAS;4CAAG,GAAG;wCAAE;wCAC5B,YAAY;4CAAE,UAAU;4CAAK,OAAO,MAAM;wCAAM;wCAChD,WAAU;;0DAEV,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC,6HAAA,CAAA,UAAK;oDACJ,KAAK,KAAK,KAAK;oDACf,KAAK,KAAK,KAAK;oDACf,IAAI;oDACJ,WAAU;;;;;;;;;;;0DAId,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC;4DAAK,WAAU;sEACb,KAAK,QAAQ;;;;;;;;;;;kEAIlB,8OAAC;wDAAG,WAAU;kEACX,KAAK,KAAK;;;;;;kEAGb,8OAAC;wDAAE,WAAU;kEACV,KAAK,OAAO;;;;;;kEAGf,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;kFAAM,KAAK,MAAM;;;;;;kFAClB,8OAAC;kFAAM,IAAI,KAAK,KAAK,WAAW,EAAE,kBAAkB;;;;;;;;;;;;0EAEtD,8OAAC;0EAAM,KAAK,QAAQ;;;;;;;;;;;;kEAGtB,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC,4JAAA,CAAA,UAAI;4DACH,MAAM,CAAC,MAAM,EAAE,KAAK,EAAE,EAAE;4DACxB,WAAU;;gEACX;8EAEC,8OAAC,2NAAA,CAAA,iBAAc;oEAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;uCA5C3B,KAAK,EAAE;;;;;;;;;;;;;;;;kCAsDpB,8OAAC,0LAAA,CAAA,SAAM,CAAC,OAAO;wBACb,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAG;wBAC7B,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAE;wBAC5B,YAAY;4BAAE,UAAU;4BAAK,OAAO;wBAAI;wBACxC,WAAU;;0CAEV,8OAAC;gCAAG,WAAU;0CAAwC;;;;;;0CAGtD,8OAAC;gCAAE,WAAU;0CAAuC;;;;;;0CAIpD,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCACC,MAAK;wCACL,aAAY;wCACZ,WAAU;;;;;;kDAEZ,8OAAC;wCAAO,WAAU;kDAAgC;;;;;;;;;;;;;;;;;;;;;;;;0BAMxD,8OAAC,4HAAA,CAAA,SAAM;;;;;;;;;;;AAGb", "debugId": null}}]}