'use client';

import { useState } from 'react';
import { motion } from 'framer-motion';
import {
  PlusIcon,
  PencilIcon,
  TrashIcon,
  EyeIcon,
  DocumentTextIcon,
  CurrencyDollarIcon,
  CalendarIcon,
  CheckCircleIcon,
  ExclamationTriangleIcon,
  ClockIcon,
  MagnifyingGlassIcon,
  ArrowDownTrayIcon,
} from '@heroicons/react/24/outline';

// Mock data - in real app, this would come from API
const invoices = [
  {
    id: 1,
    invoiceNumber: 'INV-2024-001',
    clientName: 'GreenTech Solutions',
    clientEmail: '<EMAIL>',
    projectName: 'EcoCommerce Platform',
    amount: 25000,
    currency: 'USD',
    status: 'Paid',
    issueDate: '2024-01-15T00:00:00Z',
    dueDate: '2024-02-14T00:00:00Z',
    paidDate: '2024-01-28T00:00:00Z',
    description: 'Development of e-commerce platform - Phase 1',
    items: [
      { description: 'Frontend Development', quantity: 80, rate: 150, amount: 12000 },
      { description: 'Backend Development', quantity: 60, rate: 160, amount: 9600 },
      { description: 'UI/UX Design', quantity: 40, rate: 120, amount: 4800 },
      { description: 'Project Management', quantity: 20, rate: 100, amount: 2000 }
    ],
    taxRate: 0.08,
    taxAmount: 2000,
    totalAmount: 27000,
    notes: 'Payment received via wire transfer',
    createdAt: '2024-01-15T10:00:00Z',
    updatedAt: '2024-01-28T14:30:00Z',
  },
  {
    id: 2,
    invoiceNumber: 'INV-2024-002',
    clientName: 'MedTech Innovations',
    clientEmail: '<EMAIL>',
    projectName: 'HealthTracker Mobile App',
    amount: 18000,
    currency: 'USD',
    status: 'Pending',
    issueDate: '2024-01-20T00:00:00Z',
    dueDate: '2024-02-19T00:00:00Z',
    paidDate: null,
    description: 'Mobile app development - Final phase',
    items: [
      { description: 'Mobile Development', quantity: 100, rate: 140, amount: 14000 },
      { description: 'Testing & QA', quantity: 30, rate: 100, amount: 3000 },
      { description: 'App Store Deployment', quantity: 10, rate: 100, amount: 1000 }
    ],
    taxRate: 0.08,
    taxAmount: 1440,
    totalAmount: 19440,
    notes: 'Net 30 payment terms',
    createdAt: '2024-01-20T11:00:00Z',
    updatedAt: '2024-01-22T16:45:00Z',
  },
  {
    id: 3,
    invoiceNumber: 'INV-2024-003',
    clientName: 'FinanceFlow Corp',
    clientEmail: '<EMAIL>',
    projectName: 'Financial Dashboard',
    amount: 35000,
    currency: 'USD',
    status: 'Overdue',
    issueDate: '2024-01-05T00:00:00Z',
    dueDate: '2024-01-20T00:00:00Z',
    paidDate: null,
    description: 'Custom financial analytics dashboard',
    items: [
      { description: 'Dashboard Development', quantity: 120, rate: 160, amount: 19200 },
      { description: 'Data Integration', quantity: 80, rate: 150, amount: 12000 },
      { description: 'Security Implementation', quantity: 40, rate: 180, amount: 7200 }
    ],
    taxRate: 0.08,
    taxAmount: 3072,
    totalAmount: 41472,
    notes: 'Follow up required - payment overdue',
    createdAt: '2024-01-05T09:00:00Z',
    updatedAt: '2024-01-25T13:20:00Z',
  },
  {
    id: 4,
    invoiceNumber: 'INV-2024-004',
    clientName: 'EcoCommerce',
    clientEmail: '<EMAIL>',
    projectName: 'Website Maintenance',
    amount: 5000,
    currency: 'USD',
    status: 'Draft',
    issueDate: '2024-01-25T00:00:00Z',
    dueDate: '2024-02-24T00:00:00Z',
    paidDate: null,
    description: 'Monthly maintenance and support',
    items: [
      { description: 'Website Maintenance', quantity: 20, rate: 120, amount: 2400 },
      { description: 'Content Updates', quantity: 15, rate: 100, amount: 1500 },
      { description: 'Performance Optimization', quantity: 10, rate: 150, amount: 1500 }
    ],
    taxRate: 0.08,
    taxAmount: 432,
    totalAmount: 5432,
    notes: 'Monthly recurring service',
    createdAt: '2024-01-25T14:00:00Z',
    updatedAt: '2024-01-25T14:00:00Z',
  },
];

const statuses = ['All', 'Draft', 'Pending', 'Paid', 'Overdue', 'Cancelled'];

const getStatusColor = (status: string) => {
  switch (status) {
    case 'Paid': return 'bg-green-100 text-green-800';
    case 'Pending': return 'bg-yellow-100 text-yellow-800';
    case 'Overdue': return 'bg-red-100 text-red-800';
    case 'Draft': return 'bg-gray-100 text-gray-800';
    case 'Cancelled': return 'bg-gray-100 text-gray-800';
    default: return 'bg-gray-100 text-gray-800';
  }
};

const getStatusIcon = (status: string) => {
  switch (status) {
    case 'Paid': return CheckCircleIcon;
    case 'Pending': return ClockIcon;
    case 'Overdue': return ExclamationTriangleIcon;
    case 'Draft': return DocumentTextIcon;
    default: return ClockIcon;
  }
};

export default function InvoicesPage() {
  const [invoicesList, setInvoicesList] = useState(invoices);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedStatus, setSelectedStatus] = useState('All');
  const [selectedInvoice, setSelectedInvoice] = useState<any>(null);
  const [showAddModal, setShowAddModal] = useState(false);

  const filteredInvoices = invoicesList.filter(invoice => {
    const matchesSearch = invoice.invoiceNumber.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         invoice.clientName.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         invoice.projectName.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesStatus = selectedStatus === 'All' || invoice.status === selectedStatus;
    
    return matchesSearch && matchesStatus;
  });

  const handleStatusChange = (id: number, newStatus: string) => {
    setInvoicesList(prev => prev.map(invoice => 
      invoice.id === id 
        ? { 
            ...invoice, 
            status: newStatus,
            paidDate: newStatus === 'Paid' ? new Date().toISOString() : null
          }
        : invoice
    ));
  };

  const handleDelete = (id: number) => {
    if (confirm('Are you sure you want to delete this invoice?')) {
      setInvoicesList(prev => prev.filter(invoice => invoice.id !== id));
    }
  };

  const formatCurrency = (amount: number, currency: string = 'USD') => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: currency,
      minimumFractionDigits: 0,
    }).format(amount);
  };

  const formatDate = (dateString: string | null) => {
    if (!dateString) return 'Not set';
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
    });
  };

  const isOverdue = (dueDate: string, status: string) => {
    return status !== 'Paid' && new Date(dueDate) < new Date();
  };

  const getTotalStats = () => {
    const total = invoicesList.reduce((sum, inv) => sum + inv.totalAmount, 0);
    const paid = invoicesList.filter(inv => inv.status === 'Paid').reduce((sum, inv) => sum + inv.totalAmount, 0);
    const pending = invoicesList.filter(inv => inv.status === 'Pending').reduce((sum, inv) => sum + inv.totalAmount, 0);
    const overdue = invoicesList.filter(inv => inv.status === 'Overdue').reduce((sum, inv) => sum + inv.totalAmount, 0);
    
    return { total, paid, pending, overdue };
  };

  const stats = getTotalStats();

  return (
    <div className="py-6">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 md:px-8">
        {/* Header */}
        <div className="md:flex md:items-center md:justify-between mb-8">
          <div className="flex-1 min-w-0">
            <h2 className="text-2xl font-bold leading-7 text-gray-900 sm:text-3xl sm:truncate">
              Invoices
            </h2>
            <p className="mt-1 text-sm text-gray-500">
              Manage client invoices, payments, and billing information
            </p>
          </div>
          <div className="mt-4 flex md:mt-0 md:ml-4">
            <button
              onClick={() => setShowAddModal(true)}
              className="ml-3 inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
            >
              <PlusIcon className="-ml-1 mr-2 h-5 w-5" />
              Create Invoice
            </button>
          </div>
        </div>

        {/* Stats */}
        <div className="mb-6 grid grid-cols-1 gap-5 sm:grid-cols-4">
          <div className="bg-white overflow-hidden shadow rounded-lg">
            <div className="p-5">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <div className="w-8 h-8 bg-blue-500 rounded-md flex items-center justify-center">
                    <span className="text-white text-sm font-bold">{formatCurrency(stats.total / 1000)}K</span>
                  </div>
                </div>
                <div className="ml-5 w-0 flex-1">
                  <dl>
                    <dt className="text-sm font-medium text-gray-500 truncate">Total Invoiced</dt>
                    <dd className="text-lg font-medium text-gray-900">All Time</dd>
                  </dl>
                </div>
              </div>
            </div>
          </div>

          <div className="bg-white overflow-hidden shadow rounded-lg">
            <div className="p-5">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <div className="w-8 h-8 bg-green-500 rounded-md flex items-center justify-center">
                    <span className="text-white text-sm font-bold">{formatCurrency(stats.paid / 1000)}K</span>
                  </div>
                </div>
                <div className="ml-5 w-0 flex-1">
                  <dl>
                    <dt className="text-sm font-medium text-gray-500 truncate">Paid</dt>
                    <dd className="text-lg font-medium text-gray-900">Received</dd>
                  </dl>
                </div>
              </div>
            </div>
          </div>

          <div className="bg-white overflow-hidden shadow rounded-lg">
            <div className="p-5">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <div className="w-8 h-8 bg-yellow-500 rounded-md flex items-center justify-center">
                    <span className="text-white text-sm font-bold">{formatCurrency(stats.pending / 1000)}K</span>
                  </div>
                </div>
                <div className="ml-5 w-0 flex-1">
                  <dl>
                    <dt className="text-sm font-medium text-gray-500 truncate">Pending</dt>
                    <dd className="text-lg font-medium text-gray-900">Outstanding</dd>
                  </dl>
                </div>
              </div>
            </div>
          </div>

          <div className="bg-white overflow-hidden shadow rounded-lg">
            <div className="p-5">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <div className="w-8 h-8 bg-red-500 rounded-md flex items-center justify-center">
                    <span className="text-white text-sm font-bold">{formatCurrency(stats.overdue / 1000)}K</span>
                  </div>
                </div>
                <div className="ml-5 w-0 flex-1">
                  <dl>
                    <dt className="text-sm font-medium text-gray-500 truncate">Overdue</dt>
                    <dd className="text-lg font-medium text-gray-900">Past Due</dd>
                  </dl>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Filters */}
        <div className="mb-6 bg-white shadow rounded-lg">
          <div className="px-4 py-5 sm:p-6">
            <div className="grid grid-cols-1 gap-4 sm:grid-cols-2">
              {/* Search */}
              <div className="relative">
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <MagnifyingGlassIcon className="h-5 w-5 text-gray-400" />
                </div>
                <input
                  type="text"
                  placeholder="Search invoices..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                />
              </div>

              {/* Status Filter */}
              <div>
                <select
                  value={selectedStatus}
                  onChange={(e) => setSelectedStatus(e.target.value)}
                  className="block w-full pl-3 pr-10 py-2 text-base border border-gray-300 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm rounded-md"
                >
                  {statuses.map(status => (
                    <option key={status} value={status}>{status}</option>
                  ))}
                </select>
              </div>
            </div>
          </div>
        </div>

        {/* Invoices List */}
        <div className="bg-white shadow overflow-hidden sm:rounded-md">
          <ul className="divide-y divide-gray-200">
            {filteredInvoices.map((invoice, index) => {
              const StatusIcon = getStatusIcon(invoice.status);
              return (
                <motion.li
                  key={invoice.id}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.3, delay: index * 0.1 }}
                >
                  <div className="px-4 py-4 sm:px-6">
                    <div className="flex items-center justify-between">
                      <div className="flex items-start space-x-4 flex-1">
                        <div className="flex-shrink-0">
                          <div className={`w-10 h-10 rounded-lg flex items-center justify-center ${getStatusColor(invoice.status)}`}>
                            <StatusIcon className="w-6 h-6" />
                          </div>
                        </div>
                        
                        <div className="flex-1 min-w-0">
                          <div className="flex items-center space-x-2 mb-1">
                            <h3 className="text-lg font-medium text-gray-900">
                              {invoice.invoiceNumber}
                            </h3>
                            <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(invoice.status)}`}>
                              {invoice.status}
                            </span>
                            {isOverdue(invoice.dueDate, invoice.status) && (
                              <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                                Overdue
                              </span>
                            )}
                          </div>
                          
                          <div className="flex items-center space-x-4 text-sm text-gray-500 mb-2">
                            <span>{invoice.clientName}</span>
                            <span>{invoice.projectName}</span>
                            <span className="font-medium text-gray-900">{formatCurrency(invoice.totalAmount)}</span>
                          </div>
                          
                          <div className="flex items-center space-x-4 text-sm text-gray-500">
                            <div className="flex items-center">
                              <CalendarIcon className="h-4 w-4 mr-1" />
                              Issued: {formatDate(invoice.issueDate)}
                            </div>
                            <div className="flex items-center">
                              <CalendarIcon className="h-4 w-4 mr-1" />
                              Due: {formatDate(invoice.dueDate)}
                            </div>
                            {invoice.paidDate && (
                              <div className="flex items-center">
                                <CheckCircleIcon className="h-4 w-4 mr-1 text-green-500" />
                                Paid: {formatDate(invoice.paidDate)}
                              </div>
                            )}
                          </div>
                        </div>
                      </div>
                      
                      <div className="flex items-center space-x-2 ml-4">
                        <button
                          onClick={() => setSelectedInvoice(invoice)}
                          className="text-gray-400 hover:text-blue-600 transition-colors"
                          title="View Details"
                        >
                          <EyeIcon className="h-5 w-5" />
                        </button>
                        <button
                          className="text-gray-400 hover:text-blue-600 transition-colors"
                          title="Edit"
                        >
                          <PencilIcon className="h-5 w-5" />
                        </button>
                        <button
                          className="text-gray-400 hover:text-green-600 transition-colors"
                          title="Download PDF"
                        >
                          <ArrowDownTrayIcon className="h-5 w-5" />
                        </button>
                        <button
                          onClick={() => handleDelete(invoice.id)}
                          className="text-gray-400 hover:text-red-600 transition-colors"
                          title="Delete"
                        >
                          <TrashIcon className="h-5 w-5" />
                        </button>
                      </div>
                    </div>
                  </div>
                </motion.li>
              );
            })}
          </ul>
        </div>

        {/* Empty State */}
        {filteredInvoices.length === 0 && (
          <div className="bg-white shadow rounded-lg">
            <div className="text-center py-12">
              <DocumentTextIcon className="mx-auto h-12 w-12 text-gray-400" />
              <h3 className="mt-2 text-sm font-medium text-gray-900">No invoices found</h3>
              <p className="mt-1 text-sm text-gray-500">
                Try adjusting your search or filter criteria.
              </p>
            </div>
          </div>
        )}

        {/* Invoice Detail Modal */}
        {selectedInvoice && (
          <div className="fixed inset-0 z-50 overflow-y-auto">
            <div className="flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
              <div className="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" onClick={() => setSelectedInvoice(null)} />
              
              <div className="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-4xl sm:w-full">
                <div className="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4 max-h-96 overflow-y-auto">
                  <div className="sm:flex sm:items-start">
                    <div className="mt-3 text-center sm:mt-0 sm:text-left w-full">
                      <h3 className="text-lg leading-6 font-medium text-gray-900 mb-4">
                        Invoice {selectedInvoice.invoiceNumber}
                      </h3>
                      
                      <div className="space-y-4">
                        <div className="grid grid-cols-2 gap-4">
                          <div>
                            <label className="block text-sm font-medium text-gray-700">Client</label>
                            <p className="mt-1 text-sm text-gray-900">{selectedInvoice.clientName}</p>
                          </div>
                          <div>
                            <label className="block text-sm font-medium text-gray-700">Project</label>
                            <p className="mt-1 text-sm text-gray-900">{selectedInvoice.projectName}</p>
                          </div>
                          <div>
                            <label className="block text-sm font-medium text-gray-700">Status</label>
                            <span className={`mt-1 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(selectedInvoice.status)}`}>
                              {selectedInvoice.status}
                            </span>
                          </div>
                          <div>
                            <label className="block text-sm font-medium text-gray-700">Total Amount</label>
                            <p className="mt-1 text-sm text-gray-900 font-semibold">{formatCurrency(selectedInvoice.totalAmount)}</p>
                          </div>
                        </div>
                        
                        <div>
                          <label className="block text-sm font-medium text-gray-700">Line Items</label>
                          <div className="mt-1 overflow-x-auto">
                            <table className="min-w-full divide-y divide-gray-200">
                              <thead className="bg-gray-50">
                                <tr>
                                  <th className="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase">Description</th>
                                  <th className="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase">Qty</th>
                                  <th className="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase">Rate</th>
                                  <th className="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase">Amount</th>
                                </tr>
                              </thead>
                              <tbody className="bg-white divide-y divide-gray-200">
                                {selectedInvoice.items.map((item: any, index: number) => (
                                  <tr key={index}>
                                    <td className="px-3 py-2 text-sm text-gray-900">{item.description}</td>
                                    <td className="px-3 py-2 text-sm text-gray-900">{item.quantity}</td>
                                    <td className="px-3 py-2 text-sm text-gray-900">{formatCurrency(item.rate)}</td>
                                    <td className="px-3 py-2 text-sm text-gray-900">{formatCurrency(item.amount)}</td>
                                  </tr>
                                ))}
                              </tbody>
                            </table>
                          </div>
                        </div>
                        
                        <div className="grid grid-cols-3 gap-4">
                          <div>
                            <label className="block text-sm font-medium text-gray-700">Issue Date</label>
                            <p className="mt-1 text-sm text-gray-900">{formatDate(selectedInvoice.issueDate)}</p>
                          </div>
                          <div>
                            <label className="block text-sm font-medium text-gray-700">Due Date</label>
                            <p className="mt-1 text-sm text-gray-900">{formatDate(selectedInvoice.dueDate)}</p>
                          </div>
                          <div>
                            <label className="block text-sm font-medium text-gray-700">Paid Date</label>
                            <p className="mt-1 text-sm text-gray-900">{formatDate(selectedInvoice.paidDate)}</p>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
                <div className="bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
                  <button
                    type="button"
                    className="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-blue-600 text-base font-medium text-white hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 sm:ml-3 sm:w-auto sm:text-sm"
                  >
                    Edit Invoice
                  </button>
                  <button
                    type="button"
                    className="mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 sm:mt-0 sm:w-auto sm:text-sm"
                    onClick={() => setSelectedInvoice(null)}
                  >
                    Close
                  </button>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
