{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Project%20By%20AI/Technoloway/Technoloway%20%28Processing%29/technoloway-simple/src/app/admin/about-pages/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport { motion } from 'framer-motion';\nimport {\n  PlusIcon,\n  PencilIcon,\n  TrashIcon,\n  EyeIcon,\n  DocumentTextIcon,\n  CheckCircleIcon,\n  XCircleIcon,\n  ClockIcon,\n} from '@heroicons/react/24/outline';\n\n// Mock data - in real app, this would come from API\nconst aboutPages = [\n  {\n    id: 1,\n    title: 'About Technoloway',\n    slug: 'about',\n    content: `<h2>Our Story</h2>\n    <p>Founded in 2020, Technoloway has been at the forefront of software innovation, helping businesses transform their ideas into digital reality. We specialize in creating scalable, modern solutions that drive growth and efficiency.</p>\n    \n    <h3>Our Mission</h3>\n    <p>To democratize cutting-edge technology and make it accessible to businesses of all sizes. We believe that every company deserves world-class software solutions.</p>\n    \n    <h3>Our Values</h3>\n    <ul>\n      <li>Innovation: We stay ahead of technology trends</li>\n      <li>Quality: We deliver excellence in every project</li>\n      <li>Partnership: We work as an extension of your team</li>\n      <li>Transparency: We maintain open communication</li>\n    </ul>`,\n    metaTitle: 'About Technoloway - Leading Software Development Company',\n    metaDescription: 'Learn about Technoloway\\'s mission, values, and commitment to delivering exceptional software solutions for businesses worldwide.',\n    isPublished: true,\n    publishedAt: '2024-01-15T10:00:00Z',\n    createdAt: '2024-01-10T09:00:00Z',\n    updatedAt: '2024-01-20T14:30:00Z',\n    author: 'Sarah Johnson',\n  },\n  {\n    id: 2,\n    title: 'Our Team',\n    slug: 'team',\n    content: `<h2>Meet Our Expert Team</h2>\n    <p>Our diverse team of developers, designers, and strategists brings together decades of experience in software development, user experience design, and business strategy.</p>\n    \n    <h3>Leadership</h3>\n    <p>Led by industry veterans with experience at top technology companies, our leadership team guides our vision and ensures we deliver exceptional results.</p>\n    \n    <h3>Development Team</h3>\n    <p>Our developers are experts in modern technologies including React, Node.js, Python, and cloud platforms. They stay current with the latest trends and best practices.</p>`,\n    metaTitle: 'Our Team - Expert Software Developers | Technoloway',\n    metaDescription: 'Meet the talented team behind Technoloway. Our expert developers, designers, and strategists are passionate about creating exceptional software solutions.',\n    isPublished: true,\n    publishedAt: '2024-01-12T11:00:00Z',\n    createdAt: '2024-01-08T10:00:00Z',\n    updatedAt: '2024-01-18T16:45:00Z',\n    author: 'Mike Chen',\n  },\n  {\n    id: 3,\n    title: 'Our Process',\n    slug: 'process',\n    content: `<h2>How We Work</h2>\n    <p>Our proven development process ensures successful project delivery while maintaining transparency and collaboration throughout.</p>\n    \n    <h3>Discovery & Planning</h3>\n    <p>We start by understanding your business goals, technical requirements, and user needs. This phase includes stakeholder interviews, technical analysis, and project planning.</p>\n    \n    <h3>Design & Development</h3>\n    <p>Our iterative approach includes regular check-ins, demos, and feedback sessions to ensure we're building exactly what you need.</p>\n    \n    <h3>Testing & Deployment</h3>\n    <p>Comprehensive testing and smooth deployment ensure your solution is ready for production use.</p>`,\n    metaTitle: 'Our Development Process | Technoloway',\n    metaDescription: 'Learn about Technoloway\\'s proven software development process that ensures successful project delivery and client satisfaction.',\n    isPublished: false,\n    publishedAt: null,\n    createdAt: '2024-01-05T14:00:00Z',\n    updatedAt: '2024-01-15T13:20:00Z',\n    author: 'Emily Davis',\n  },\n];\n\nexport default function AboutPagesPage() {\n  const [pages, setPages] = useState(aboutPages);\n  const [selectedPage, setSelectedPage] = useState<any>(null);\n  const [showAddModal, setShowAddModal] = useState(false);\n\n  const handleTogglePublished = (id: number) => {\n    setPages(prev => prev.map(page => \n      page.id === id \n        ? { \n            ...page, \n            isPublished: !page.isPublished,\n            publishedAt: !page.isPublished ? new Date().toISOString() : null\n          }\n        : page\n    ));\n  };\n\n  const handleDelete = (id: number) => {\n    if (confirm('Are you sure you want to delete this page?')) {\n      setPages(prev => prev.filter(page => page.id !== id));\n    }\n  };\n\n  const formatDate = (dateString: string | null) => {\n    if (!dateString) return 'Not published';\n    return new Date(dateString).toLocaleDateString('en-US', {\n      year: 'numeric',\n      month: 'short',\n      day: 'numeric',\n      hour: '2-digit',\n      minute: '2-digit',\n    });\n  };\n\n  const stripHtml = (html: string) => {\n    return html.replace(/<[^>]*>/g, '').substring(0, 150) + '...';\n  };\n\n  return (\n    <div className=\"py-6\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 md:px-8\">\n        {/* Header */}\n        <div className=\"md:flex md:items-center md:justify-between mb-8\">\n          <div className=\"flex-1 min-w-0\">\n            <h2 className=\"text-2xl font-bold leading-7 text-gray-900 sm:text-3xl sm:truncate\">\n              About Pages\n            </h2>\n            <p className=\"mt-1 text-sm text-gray-500\">\n              Manage your company's about pages, team information, and process documentation\n            </p>\n          </div>\n          <div className=\"mt-4 flex md:mt-0 md:ml-4\">\n            <button\n              onClick={() => setShowAddModal(true)}\n              className=\"ml-3 inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500\"\n            >\n              <PlusIcon className=\"-ml-1 mr-2 h-5 w-5\" />\n              Add Page\n            </button>\n          </div>\n        </div>\n\n        {/* Stats */}\n        <div className=\"mb-6 grid grid-cols-1 gap-5 sm:grid-cols-3\">\n          <div className=\"bg-white overflow-hidden shadow rounded-lg\">\n            <div className=\"p-5\">\n              <div className=\"flex items-center\">\n                <div className=\"flex-shrink-0\">\n                  <div className=\"w-8 h-8 bg-blue-500 rounded-md flex items-center justify-center\">\n                    <span className=\"text-white text-sm font-bold\">{pages.length}</span>\n                  </div>\n                </div>\n                <div className=\"ml-5 w-0 flex-1\">\n                  <dl>\n                    <dt className=\"text-sm font-medium text-gray-500 truncate\">Total Pages</dt>\n                    <dd className=\"text-lg font-medium text-gray-900\">Created</dd>\n                  </dl>\n                </div>\n              </div>\n            </div>\n          </div>\n\n          <div className=\"bg-white overflow-hidden shadow rounded-lg\">\n            <div className=\"p-5\">\n              <div className=\"flex items-center\">\n                <div className=\"flex-shrink-0\">\n                  <div className=\"w-8 h-8 bg-green-500 rounded-md flex items-center justify-center\">\n                    <span className=\"text-white text-sm font-bold\">\n                      {pages.filter(p => p.isPublished).length}\n                    </span>\n                  </div>\n                </div>\n                <div className=\"ml-5 w-0 flex-1\">\n                  <dl>\n                    <dt className=\"text-sm font-medium text-gray-500 truncate\">Published</dt>\n                    <dd className=\"text-lg font-medium text-gray-900\">Live</dd>\n                  </dl>\n                </div>\n              </div>\n            </div>\n          </div>\n\n          <div className=\"bg-white overflow-hidden shadow rounded-lg\">\n            <div className=\"p-5\">\n              <div className=\"flex items-center\">\n                <div className=\"flex-shrink-0\">\n                  <div className=\"w-8 h-8 bg-yellow-500 rounded-md flex items-center justify-center\">\n                    <span className=\"text-white text-sm font-bold\">\n                      {pages.filter(p => !p.isPublished).length}\n                    </span>\n                  </div>\n                </div>\n                <div className=\"ml-5 w-0 flex-1\">\n                  <dl>\n                    <dt className=\"text-sm font-medium text-gray-500 truncate\">Drafts</dt>\n                    <dd className=\"text-lg font-medium text-gray-900\">Unpublished</dd>\n                  </dl>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        {/* Pages List */}\n        <div className=\"bg-white shadow overflow-hidden sm:rounded-md\">\n          <ul className=\"divide-y divide-gray-200\">\n            {pages.map((page, index) => (\n              <motion.li\n                key={page.id}\n                initial={{ opacity: 0, y: 20 }}\n                animate={{ opacity: 1, y: 0 }}\n                transition={{ duration: 0.3, delay: index * 0.1 }}\n              >\n                <div className=\"px-4 py-4 sm:px-6\">\n                  <div className=\"flex items-center justify-between\">\n                    <div className=\"flex items-start space-x-4 flex-1\">\n                      <div className=\"flex-shrink-0\">\n                        <div className={`w-10 h-10 rounded-lg flex items-center justify-center ${\n                          page.isPublished ? 'bg-green-100' : 'bg-gray-100'\n                        }`}>\n                          {page.isPublished ? (\n                            <CheckCircleIcon className=\"w-6 h-6 text-green-600\" />\n                          ) : (\n                            <ClockIcon className=\"w-6 h-6 text-gray-400\" />\n                          )}\n                        </div>\n                      </div>\n                      \n                      <div className=\"flex-1 min-w-0\">\n                        <div className=\"flex items-center space-x-2 mb-1\">\n                          <h3 className=\"text-lg font-medium text-gray-900 truncate\">\n                            {page.title}\n                          </h3>\n                          <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${\n                            page.isPublished \n                              ? 'bg-green-100 text-green-800'\n                              : 'bg-gray-100 text-gray-800'\n                          }`}>\n                            {page.isPublished ? 'Published' : 'Draft'}\n                          </span>\n                        </div>\n                        \n                        <p className=\"text-sm text-gray-600 mb-2\">\n                          /{page.slug}\n                        </p>\n                        \n                        <p className=\"text-sm text-gray-600 mb-3\">\n                          {stripHtml(page.content)}\n                        </p>\n                        \n                        <div className=\"flex items-center space-x-4 text-sm text-gray-500\">\n                          <span>Author: {page.author}</span>\n                          <span>Created: {formatDate(page.createdAt)}</span>\n                          <span>Updated: {formatDate(page.updatedAt)}</span>\n                          {page.publishedAt && (\n                            <span>Published: {formatDate(page.publishedAt)}</span>\n                          )}\n                        </div>\n                      </div>\n                    </div>\n                    \n                    <div className=\"flex items-center space-x-2 ml-4\">\n                      <button\n                        onClick={() => setSelectedPage(page)}\n                        className=\"text-gray-400 hover:text-blue-600 transition-colors\"\n                        title=\"View\"\n                      >\n                        <EyeIcon className=\"h-5 w-5\" />\n                      </button>\n                      <button\n                        className=\"text-gray-400 hover:text-blue-600 transition-colors\"\n                        title=\"Edit\"\n                      >\n                        <PencilIcon className=\"h-5 w-5\" />\n                      </button>\n                      <button\n                        onClick={() => handleTogglePublished(page.id)}\n                        className={`text-gray-400 hover:text-green-600 transition-colors ${\n                          page.isPublished ? 'text-green-600' : ''\n                        }`}\n                        title={page.isPublished ? 'Unpublish' : 'Publish'}\n                      >\n                        <CheckCircleIcon className=\"h-5 w-5\" />\n                      </button>\n                      <button\n                        onClick={() => handleDelete(page.id)}\n                        className=\"text-gray-400 hover:text-red-600 transition-colors\"\n                        title=\"Delete\"\n                      >\n                        <TrashIcon className=\"h-5 w-5\" />\n                      </button>\n                    </div>\n                  </div>\n                </div>\n              </motion.li>\n            ))}\n          </ul>\n        </div>\n\n        {/* Empty State */}\n        {pages.length === 0 && (\n          <div className=\"bg-white shadow rounded-lg\">\n            <div className=\"text-center py-12\">\n              <DocumentTextIcon className=\"mx-auto h-12 w-12 text-gray-400\" />\n              <h3 className=\"mt-2 text-sm font-medium text-gray-900\">No about pages</h3>\n              <p className=\"mt-1 text-sm text-gray-500\">\n                Get started by creating your first about page.\n              </p>\n              <div className=\"mt-6\">\n                <button\n                  onClick={() => setShowAddModal(true)}\n                  className=\"inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500\"\n                >\n                  <PlusIcon className=\"-ml-1 mr-2 h-5 w-5\" />\n                  Add Page\n                </button>\n              </div>\n            </div>\n          </div>\n        )}\n\n        {/* Page Preview Modal */}\n        {selectedPage && (\n          <div className=\"fixed inset-0 z-50 overflow-y-auto\">\n            <div className=\"flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0\">\n              <div className=\"fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity\" onClick={() => setSelectedPage(null)} />\n              \n              <div className=\"inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-4xl sm:w-full\">\n                <div className=\"bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4\">\n                  <div className=\"sm:flex sm:items-start\">\n                    <div className=\"mt-3 text-center sm:mt-0 sm:text-left w-full\">\n                      <h3 className=\"text-lg leading-6 font-medium text-gray-900 mb-4\">\n                        {selectedPage.title}\n                      </h3>\n                      \n                      <div className=\"mb-4 p-4 bg-gray-50 rounded-lg\">\n                        <h4 className=\"text-sm font-medium text-gray-700 mb-2\">SEO Information</h4>\n                        <p className=\"text-sm text-gray-600 mb-1\">\n                          <strong>Meta Title:</strong> {selectedPage.metaTitle}\n                        </p>\n                        <p className=\"text-sm text-gray-600 mb-1\">\n                          <strong>Meta Description:</strong> {selectedPage.metaDescription}\n                        </p>\n                        <p className=\"text-sm text-gray-600\">\n                          <strong>URL:</strong> /{selectedPage.slug}\n                        </p>\n                      </div>\n                      \n                      <div className=\"prose max-w-none\">\n                        <div dangerouslySetInnerHTML={{ __html: selectedPage.content }} />\n                      </div>\n                    </div>\n                  </div>\n                </div>\n                <div className=\"bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse\">\n                  <button\n                    type=\"button\"\n                    className=\"w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-blue-600 text-base font-medium text-white hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 sm:ml-3 sm:w-auto sm:text-sm\"\n                  >\n                    Edit Page\n                  </button>\n                  <button\n                    type=\"button\"\n                    className=\"mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 sm:mt-0 sm:w-auto sm:text-sm\"\n                    onClick={() => setSelectedPage(null)}\n                  >\n                    Close\n                  </button>\n                </div>\n              </div>\n            </div>\n          </div>\n        )}\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAJA;;;;;AAeA,oDAAoD;AACpD,MAAM,aAAa;IACjB;QACE,IAAI;QACJ,OAAO;QACP,MAAM;QACN,SAAS,CAAC;;;;;;;;;;;;SAYL,CAAC;QACN,WAAW;QACX,iBAAiB;QACjB,aAAa;QACb,aAAa;QACb,WAAW;QACX,WAAW;QACX,QAAQ;IACV;IACA;QACE,IAAI;QACJ,OAAO;QACP,MAAM;QACN,SAAS,CAAC;;;;;;;gLAOkK,CAAC;QAC7K,WAAW;QACX,iBAAiB;QACjB,aAAa;QACb,aAAa;QACb,WAAW;QACX,WAAW;QACX,QAAQ;IACV;IACA;QACE,IAAI;QACJ,OAAO;QACP,MAAM;QACN,SAAS,CAAC;;;;;;;;;;wGAU0F,CAAC;QACrG,WAAW;QACX,iBAAiB;QACjB,aAAa;QACb,aAAa;QACb,WAAW;QACX,WAAW;QACX,QAAQ;IACV;CACD;AAEc,SAAS;IACtB,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAO;IACtD,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEjD,MAAM,wBAAwB,CAAC;QAC7B,SAAS,CAAA,OAAQ,KAAK,GAAG,CAAC,CAAA,OACxB,KAAK,EAAE,KAAK,KACR;oBACE,GAAG,IAAI;oBACP,aAAa,CAAC,KAAK,WAAW;oBAC9B,aAAa,CAAC,KAAK,WAAW,GAAG,IAAI,OAAO,WAAW,KAAK;gBAC9D,IACA;IAER;IAEA,MAAM,eAAe,CAAC;QACpB,IAAI,QAAQ,+CAA+C;YACzD,SAAS,CAAA,OAAQ,KAAK,MAAM,CAAC,CAAA,OAAQ,KAAK,EAAE,KAAK;QACnD;IACF;IAEA,MAAM,aAAa,CAAC;QAClB,IAAI,CAAC,YAAY,OAAO;QACxB,OAAO,IAAI,KAAK,YAAY,kBAAkB,CAAC,SAAS;YACtD,MAAM;YACN,OAAO;YACP,KAAK;YACL,MAAM;YACN,QAAQ;QACV;IACF;IAEA,MAAM,YAAY,CAAC;QACjB,OAAO,KAAK,OAAO,CAAC,YAAY,IAAI,SAAS,CAAC,GAAG,OAAO;IAC1D;IAEA,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;;8BAEb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAAqE;;;;;;8CAGnF,8OAAC;oCAAE,WAAU;8CAA6B;;;;;;;;;;;;sCAI5C,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCACC,SAAS,IAAM,gBAAgB;gCAC/B,WAAU;;kDAEV,8OAAC,+MAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;oCAAuB;;;;;;;;;;;;;;;;;;8BAOjD,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDAAK,WAAU;8DAAgC,MAAM,MAAM;;;;;;;;;;;;;;;;sDAGhE,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;;kEACC,8OAAC;wDAAG,WAAU;kEAA6C;;;;;;kEAC3D,8OAAC;wDAAG,WAAU;kEAAoC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAO5D,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDAAK,WAAU;8DACb,MAAM,MAAM,CAAC,CAAA,IAAK,EAAE,WAAW,EAAE,MAAM;;;;;;;;;;;;;;;;sDAI9C,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;;kEACC,8OAAC;wDAAG,WAAU;kEAA6C;;;;;;kEAC3D,8OAAC;wDAAG,WAAU;kEAAoC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAO5D,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDAAK,WAAU;8DACb,MAAM,MAAM,CAAC,CAAA,IAAK,CAAC,EAAE,WAAW,EAAE,MAAM;;;;;;;;;;;;;;;;sDAI/C,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;;kEACC,8OAAC;wDAAG,WAAU;kEAA6C;;;;;;kEAC3D,8OAAC;wDAAG,WAAU;kEAAoC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAS9D,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAG,WAAU;kCACX,MAAM,GAAG,CAAC,CAAC,MAAM,sBAChB,8OAAC,0LAAA,CAAA,SAAM,CAAC,EAAE;gCAER,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAG;gCAC7B,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAE;gCAC5B,YAAY;oCAAE,UAAU;oCAAK,OAAO,QAAQ;gCAAI;0CAEhD,cAAA,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC;4DAAI,WAAW,CAAC,sDAAsD,EACrE,KAAK,WAAW,GAAG,iBAAiB,eACpC;sEACC,KAAK,WAAW,iBACf,8OAAC,6NAAA,CAAA,kBAAe;gEAAC,WAAU;;;;;qFAE3B,8OAAC,iNAAA,CAAA,YAAS;gEAAC,WAAU;;;;;;;;;;;;;;;;kEAK3B,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEAAG,WAAU;kFACX,KAAK,KAAK;;;;;;kFAEb,8OAAC;wEAAK,WAAW,CAAC,wEAAwE,EACxF,KAAK,WAAW,GACZ,gCACA,6BACJ;kFACC,KAAK,WAAW,GAAG,cAAc;;;;;;;;;;;;0EAItC,8OAAC;gEAAE,WAAU;;oEAA6B;oEACtC,KAAK,IAAI;;;;;;;0EAGb,8OAAC;gEAAE,WAAU;0EACV,UAAU,KAAK,OAAO;;;;;;0EAGzB,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;;4EAAK;4EAAS,KAAK,MAAM;;;;;;;kFAC1B,8OAAC;;4EAAK;4EAAU,WAAW,KAAK,SAAS;;;;;;;kFACzC,8OAAC;;4EAAK;4EAAU,WAAW,KAAK,SAAS;;;;;;;oEACxC,KAAK,WAAW,kBACf,8OAAC;;4EAAK;4EAAY,WAAW,KAAK,WAAW;;;;;;;;;;;;;;;;;;;;;;;;;0DAMrD,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDACC,SAAS,IAAM,gBAAgB;wDAC/B,WAAU;wDACV,OAAM;kEAEN,cAAA,8OAAC,6MAAA,CAAA,UAAO;4DAAC,WAAU;;;;;;;;;;;kEAErB,8OAAC;wDACC,WAAU;wDACV,OAAM;kEAEN,cAAA,8OAAC,mNAAA,CAAA,aAAU;4DAAC,WAAU;;;;;;;;;;;kEAExB,8OAAC;wDACC,SAAS,IAAM,sBAAsB,KAAK,EAAE;wDAC5C,WAAW,CAAC,qDAAqD,EAC/D,KAAK,WAAW,GAAG,mBAAmB,IACtC;wDACF,OAAO,KAAK,WAAW,GAAG,cAAc;kEAExC,cAAA,8OAAC,6NAAA,CAAA,kBAAe;4DAAC,WAAU;;;;;;;;;;;kEAE7B,8OAAC;wDACC,SAAS,IAAM,aAAa,KAAK,EAAE;wDACnC,WAAU;wDACV,OAAM;kEAEN,cAAA,8OAAC,iNAAA,CAAA,YAAS;4DAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;+BAjFxB,KAAK,EAAE;;;;;;;;;;;;;;;gBA4FnB,MAAM,MAAM,KAAK,mBAChB,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,+NAAA,CAAA,mBAAgB;gCAAC,WAAU;;;;;;0CAC5B,8OAAC;gCAAG,WAAU;0CAAyC;;;;;;0CACvD,8OAAC;gCAAE,WAAU;0CAA6B;;;;;;0CAG1C,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCACC,SAAS,IAAM,gBAAgB;oCAC/B,WAAU;;sDAEV,8OAAC,+MAAA,CAAA,WAAQ;4CAAC,WAAU;;;;;;wCAAuB;;;;;;;;;;;;;;;;;;;;;;;gBASpD,8BACC,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;gCAA6D,SAAS,IAAM,gBAAgB;;;;;;0CAE3G,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAG,WAAU;kEACX,aAAa,KAAK;;;;;;kEAGrB,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAG,WAAU;0EAAyC;;;;;;0EACvD,8OAAC;gEAAE,WAAU;;kFACX,8OAAC;kFAAO;;;;;;oEAAoB;oEAAE,aAAa,SAAS;;;;;;;0EAEtD,8OAAC;gEAAE,WAAU;;kFACX,8OAAC;kFAAO;;;;;;oEAA0B;oEAAE,aAAa,eAAe;;;;;;;0EAElE,8OAAC;gEAAE,WAAU;;kFACX,8OAAC;kFAAO;;;;;;oEAAa;oEAAG,aAAa,IAAI;;;;;;;;;;;;;kEAI7C,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC;4DAAI,yBAAyB;gEAAE,QAAQ,aAAa,OAAO;4DAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;kDAKrE,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDACC,MAAK;gDACL,WAAU;0DACX;;;;;;0DAGD,8OAAC;gDACC,MAAK;gDACL,WAAU;gDACV,SAAS,IAAM,gBAAgB;0DAChC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAWnB", "debugId": null}}, {"offset": {"line": 945, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Project%20By%20AI/Technoloway/Technoloway%20%28Processing%29/technoloway-simple/node_modules/%40heroicons/react/24/outline/esm/PlusIcon.js"], "sourcesContent": ["import * as React from \"react\";\nfunction PlusIcon({\n  title,\n  titleId,\n  ...props\n}, svgRef) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    xmlns: \"http://www.w3.org/2000/svg\",\n    fill: \"none\",\n    viewBox: \"0 0 24 24\",\n    strokeWidth: 1.5,\n    stroke: \"currentColor\",\n    \"aria-hidden\": \"true\",\n    \"data-slot\": \"icon\",\n    ref: svgRef,\n    \"aria-labelledby\": titleId\n  }, props), title ? /*#__PURE__*/React.createElement(\"title\", {\n    id: titleId\n  }, title) : null, /*#__PURE__*/React.createElement(\"path\", {\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\",\n    d: \"M12 4.5v15m7.5-7.5h-15\"\n  }));\n}\nconst ForwardRef = /*#__PURE__*/ React.forwardRef(PlusIcon);\nexport default ForwardRef;"], "names": [], "mappings": ";;;AAAA;;AACA,SAAS,SAAS,EAChB,KAAK,EACL,OAAO,EACP,GAAG,OACJ,EAAE,MAAM;IACP,OAAO,WAAW,GAAE,CAAA,GAAA,qMAAA,CAAA,gBAAmB,AAAD,EAAE,OAAO,OAAO,MAAM,CAAC;QAC3D,OAAO;QACP,MAAM;QACN,SAAS;QACT,aAAa;QACb,QAAQ;QACR,eAAe;QACf,aAAa;QACb,KAAK;QACL,mBAAmB;IACrB,GAAG,QAAQ,QAAQ,WAAW,GAAE,CAAA,GAAA,qMAAA,CAAA,gBAAmB,AAAD,EAAE,SAAS;QAC3D,IAAI;IACN,GAAG,SAAS,MAAM,WAAW,GAAE,CAAA,GAAA,qMAAA,CAAA,gBAAmB,AAAD,EAAE,QAAQ;QACzD,eAAe;QACf,gBAAgB;QAChB,GAAG;IACL;AACF;AACA,MAAM,aAAa,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAAE;uCACnC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 987, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Project%20By%20AI/Technoloway/Technoloway%20%28Processing%29/technoloway-simple/node_modules/%40heroicons/react/24/outline/esm/PencilIcon.js"], "sourcesContent": ["import * as React from \"react\";\nfunction PencilIcon({\n  title,\n  titleId,\n  ...props\n}, svgRef) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    xmlns: \"http://www.w3.org/2000/svg\",\n    fill: \"none\",\n    viewBox: \"0 0 24 24\",\n    strokeWidth: 1.5,\n    stroke: \"currentColor\",\n    \"aria-hidden\": \"true\",\n    \"data-slot\": \"icon\",\n    ref: svgRef,\n    \"aria-labelledby\": titleId\n  }, props), title ? /*#__PURE__*/React.createElement(\"title\", {\n    id: titleId\n  }, title) : null, /*#__PURE__*/React.createElement(\"path\", {\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\",\n    d: \"m16.862 4.487 1.687-1.688a1.875 1.875 0 1 1 2.652 2.652L6.832 19.82a4.5 4.5 0 0 1-1.897 1.13l-2.685.8.8-2.685a4.5 4.5 0 0 1 1.13-1.897L16.863 4.487Zm0 0L19.5 7.125\"\n  }));\n}\nconst ForwardRef = /*#__PURE__*/ React.forwardRef(PencilIcon);\nexport default ForwardRef;"], "names": [], "mappings": ";;;AAAA;;AACA,SAAS,WAAW,EAClB,KAAK,EACL,OAAO,EACP,GAAG,OACJ,EAAE,MAAM;IACP,OAAO,WAAW,GAAE,CAAA,GAAA,qMAAA,CAAA,gBAAmB,AAAD,EAAE,OAAO,OAAO,MAAM,CAAC;QAC3D,OAAO;QACP,MAAM;QACN,SAAS;QACT,aAAa;QACb,QAAQ;QACR,eAAe;QACf,aAAa;QACb,KAAK;QACL,mBAAmB;IACrB,GAAG,QAAQ,QAAQ,WAAW,GAAE,CAAA,GAAA,qMAAA,CAAA,gBAAmB,AAAD,EAAE,SAAS;QAC3D,IAAI;IACN,GAAG,SAAS,MAAM,WAAW,GAAE,CAAA,GAAA,qMAAA,CAAA,gBAAmB,AAAD,EAAE,QAAQ;QACzD,eAAe;QACf,gBAAgB;QAChB,GAAG;IACL;AACF;AACA,MAAM,aAAa,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAAE;uCACnC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1029, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Project%20By%20AI/Technoloway/Technoloway%20%28Processing%29/technoloway-simple/node_modules/%40heroicons/react/24/outline/esm/TrashIcon.js"], "sourcesContent": ["import * as React from \"react\";\nfunction TrashIcon({\n  title,\n  titleId,\n  ...props\n}, svgRef) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    xmlns: \"http://www.w3.org/2000/svg\",\n    fill: \"none\",\n    viewBox: \"0 0 24 24\",\n    strokeWidth: 1.5,\n    stroke: \"currentColor\",\n    \"aria-hidden\": \"true\",\n    \"data-slot\": \"icon\",\n    ref: svgRef,\n    \"aria-labelledby\": titleId\n  }, props), title ? /*#__PURE__*/React.createElement(\"title\", {\n    id: titleId\n  }, title) : null, /*#__PURE__*/React.createElement(\"path\", {\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\",\n    d: \"m14.74 9-.346 9m-4.788 0L9.26 9m9.968-3.21c.342.052.682.107 1.022.166m-1.022-.165L18.16 19.673a2.25 2.25 0 0 1-2.244 2.077H8.084a2.25 2.25 0 0 1-2.244-2.077L4.772 5.79m14.456 0a48.108 48.108 0 0 0-3.478-.397m-12 .562c.34-.059.68-.114 1.022-.165m0 0a48.11 48.11 0 0 1 3.478-.397m7.5 0v-.916c0-1.18-.91-2.164-2.09-2.201a51.964 51.964 0 0 0-3.32 0c-1.18.037-2.09 1.022-2.09 2.201v.916m7.5 0a48.667 48.667 0 0 0-7.5 0\"\n  }));\n}\nconst ForwardRef = /*#__PURE__*/ React.forwardRef(TrashIcon);\nexport default ForwardRef;"], "names": [], "mappings": ";;;AAAA;;AACA,SAAS,UAAU,EACjB,KAAK,EACL,OAAO,EACP,GAAG,OACJ,EAAE,MAAM;IACP,OAAO,WAAW,GAAE,CAAA,GAAA,qMAAA,CAAA,gBAAmB,AAAD,EAAE,OAAO,OAAO,MAAM,CAAC;QAC3D,OAAO;QACP,MAAM;QACN,SAAS;QACT,aAAa;QACb,QAAQ;QACR,eAAe;QACf,aAAa;QACb,KAAK;QACL,mBAAmB;IACrB,GAAG,QAAQ,QAAQ,WAAW,GAAE,CAAA,GAAA,qMAAA,CAAA,gBAAmB,AAAD,EAAE,SAAS;QAC3D,IAAI;IACN,GAAG,SAAS,MAAM,WAAW,GAAE,CAAA,GAAA,qMAAA,CAAA,gBAAmB,AAAD,EAAE,QAAQ;QACzD,eAAe;QACf,gBAAgB;QAChB,GAAG;IACL;AACF;AACA,MAAM,aAAa,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAAE;uCACnC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1071, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Project%20By%20AI/Technoloway/Technoloway%20%28Processing%29/technoloway-simple/node_modules/%40heroicons/react/24/outline/esm/EyeIcon.js"], "sourcesContent": ["import * as React from \"react\";\nfunction EyeIcon({\n  title,\n  titleId,\n  ...props\n}, svgRef) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    xmlns: \"http://www.w3.org/2000/svg\",\n    fill: \"none\",\n    viewBox: \"0 0 24 24\",\n    strokeWidth: 1.5,\n    stroke: \"currentColor\",\n    \"aria-hidden\": \"true\",\n    \"data-slot\": \"icon\",\n    ref: svgRef,\n    \"aria-labelledby\": titleId\n  }, props), title ? /*#__PURE__*/React.createElement(\"title\", {\n    id: titleId\n  }, title) : null, /*#__PURE__*/React.createElement(\"path\", {\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\",\n    d: \"M2.036 12.322a1.012 1.012 0 0 1 0-.639C3.423 7.51 7.36 4.5 12 4.5c4.638 0 8.573 3.007 9.963 7.178.07.207.07.431 0 .639C20.577 16.49 16.64 19.5 12 19.5c-4.638 0-8.573-3.007-9.963-7.178Z\"\n  }), /*#__PURE__*/React.createElement(\"path\", {\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\",\n    d: \"M15 12a3 3 0 1 1-6 0 3 3 0 0 1 6 0Z\"\n  }));\n}\nconst ForwardRef = /*#__PURE__*/ React.forwardRef(EyeIcon);\nexport default ForwardRef;"], "names": [], "mappings": ";;;AAAA;;AACA,SAAS,QAAQ,EACf,KAAK,EACL,OAAO,EACP,GAAG,OACJ,EAAE,MAAM;IACP,OAAO,WAAW,GAAE,CAAA,GAAA,qMAAA,CAAA,gBAAmB,AAAD,EAAE,OAAO,OAAO,MAAM,CAAC;QAC3D,OAAO;QACP,MAAM;QACN,SAAS;QACT,aAAa;QACb,QAAQ;QACR,eAAe;QACf,aAAa;QACb,KAAK;QACL,mBAAmB;IACrB,GAAG,QAAQ,QAAQ,WAAW,GAAE,CAAA,GAAA,qMAAA,CAAA,gBAAmB,AAAD,EAAE,SAAS;QAC3D,IAAI;IACN,GAAG,SAAS,MAAM,WAAW,GAAE,CAAA,GAAA,qMAAA,CAAA,gBAAmB,AAAD,EAAE,QAAQ;QACzD,eAAe;QACf,gBAAgB;QAChB,GAAG;IACL,IAAI,WAAW,GAAE,CAAA,GAAA,qMAAA,CAAA,gBAAmB,AAAD,EAAE,QAAQ;QAC3C,eAAe;QACf,gBAAgB;QAChB,GAAG;IACL;AACF;AACA,MAAM,aAAa,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAAE;uCACnC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1117, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Project%20By%20AI/Technoloway/Technoloway%20%28Processing%29/technoloway-simple/node_modules/%40heroicons/react/24/outline/esm/CheckCircleIcon.js"], "sourcesContent": ["import * as React from \"react\";\nfunction CheckCircleIcon({\n  title,\n  titleId,\n  ...props\n}, svgRef) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    xmlns: \"http://www.w3.org/2000/svg\",\n    fill: \"none\",\n    viewBox: \"0 0 24 24\",\n    strokeWidth: 1.5,\n    stroke: \"currentColor\",\n    \"aria-hidden\": \"true\",\n    \"data-slot\": \"icon\",\n    ref: svgRef,\n    \"aria-labelledby\": titleId\n  }, props), title ? /*#__PURE__*/React.createElement(\"title\", {\n    id: titleId\n  }, title) : null, /*#__PURE__*/React.createElement(\"path\", {\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\",\n    d: \"M9 12.75 11.25 15 15 9.75M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z\"\n  }));\n}\nconst ForwardRef = /*#__PURE__*/ React.forwardRef(CheckCircleIcon);\nexport default ForwardRef;"], "names": [], "mappings": ";;;AAAA;;AACA,SAAS,gBAAgB,EACvB,KAAK,EACL,OAAO,EACP,GAAG,OACJ,EAAE,MAAM;IACP,OAAO,WAAW,GAAE,CAAA,GAAA,qMAAA,CAAA,gBAAmB,AAAD,EAAE,OAAO,OAAO,MAAM,CAAC;QAC3D,OAAO;QACP,MAAM;QACN,SAAS;QACT,aAAa;QACb,QAAQ;QACR,eAAe;QACf,aAAa;QACb,KAAK;QACL,mBAAmB;IACrB,GAAG,QAAQ,QAAQ,WAAW,GAAE,CAAA,GAAA,qMAAA,CAAA,gBAAmB,AAAD,EAAE,SAAS;QAC3D,IAAI;IACN,GAAG,SAAS,MAAM,WAAW,GAAE,CAAA,GAAA,qMAAA,CAAA,gBAAmB,AAAD,EAAE,QAAQ;QACzD,eAAe;QACf,gBAAgB;QAChB,GAAG;IACL;AACF;AACA,MAAM,aAAa,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAAE;uCACnC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1159, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Project%20By%20AI/Technoloway/Technoloway%20%28Processing%29/technoloway-simple/node_modules/%40heroicons/react/24/outline/esm/ClockIcon.js"], "sourcesContent": ["import * as React from \"react\";\nfunction ClockIcon({\n  title,\n  titleId,\n  ...props\n}, svgRef) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    xmlns: \"http://www.w3.org/2000/svg\",\n    fill: \"none\",\n    viewBox: \"0 0 24 24\",\n    strokeWidth: 1.5,\n    stroke: \"currentColor\",\n    \"aria-hidden\": \"true\",\n    \"data-slot\": \"icon\",\n    ref: svgRef,\n    \"aria-labelledby\": titleId\n  }, props), title ? /*#__PURE__*/React.createElement(\"title\", {\n    id: titleId\n  }, title) : null, /*#__PURE__*/React.createElement(\"path\", {\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\",\n    d: \"M12 6v6h4.5m4.5 0a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z\"\n  }));\n}\nconst ForwardRef = /*#__PURE__*/ React.forwardRef(ClockIcon);\nexport default ForwardRef;"], "names": [], "mappings": ";;;AAAA;;AACA,SAAS,UAAU,EACjB,KAAK,EACL,OAAO,EACP,GAAG,OACJ,EAAE,MAAM;IACP,OAAO,WAAW,GAAE,CAAA,GAAA,qMAAA,CAAA,gBAAmB,AAAD,EAAE,OAAO,OAAO,MAAM,CAAC;QAC3D,OAAO;QACP,MAAM;QACN,SAAS;QACT,aAAa;QACb,QAAQ;QACR,eAAe;QACf,aAAa;QACb,KAAK;QACL,mBAAmB;IACrB,GAAG,QAAQ,QAAQ,WAAW,GAAE,CAAA,GAAA,qMAAA,CAAA,gBAAmB,AAAD,EAAE,SAAS;QAC3D,IAAI;IACN,GAAG,SAAS,MAAM,WAAW,GAAE,CAAA,GAAA,qMAAA,CAAA,gBAAmB,AAAD,EAAE,QAAQ;QACzD,eAAe;QACf,gBAAgB;QAChB,GAAG;IACL;AACF;AACA,MAAM,aAAa,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAAE;uCACnC", "ignoreList": [0], "debugId": null}}]}