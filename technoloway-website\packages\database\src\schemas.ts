import { z } from 'zod';

// User schemas
export const createUserSchema = z.object({
  email: z.string().email(),
  clerkId: z.string(),
  firstName: z.string().optional(),
  lastName: z.string().optional(),
  imageUrl: z.string().url().optional(),
});

export const updateUserSchema = z.object({
  firstName: z.string().optional(),
  lastName: z.string().optional(),
  imageUrl: z.string().url().optional(),
});

// Post schemas
export const createPostSchema = z.object({
  title: z.string().min(1).max(200),
  content: z.string().min(1),
  excerpt: z.string().max(500).optional(),
  coverImage: z.string().url().optional(),
  published: z.boolean().default(false),
  categoryId: z.string().optional(),
  tags: z.array(z.string()).optional(),
});

export const updatePostSchema = createPostSchema.partial();

// Category schemas
export const createCategorySchema = z.object({
  name: z.string().min(1).max(100),
  description: z.string().optional(),
});

export const updateCategorySchema = createCategorySchema.partial();

// Project schemas
export const createProjectSchema = z.object({
  title: z.string().min(1).max(200),
  description: z.string().min(1),
  imageUrl: z.string().url().optional(),
  projectUrl: z.string().url().optional(),
  githubUrl: z.string().url().optional(),
  technologies: z.array(z.string()),
  featured: z.boolean().default(false),
});

export const updateProjectSchema = createProjectSchema.partial();

// Contact inquiry schemas
export const createContactInquirySchema = z.object({
  name: z.string().min(1).max(100),
  email: z.string().email(),
  company: z.string().max(100).optional(),
  phone: z.string().max(20).optional(),
  subject: z.string().min(1).max(200),
  message: z.string().min(1).max(2000),
});

export const updateContactInquirySchema = z.object({
  status: z.enum(['PENDING', 'IN_PROGRESS', 'RESOLVED', 'CLOSED']),
  assignedToId: z.string().optional(),
});

// Service schemas
export const createServiceSchema = z.object({
  title: z.string().min(1).max(200),
  description: z.string().min(1),
  icon: z.string().optional(),
  features: z.array(z.string()),
  price: z.string().optional(),
  popular: z.boolean().default(false),
});

export const updateServiceSchema = createServiceSchema.partial();

// Testimonial schemas
export const createTestimonialSchema = z.object({
  name: z.string().min(1).max(100),
  position: z.string().min(1).max(100),
  company: z.string().min(1).max(100),
  content: z.string().min(1).max(1000),
  rating: z.number().min(1).max(5).default(5),
  imageUrl: z.string().url().optional(),
  featured: z.boolean().default(false),
});

export const updateTestimonialSchema = createTestimonialSchema.partial();

// Common schemas
export const paginationSchema = z.object({
  page: z.number().min(1).default(1),
  limit: z.number().min(1).max(100).default(10),
});

export const searchSchema = z.object({
  query: z.string().optional(),
  category: z.string().optional(),
  tags: z.array(z.string()).optional(),
  published: z.boolean().optional(),
});

export type CreateUserInput = z.infer<typeof createUserSchema>;
export type UpdateUserInput = z.infer<typeof updateUserSchema>;
export type CreatePostInput = z.infer<typeof createPostSchema>;
export type UpdatePostInput = z.infer<typeof updatePostSchema>;
export type CreateCategoryInput = z.infer<typeof createCategorySchema>;
export type UpdateCategoryInput = z.infer<typeof updateCategorySchema>;
export type CreateProjectInput = z.infer<typeof createProjectSchema>;
export type UpdateProjectInput = z.infer<typeof updateProjectSchema>;
export type CreateContactInquiryInput = z.infer<typeof createContactInquirySchema>;
export type UpdateContactInquiryInput = z.infer<typeof updateContactInquirySchema>;
export type CreateServiceInput = z.infer<typeof createServiceSchema>;
export type UpdateServiceInput = z.infer<typeof updateServiceSchema>;
export type CreateTestimonialInput = z.infer<typeof createTestimonialSchema>;
export type UpdateTestimonialInput = z.infer<typeof updateTestimonialSchema>;
export type PaginationInput = z.infer<typeof paginationSchema>;
export type SearchInput = z.infer<typeof searchSchema>;
