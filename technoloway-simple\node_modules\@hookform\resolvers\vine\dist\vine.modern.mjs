import{validateFieldsNatively as e,toNestErrors as r}from"@hookform/resolvers";import{errors as s}from"@vinejs/vine";import{appendErrors as t}from"react-hook-form";function o(e,r){const s={};for(;e.length;){const o=e[0],a=o.field;if(a in s||(s[a]={message:o.message,type:o.rule}),r){const{types:e}=s[a],i=e&&e[o.rule];s[a]=t(a,r,s,o.rule,i?[...i,o.message]:o.message)}e.shift()}return s}function a(t,a,i={}){return async(n,l,c)=>{try{const r=await t.validate(n,a);return c.shouldUseNativeValidation&&e({},c),{errors:{},values:i.raw?Object.assign({},n):r}}catch(e){if(e instanceof s.E_VALIDATION_ERROR)return{values:{},errors:r(o(e.messages,!c.shouldUseNativeValidation&&"all"===c.criteriaMode),c)};throw e}}}export{a as vineResolver};
//# sourceMappingURL=vine.modern.mjs.map
