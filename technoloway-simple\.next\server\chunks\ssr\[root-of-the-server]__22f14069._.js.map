{"version": 3, "sources": [], "sections": [{"offset": {"line": 39, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Project%20By%20AI/Technoloway/Technoloway%20%28Processing%29/technoloway-simple/src/components/header.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport Link from 'next/link';\nimport { usePathname } from 'next/navigation';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport { Bars3Icon, XMarkIcon } from '@heroicons/react/24/outline';\n\nconst navigation = [\n  { name: 'Home', href: '/' },\n  { name: 'About', href: '/about' },\n  { name: 'Services', href: '/services' },\n  { name: 'Projects', href: '/projects' },\n  { name: 'Technologies', href: '/technologies' },\n  { name: 'Contact', href: '/contact' },\n];\n\nexport function Header() {\n  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);\n  const pathname = usePathname();\n\n  const isActive = (href: string) => {\n    if (href === '/') {\n      return pathname === '/';\n    }\n    return pathname.startsWith(href);\n  };\n\n  return (\n    <header className=\"fixed inset-x-0 top-0 z-50 bg-white/80 backdrop-blur-md border-b border-gray-200\">\n      <nav className=\"container flex items-center justify-between py-4\" aria-label=\"Global\">\n        <div className=\"flex lg:flex-1\">\n          <Link href=\"/\" className=\"-m-1.5 p-1.5\">\n            <span className=\"sr-only\">Technoloway</span>\n            <div className=\"flex items-center space-x-2\">\n              <div className=\"h-8 w-8 bg-gradient-to-r from-blue-600 to-blue-400 rounded-lg flex items-center justify-center\">\n                <span className=\"text-white font-bold text-sm\">T</span>\n              </div>\n              <span className=\"text-xl font-bold text-gray-900\">Technoloway</span>\n            </div>\n          </Link>\n        </div>\n        \n        <div className=\"flex lg:hidden\">\n          <button\n            type=\"button\"\n            className=\"-m-2.5 inline-flex items-center justify-center rounded-md p-2.5 text-gray-700\"\n            onClick={() => setMobileMenuOpen(true)}\n          >\n            <span className=\"sr-only\">Open main menu</span>\n            <Bars3Icon className=\"h-6 w-6\" aria-hidden=\"true\" />\n          </button>\n        </div>\n        \n        <div className=\"hidden lg:flex lg:gap-x-8\">\n          {navigation.map((item) => (\n            <Link\n              key={item.name}\n              href={item.href}\n              className={`text-sm font-semibold leading-6 transition-colors hover:text-blue-600 ${\n                isActive(item.href) ? 'text-blue-600' : 'text-gray-900'\n              }`}\n            >\n              {item.name}\n            </Link>\n          ))}\n        </div>\n        \n        <div className=\"hidden lg:flex lg:flex-1 lg:justify-end\">\n          <Link href=\"/contact\" className=\"btn-primary\">\n            Get Started\n          </Link>\n        </div>\n      </nav>\n      \n      {/* Mobile menu */}\n      <AnimatePresence>\n        {mobileMenuOpen && (\n          <motion.div\n            initial={{ opacity: 0 }}\n            animate={{ opacity: 1 }}\n            exit={{ opacity: 0 }}\n            className=\"lg:hidden\"\n          >\n            <div className=\"fixed inset-0 z-50\" />\n            <motion.div\n              initial={{ x: '100%' }}\n              animate={{ x: 0 }}\n              exit={{ x: '100%' }}\n              transition={{ type: 'spring', damping: 25, stiffness: 200 }}\n              className=\"fixed inset-y-0 right-0 z-50 w-full overflow-y-auto bg-white px-6 py-6 sm:max-w-sm sm:ring-1 sm:ring-gray-900/10\"\n            >\n              <div className=\"flex items-center justify-between\">\n                <Link href=\"/\" className=\"-m-1.5 p-1.5\">\n                  <span className=\"sr-only\">Technoloway</span>\n                  <div className=\"flex items-center space-x-2\">\n                    <div className=\"h-8 w-8 bg-gradient-to-r from-blue-600 to-blue-400 rounded-lg flex items-center justify-center\">\n                      <span className=\"text-white font-bold text-sm\">T</span>\n                    </div>\n                    <span className=\"text-xl font-bold text-gray-900\">Technoloway</span>\n                  </div>\n                </Link>\n                <button\n                  type=\"button\"\n                  className=\"-m-2.5 rounded-md p-2.5 text-gray-700\"\n                  onClick={() => setMobileMenuOpen(false)}\n                >\n                  <span className=\"sr-only\">Close menu</span>\n                  <XMarkIcon className=\"h-6 w-6\" aria-hidden=\"true\" />\n                </button>\n              </div>\n              <div className=\"mt-6 flow-root\">\n                <div className=\"-my-6 divide-y divide-gray-500/10\">\n                  <div className=\"space-y-2 py-6\">\n                    {navigation.map((item) => (\n                      <Link\n                        key={item.name}\n                        href={item.href}\n                        className={`-mx-3 block rounded-lg px-3 py-2 text-base font-semibold leading-7 hover:bg-gray-50 ${\n                          isActive(item.href) ? 'text-blue-600' : 'text-gray-900'\n                        }`}\n                        onClick={() => setMobileMenuOpen(false)}\n                      >\n                        {item.name}\n                      </Link>\n                    ))}\n                  </div>\n                  <div className=\"py-6\">\n                    <Link\n                      href=\"/contact\"\n                      className=\"-mx-3 block rounded-lg px-3 py-2.5 text-base font-semibold leading-7 text-blue-600 hover:bg-gray-50\"\n                      onClick={() => setMobileMenuOpen(false)}\n                    >\n                      Get Started\n                    </Link>\n                  </div>\n                </div>\n              </div>\n            </motion.div>\n          </motion.div>\n        )}\n      </AnimatePresence>\n    </header>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AACA;AAAA;AANA;;;;;;;AAQA,MAAM,aAAa;IACjB;QAAE,MAAM;QAAQ,MAAM;IAAI;IAC1B;QAAE,MAAM;QAAS,MAAM;IAAS;IAChC;QAAE,MAAM;QAAY,MAAM;IAAY;IACtC;QAAE,MAAM;QAAY,MAAM;IAAY;IACtC;QAAE,MAAM;QAAgB,MAAM;IAAgB;IAC9C;QAAE,MAAM;QAAW,MAAM;IAAW;CACrC;AAEM,SAAS;IACd,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,WAAW,CAAA,GAAA,kIAAA,CAAA,cAAW,AAAD;IAE3B,MAAM,WAAW,CAAC;QAChB,IAAI,SAAS,KAAK;YAChB,OAAO,aAAa;QACtB;QACA,OAAO,SAAS,UAAU,CAAC;IAC7B;IAEA,qBACE,8OAAC;QAAO,WAAU;;0BAChB,8OAAC;gBAAI,WAAU;gBAAmD,cAAW;;kCAC3E,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC,4JAAA,CAAA,UAAI;4BAAC,MAAK;4BAAI,WAAU;;8CACvB,8OAAC;oCAAK,WAAU;8CAAU;;;;;;8CAC1B,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAK,WAAU;0DAA+B;;;;;;;;;;;sDAEjD,8OAAC;4CAAK,WAAU;sDAAkC;;;;;;;;;;;;;;;;;;;;;;;kCAKxD,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BACC,MAAK;4BACL,WAAU;4BACV,SAAS,IAAM,kBAAkB;;8CAEjC,8OAAC;oCAAK,WAAU;8CAAU;;;;;;8CAC1B,8OAAC,iNAAA,CAAA,YAAS;oCAAC,WAAU;oCAAU,eAAY;;;;;;;;;;;;;;;;;kCAI/C,8OAAC;wBAAI,WAAU;kCACZ,WAAW,GAAG,CAAC,CAAC,qBACf,8OAAC,4JAAA,CAAA,UAAI;gCAEH,MAAM,KAAK,IAAI;gCACf,WAAW,CAAC,sEAAsE,EAChF,SAAS,KAAK,IAAI,IAAI,kBAAkB,iBACxC;0CAED,KAAK,IAAI;+BANL,KAAK,IAAI;;;;;;;;;;kCAWpB,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC,4JAAA,CAAA,UAAI;4BAAC,MAAK;4BAAW,WAAU;sCAAc;;;;;;;;;;;;;;;;;0BAOlD,8OAAC,yLAAA,CAAA,kBAAe;0BACb,gCACC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;oBAAE;oBACtB,SAAS;wBAAE,SAAS;oBAAE;oBACtB,MAAM;wBAAE,SAAS;oBAAE;oBACnB,WAAU;;sCAEV,8OAAC;4BAAI,WAAU;;;;;;sCACf,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,SAAS;gCAAE,GAAG;4BAAO;4BACrB,SAAS;gCAAE,GAAG;4BAAE;4BAChB,MAAM;gCAAE,GAAG;4BAAO;4BAClB,YAAY;gCAAE,MAAM;gCAAU,SAAS;gCAAI,WAAW;4BAAI;4BAC1D,WAAU;;8CAEV,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,4JAAA,CAAA,UAAI;4CAAC,MAAK;4CAAI,WAAU;;8DACvB,8OAAC;oDAAK,WAAU;8DAAU;;;;;;8DAC1B,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;sEACb,cAAA,8OAAC;gEAAK,WAAU;0EAA+B;;;;;;;;;;;sEAEjD,8OAAC;4DAAK,WAAU;sEAAkC;;;;;;;;;;;;;;;;;;sDAGtD,8OAAC;4CACC,MAAK;4CACL,WAAU;4CACV,SAAS,IAAM,kBAAkB;;8DAEjC,8OAAC;oDAAK,WAAU;8DAAU;;;;;;8DAC1B,8OAAC,iNAAA,CAAA,YAAS;oDAAC,WAAU;oDAAU,eAAY;;;;;;;;;;;;;;;;;;8CAG/C,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;0DACZ,WAAW,GAAG,CAAC,CAAC,qBACf,8OAAC,4JAAA,CAAA,UAAI;wDAEH,MAAM,KAAK,IAAI;wDACf,WAAW,CAAC,oFAAoF,EAC9F,SAAS,KAAK,IAAI,IAAI,kBAAkB,iBACxC;wDACF,SAAS,IAAM,kBAAkB;kEAEhC,KAAK,IAAI;uDAPL,KAAK,IAAI;;;;;;;;;;0DAWpB,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC,4JAAA,CAAA,UAAI;oDACH,MAAK;oDACL,WAAU;oDACV,SAAS,IAAM,kBAAkB;8DAClC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAYrB", "debugId": null}}, {"offset": {"line": 431, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Project%20By%20AI/Technoloway/Technoloway%20%28Processing%29/technoloway-simple/src/components/footer.tsx"], "sourcesContent": ["import Link from 'next/link';\n\nexport function Footer() {\n  return (\n    <footer className=\"bg-gray-900 text-white\">\n      <div className=\"container py-16\">\n        <div className=\"grid grid-cols-1 md:grid-cols-4 gap-8\">\n          <div className=\"col-span-1 md:col-span-2\">\n            <div className=\"flex items-center space-x-2 mb-6\">\n              <div className=\"h-8 w-8 bg-gradient-to-r from-blue-600 to-blue-400 rounded-lg flex items-center justify-center\">\n                <span className=\"text-white font-bold text-sm\">T</span>\n              </div>\n              <span className=\"text-xl font-bold\">Technoloway</span>\n            </div>\n            <p className=\"text-gray-300 mb-6 max-w-md\">\n              Transforming ideas into digital reality. We build exceptional software \n              solutions that drive business growth and innovation.\n            </p>\n            <div className=\"space-y-2\">\n              <p className=\"text-gray-300\"><EMAIL></p>\n              <p className=\"text-gray-300\">+****************</p>\n              <p className=\"text-gray-300\">San Francisco, CA</p>\n            </div>\n          </div>\n          \n          <div>\n            <h3 className=\"text-lg font-semibold mb-4\">Services</h3>\n            <ul className=\"space-y-2 text-gray-300\">\n              <li><Link href=\"/#services\" className=\"hover:text-white transition-colors\">Web Development</Link></li>\n              <li><Link href=\"/#services\" className=\"hover:text-white transition-colors\">Mobile Apps</Link></li>\n              <li><Link href=\"/#services\" className=\"hover:text-white transition-colors\">Cloud Solutions</Link></li>\n              <li><Link href=\"/#services\" className=\"hover:text-white transition-colors\">API Development</Link></li>\n            </ul>\n          </div>\n          \n          <div>\n            <h3 className=\"text-lg font-semibold mb-4\">Company</h3>\n            <ul className=\"space-y-2 text-gray-300\">\n              <li><Link href=\"/team\" className=\"hover:text-white transition-colors\">About</Link></li>\n              <li><Link href=\"/team\" className=\"hover:text-white transition-colors\">Team</Link></li>\n              <li><Link href=\"/portfolio\" className=\"hover:text-white transition-colors\">Portfolio</Link></li>\n              <li><Link href=\"/blog\" className=\"hover:text-white transition-colors\">Blog</Link></li>\n              <li><Link href=\"/#contact\" className=\"hover:text-white transition-colors\">Contact</Link></li>\n            </ul>\n          </div>\n        </div>\n        \n        <div className=\"border-t border-gray-700 mt-12 pt-8 text-center\">\n          <p className=\"text-gray-400\">\n            &copy; {new Date().getFullYear()} Technoloway. All rights reserved.\n          </p>\n        </div>\n      </div>\n    </footer>\n  );\n}\n"], "names": [], "mappings": ";;;;AAAA;;;AAEO,SAAS;IACd,qBACE,8OAAC;QAAO,WAAU;kBAChB,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAK,WAAU;0DAA+B;;;;;;;;;;;sDAEjD,8OAAC;4CAAK,WAAU;sDAAoB;;;;;;;;;;;;8CAEtC,8OAAC;oCAAE,WAAU;8CAA8B;;;;;;8CAI3C,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAE,WAAU;sDAAgB;;;;;;sDAC7B,8OAAC;4CAAE,WAAU;sDAAgB;;;;;;sDAC7B,8OAAC;4CAAE,WAAU;sDAAgB;;;;;;;;;;;;;;;;;;sCAIjC,8OAAC;;8CACC,8OAAC;oCAAG,WAAU;8CAA6B;;;;;;8CAC3C,8OAAC;oCAAG,WAAU;;sDACZ,8OAAC;sDAAG,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAa,WAAU;0DAAqC;;;;;;;;;;;sDAC3E,8OAAC;sDAAG,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAa,WAAU;0DAAqC;;;;;;;;;;;sDAC3E,8OAAC;sDAAG,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAa,WAAU;0DAAqC;;;;;;;;;;;sDAC3E,8OAAC;sDAAG,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAa,WAAU;0DAAqC;;;;;;;;;;;;;;;;;;;;;;;sCAI/E,8OAAC;;8CACC,8OAAC;oCAAG,WAAU;8CAA6B;;;;;;8CAC3C,8OAAC;oCAAG,WAAU;;sDACZ,8OAAC;sDAAG,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAQ,WAAU;0DAAqC;;;;;;;;;;;sDACtE,8OAAC;sDAAG,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAQ,WAAU;0DAAqC;;;;;;;;;;;sDACtE,8OAAC;sDAAG,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAa,WAAU;0DAAqC;;;;;;;;;;;sDAC3E,8OAAC;sDAAG,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAQ,WAAU;0DAAqC;;;;;;;;;;;sDACtE,8OAAC;sDAAG,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAY,WAAU;0DAAqC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAKhF,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAE,WAAU;;4BAAgB;4BACnB,IAAI,OAAO,WAAW;4BAAG;;;;;;;;;;;;;;;;;;;;;;;AAM7C", "debugId": null}}, {"offset": {"line": 757, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Project%20By%20AI/Technoloway/Technoloway%20%28Processing%29/technoloway-simple/src/app/projects/page.tsx"], "sourcesContent": ["'use client';\n\nimport Link from 'next/link';\nimport { motion } from 'framer-motion';\nimport {\n  MagnifyingGlassIcon,\n  FunnelIcon,\n  ArrowTopRightOnSquareIcon,\n  CalendarIcon,\n  UserGroupIcon,\n  CurrencyDollarIcon,\n} from '@heroicons/react/24/outline';\nimport { Header } from '@/components/header';\nimport { Footer } from '@/components/footer';\nimport { useState } from 'react';\n\nconst projects = [\n  {\n    id: 'ecommerce-platform',\n    title: 'EcoCommerce Platform',\n    description: 'A comprehensive e-commerce platform for sustainable products with advanced filtering, payment integration, and inventory management.',\n    shortDescription: 'Sustainable e-commerce platform',\n    image: 'https://images.unsplash.com/photo-1556742049-0cfed4f6a45d?w=800&h=600&fit=crop',\n    category: 'E-commerce',\n    technologies: ['Next.js', 'TypeScript', 'Stripe', 'PostgreSQL', 'Tailwind CSS'],\n    client: 'GreenTech Solutions',\n    duration: '6 months',\n    teamSize: 5,\n    budget: '$45,000',\n    status: 'Completed',\n    completedAt: '2024-01-15',\n    features: [\n      'Product catalog with advanced filtering',\n      'Secure payment processing',\n      'Inventory management system',\n      'Customer reviews and ratings',\n      'Admin dashboard',\n      'Mobile-responsive design'\n    ],\n    results: [\n      '200% increase in online sales',\n      '85% improvement in user engagement',\n      '50% reduction in cart abandonment',\n      '99.9% uptime achieved'\n    ]\n  },\n  {\n    id: 'healthcare-app',\n    title: 'HealthTracker Mobile App',\n    description: 'A comprehensive health tracking mobile application with real-time monitoring, doctor consultations, and personalized health insights.',\n    shortDescription: 'Health monitoring mobile app',\n    image: 'https://images.unsplash.com/photo-1576091160399-112ba8d25d1f?w=800&h=600&fit=crop',\n    category: 'Healthcare',\n    technologies: ['React Native', 'Node.js', 'MongoDB', 'Firebase', 'Socket.io'],\n    client: 'MedTech Innovations',\n    duration: '8 months',\n    teamSize: 6,\n    budget: '$65,000',\n    status: 'Completed',\n    completedAt: '2024-02-20',\n    features: [\n      'Real-time health monitoring',\n      'Video consultations with doctors',\n      'Medication reminders',\n      'Health data analytics',\n      'Emergency contact system',\n      'Wearable device integration'\n    ],\n    results: [\n      '150% increase in patient engagement',\n      '40% reduction in missed appointments',\n      '95% user satisfaction rate',\n      '500K+ app downloads'\n    ]\n  },\n  {\n    id: 'fintech-dashboard',\n    title: 'Financial Analytics Dashboard',\n    description: 'A sophisticated financial analytics platform providing real-time insights, risk assessment, and portfolio management for investment firms.',\n    shortDescription: 'Financial analytics platform',\n    image: 'https://images.unsplash.com/photo-**********-bebda4e38f71?w=800&h=600&fit=crop',\n    category: 'Fintech',\n    technologies: ['React', 'Python', 'PostgreSQL', 'Redis', 'Chart.js'],\n    client: 'FinanceFlow Corp',\n    duration: '10 months',\n    teamSize: 8,\n    budget: '$85,000',\n    status: 'Completed',\n    completedAt: '2023-12-10',\n    features: [\n      'Real-time market data integration',\n      'Risk assessment algorithms',\n      'Portfolio optimization tools',\n      'Automated reporting',\n      'Multi-currency support',\n      'Advanced data visualization'\n    ],\n    results: [\n      '300% faster data processing',\n      '60% improvement in decision making',\n      '25% increase in portfolio returns',\n      '99.99% data accuracy'\n    ]\n  },\n  {\n    id: 'education-platform',\n    title: 'EduLearn Online Platform',\n    description: 'An interactive online learning platform with video courses, live sessions, progress tracking, and certification management.',\n    shortDescription: 'Online education platform',\n    image: 'https://images.unsplash.com/photo-1501504905252-473c47e087f8?w=800&h=600&fit=crop',\n    category: 'Education',\n    technologies: ['Vue.js', 'Laravel', 'MySQL', 'WebRTC', 'AWS'],\n    client: 'EduTech Academy',\n    duration: '7 months',\n    teamSize: 7,\n    budget: '$55,000',\n    status: 'In Progress',\n    completedAt: null,\n    features: [\n      'Interactive video courses',\n      'Live virtual classrooms',\n      'Progress tracking and analytics',\n      'Certification management',\n      'Discussion forums',\n      'Mobile learning app'\n    ],\n    results: [\n      '80% completion rate improvement',\n      '200% increase in student enrollment',\n      '90% student satisfaction score',\n      'Reduced operational costs by 40%'\n    ]\n  },\n  {\n    id: 'logistics-system',\n    title: 'Smart Logistics Management',\n    description: 'An intelligent logistics management system with route optimization, real-time tracking, and automated scheduling for delivery companies.',\n    shortDescription: 'Logistics management system',\n    image: 'https://images.unsplash.com/photo-1586528116311-ad8dd3c8310d?w=800&h=600&fit=crop',\n    category: 'Logistics',\n    technologies: ['Angular', 'Spring Boot', 'PostgreSQL', 'Google Maps API', 'Docker'],\n    client: 'LogiFlow Solutions',\n    duration: '9 months',\n    teamSize: 6,\n    budget: '$70,000',\n    status: 'Completed',\n    completedAt: '2023-11-30',\n    features: [\n      'Route optimization algorithms',\n      'Real-time GPS tracking',\n      'Automated scheduling',\n      'Inventory management',\n      'Customer notifications',\n      'Performance analytics'\n    ],\n    results: [\n      '35% reduction in delivery time',\n      '25% decrease in fuel costs',\n      '95% on-time delivery rate',\n      '50% improvement in customer satisfaction'\n    ]\n  },\n  {\n    id: 'social-platform',\n    title: 'ConnectPro Social Network',\n    description: 'A professional networking platform with advanced matching algorithms, video conferencing, and collaboration tools for industry professionals.',\n    shortDescription: 'Professional networking platform',\n    image: 'https://images.unsplash.com/photo-1522202176988-66273c2fd55f?w=800&h=600&fit=crop',\n    category: 'Social Media',\n    technologies: ['React', 'GraphQL', 'Node.js', 'MongoDB', 'WebRTC'],\n    client: 'NetworkTech Inc',\n    duration: '12 months',\n    teamSize: 10,\n    budget: '$95,000',\n    status: 'In Progress',\n    completedAt: null,\n    features: [\n      'Professional profile management',\n      'Advanced matching algorithms',\n      'Video conferencing integration',\n      'Collaboration workspaces',\n      'Industry-specific groups',\n      'Event management system'\n    ],\n    results: [\n      '100K+ registered professionals',\n      '75% monthly active users',\n      '500+ successful connections daily',\n      '4.8/5 user rating'\n    ]\n  }\n];\n\nconst categories = ['All', 'E-commerce', 'Healthcare', 'Fintech', 'Education', 'Logistics', 'Social Media'];\nconst statuses = ['All', 'Completed', 'In Progress'];\n\nexport default function ProjectsPage() {\n  const [searchTerm, setSearchTerm] = useState('');\n  const [selectedCategory, setSelectedCategory] = useState('All');\n  const [selectedStatus, setSelectedStatus] = useState('All');\n\n  const filteredProjects = projects.filter(project => {\n    const matchesSearch = project.title.toLowerCase().includes(searchTerm.toLowerCase()) ||\n                         project.description.toLowerCase().includes(searchTerm.toLowerCase()) ||\n                         project.client.toLowerCase().includes(searchTerm.toLowerCase());\n    const matchesCategory = selectedCategory === 'All' || project.category === selectedCategory;\n    const matchesStatus = selectedStatus === 'All' || project.status === selectedStatus;\n    \n    return matchesSearch && matchesCategory && matchesStatus;\n  });\n\n  const formatDate = (dateString: string | null) => {\n    if (!dateString) return 'Ongoing';\n    return new Date(dateString).toLocaleDateString('en-US', {\n      year: 'numeric',\n      month: 'long',\n      day: 'numeric',\n    });\n  };\n\n  const getStatusColor = (status: string) => {\n    switch (status) {\n      case 'Completed': return 'bg-green-100 text-green-800';\n      case 'In Progress': return 'bg-blue-100 text-blue-800';\n      default: return 'bg-gray-100 text-gray-800';\n    }\n  };\n\n  return (\n    <div className=\"min-h-screen bg-white\">\n      <Header />\n\n      <main className=\"pt-20\">\n        {/* Hero Section */}\n        <section className=\"relative py-24 bg-gradient-to-br from-gray-50 via-white to-gray-100\">\n          <div className=\"container\">\n            <motion.div\n              initial={{ opacity: 0, y: 20 }}\n              animate={{ opacity: 1, y: 0 }}\n              transition={{ duration: 0.6 }}\n              className=\"text-center max-w-4xl mx-auto\"\n            >\n              <motion.h1\n                initial={{ opacity: 0, y: 20 }}\n                animate={{ opacity: 1, y: 0 }}\n                transition={{ duration: 0.6, delay: 0.1 }}\n                className=\"text-4xl font-bold tracking-tight text-gray-900 sm:text-6xl lg:text-7xl\"\n              >\n                Our <span className=\"gradient-text\">Portfolio</span>\n              </motion.h1>\n\n              <motion.p\n                initial={{ opacity: 0, y: 20 }}\n                animate={{ opacity: 1, y: 0 }}\n                transition={{ duration: 0.6, delay: 0.2 }}\n                className=\"mt-6 text-lg leading-8 text-gray-600 sm:text-xl lg:text-2xl\"\n              >\n                Explore our successful projects and see how we've helped businesses\n                transform their ideas into powerful digital solutions.\n              </motion.p>\n            </motion.div>\n          </div>\n        </section>\n\n        {/* Stats Section */}\n        <section className=\"py-16 bg-white\">\n          <div className=\"container\">\n            <div className=\"grid grid-cols-2 gap-8 md:grid-cols-4\">\n              {[\n                { label: 'Projects Completed', value: '500+' },\n                { label: 'Happy Clients', value: '200+' },\n                { label: 'Success Rate', value: '98%' },\n                { label: 'Years Experience', value: '10+' }\n              ].map((stat, index) => (\n                <motion.div\n                  key={stat.label}\n                  initial={{ opacity: 0, scale: 0.8 }}\n                  whileInView={{ opacity: 1, scale: 1 }}\n                  transition={{ duration: 0.5, delay: index * 0.1 }}\n                  viewport={{ once: true }}\n                  className=\"text-center\"\n                >\n                  <div className=\"text-3xl font-bold text-blue-600 sm:text-4xl\">\n                    {stat.value}\n                  </div>\n                  <div className=\"mt-2 text-sm text-gray-600\">\n                    {stat.label}\n                  </div>\n                </motion.div>\n              ))}\n            </div>\n          </div>\n        </section>\n\n        {/* Filters Section */}\n        <section className=\"py-8 bg-gray-50\">\n          <div className=\"container\">\n            <div className=\"flex flex-col lg:flex-row gap-4 items-center justify-between\">\n              {/* Search */}\n              <div className=\"relative flex-1 max-w-md\">\n                <div className=\"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\">\n                  <MagnifyingGlassIcon className=\"h-5 w-5 text-gray-400\" />\n                </div>\n                <input\n                  type=\"text\"\n                  placeholder=\"Search projects...\"\n                  value={searchTerm}\n                  onChange={(e) => setSearchTerm(e.target.value)}\n                  className=\"block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-blue-500 focus:border-blue-500 sm:text-sm\"\n                />\n              </div>\n\n              {/* Filters */}\n              <div className=\"flex gap-4\">\n                <select\n                  value={selectedCategory}\n                  onChange={(e) => setSelectedCategory(e.target.value)}\n                  className=\"block pl-3 pr-10 py-2 text-base border border-gray-300 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm rounded-md\"\n                >\n                  {categories.map(category => (\n                    <option key={category} value={category}>{category}</option>\n                  ))}\n                </select>\n\n                <select\n                  value={selectedStatus}\n                  onChange={(e) => setSelectedStatus(e.target.value)}\n                  className=\"block pl-3 pr-10 py-2 text-base border border-gray-300 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm rounded-md\"\n                >\n                  {statuses.map(status => (\n                    <option key={status} value={status}>{status}</option>\n                  ))}\n                </select>\n              </div>\n            </div>\n          </div>\n        </section>\n\n        {/* Projects Grid */}\n        <section className=\"py-16 bg-white\">\n          <div className=\"container\">\n            <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8\">\n              {filteredProjects.map((project, index) => (\n                <motion.div\n                  key={project.id}\n                  initial={{ opacity: 0, y: 20 }}\n                  whileInView={{ opacity: 1, y: 0 }}\n                  transition={{ duration: 0.6, delay: index * 0.1 }}\n                  viewport={{ once: true }}\n                  className=\"group bg-white rounded-2xl border border-gray-200 overflow-hidden hover:shadow-xl transition-all duration-300\"\n                >\n                  <div className=\"relative overflow-hidden\">\n                    <img\n                      src={project.image}\n                      alt={project.title}\n                      className=\"w-full h-48 object-cover group-hover:scale-105 transition-transform duration-300\"\n                    />\n                    <div className=\"absolute top-4 left-4\">\n                      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(project.status)}`}>\n                        {project.status}\n                      </span>\n                    </div>\n                    <div className=\"absolute top-4 right-4\">\n                      <span className=\"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-white/90 text-gray-800\">\n                        {project.category}\n                      </span>\n                    </div>\n                  </div>\n\n                  <div className=\"p-6\">\n                    <h3 className=\"text-xl font-semibold text-gray-900 mb-2 group-hover:text-blue-600 transition-colors\">\n                      {project.title}\n                    </h3>\n                    \n                    <p className=\"text-gray-600 mb-4 line-clamp-2\">\n                      {project.shortDescription}\n                    </p>\n\n                    <div className=\"space-y-2 mb-4 text-sm text-gray-500\">\n                      <div className=\"flex items-center\">\n                        <UserGroupIcon className=\"w-4 h-4 mr-2\" />\n                        Client: {project.client}\n                      </div>\n                      <div className=\"flex items-center\">\n                        <CalendarIcon className=\"w-4 h-4 mr-2\" />\n                        Duration: {project.duration}\n                      </div>\n                      <div className=\"flex items-center\">\n                        <CurrencyDollarIcon className=\"w-4 h-4 mr-2\" />\n                        Budget: {project.budget}\n                      </div>\n                    </div>\n\n                    <div className=\"mb-4\">\n                      <div className=\"flex flex-wrap gap-1\">\n                        {project.technologies.slice(0, 3).map((tech) => (\n                          <span\n                            key={tech}\n                            className=\"inline-flex items-center px-2 py-1 rounded text-xs font-medium bg-gray-100 text-gray-800\"\n                          >\n                            {tech}\n                          </span>\n                        ))}\n                        {project.technologies.length > 3 && (\n                          <span className=\"inline-flex items-center px-2 py-1 rounded text-xs font-medium bg-gray-100 text-gray-800\">\n                            +{project.technologies.length - 3}\n                          </span>\n                        )}\n                      </div>\n                    </div>\n\n                    <div className=\"flex items-center justify-between\">\n                      <span className=\"text-sm text-gray-500\">\n                        {project.status === 'Completed' ? 'Completed' : 'In Progress'}\n                      </span>\n                      <Link\n                        href={`/projects/${project.id}`}\n                        className=\"inline-flex items-center text-blue-600 hover:text-blue-700 font-medium group/link\"\n                      >\n                        View Details\n                        <ArrowTopRightOnSquareIcon className=\"ml-1 h-4 w-4 transition-transform group-hover/link:translate-x-0.5 group-hover/link:-translate-y-0.5\" />\n                      </Link>\n                    </div>\n                  </div>\n                </motion.div>\n              ))}\n            </div>\n\n            {/* Empty State */}\n            {filteredProjects.length === 0 && (\n              <div className=\"text-center py-12\">\n                <div className=\"text-gray-400 mb-4\">\n                  <FunnelIcon className=\"mx-auto h-12 w-12\" />\n                </div>\n                <h3 className=\"text-lg font-medium text-gray-900 mb-2\">No projects found</h3>\n                <p className=\"text-gray-600\">\n                  Try adjusting your search criteria or filters.\n                </p>\n              </div>\n            )}\n          </div>\n        </section>\n\n        {/* CTA Section */}\n        <section className=\"py-24 bg-gradient-to-r from-blue-600 to-purple-600\">\n          <div className=\"container\">\n            <motion.div\n              initial={{ opacity: 0, y: 20 }}\n              whileInView={{ opacity: 1, y: 0 }}\n              transition={{ duration: 0.6 }}\n              viewport={{ once: true }}\n              className=\"text-center\"\n            >\n              <h2 className=\"text-3xl font-bold tracking-tight text-white sm:text-4xl\">\n                Ready to Start Your Project?\n              </h2>\n              <p className=\"mt-4 text-lg text-blue-100 max-w-3xl mx-auto\">\n                Join our portfolio of successful projects. Let's discuss your requirements\n                and create something amazing together.\n              </p>\n              <div className=\"mt-8 flex flex-col sm:flex-row gap-4 justify-center\">\n                <Link\n                  href=\"/contact\"\n                  className=\"inline-flex items-center px-8 py-3 border border-transparent text-base font-medium rounded-md text-blue-600 bg-white hover:bg-gray-50 transition-colors\"\n                >\n                  Start Your Project\n                </Link>\n                <Link\n                  href=\"/services\"\n                  className=\"inline-flex items-center px-8 py-3 border-2 border-white text-base font-medium rounded-md text-white hover:bg-white hover:text-blue-600 transition-colors\"\n                >\n                  View Our Services\n                </Link>\n              </div>\n            </motion.div>\n          </div>\n        </section>\n      </main>\n\n      <Footer />\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAQA;AACA;AACA;AAdA;;;;;;;;AAgBA,MAAM,WAAW;IACf;QACE,IAAI;QACJ,OAAO;QACP,aAAa;QACb,kBAAkB;QAClB,OAAO;QACP,UAAU;QACV,cAAc;YAAC;YAAW;YAAc;YAAU;YAAc;SAAe;QAC/E,QAAQ;QACR,UAAU;QACV,UAAU;QACV,QAAQ;QACR,QAAQ;QACR,aAAa;QACb,UAAU;YACR;YACA;YACA;YACA;YACA;YACA;SACD;QACD,SAAS;YACP;YACA;YACA;YACA;SACD;IACH;IACA;QACE,IAAI;QACJ,OAAO;QACP,aAAa;QACb,kBAAkB;QAClB,OAAO;QACP,UAAU;QACV,cAAc;YAAC;YAAgB;YAAW;YAAW;YAAY;SAAY;QAC7E,QAAQ;QACR,UAAU;QACV,UAAU;QACV,QAAQ;QACR,QAAQ;QACR,aAAa;QACb,UAAU;YACR;YACA;YACA;YACA;YACA;YACA;SACD;QACD,SAAS;YACP;YACA;YACA;YACA;SACD;IACH;IACA;QACE,IAAI;QACJ,OAAO;QACP,aAAa;QACb,kBAAkB;QAClB,OAAO;QACP,UAAU;QACV,cAAc;YAAC;YAAS;YAAU;YAAc;YAAS;SAAW;QACpE,QAAQ;QACR,UAAU;QACV,UAAU;QACV,QAAQ;QACR,QAAQ;QACR,aAAa;QACb,UAAU;YACR;YACA;YACA;YACA;YACA;YACA;SACD;QACD,SAAS;YACP;YACA;YACA;YACA;SACD;IACH;IACA;QACE,IAAI;QACJ,OAAO;QACP,aAAa;QACb,kBAAkB;QAClB,OAAO;QACP,UAAU;QACV,cAAc;YAAC;YAAU;YAAW;YAAS;YAAU;SAAM;QAC7D,QAAQ;QACR,UAAU;QACV,UAAU;QACV,QAAQ;QACR,QAAQ;QACR,aAAa;QACb,UAAU;YACR;YACA;YACA;YACA;YACA;YACA;SACD;QACD,SAAS;YACP;YACA;YACA;YACA;SACD;IACH;IACA;QACE,IAAI;QACJ,OAAO;QACP,aAAa;QACb,kBAAkB;QAClB,OAAO;QACP,UAAU;QACV,cAAc;YAAC;YAAW;YAAe;YAAc;YAAmB;SAAS;QACnF,QAAQ;QACR,UAAU;QACV,UAAU;QACV,QAAQ;QACR,QAAQ;QACR,aAAa;QACb,UAAU;YACR;YACA;YACA;YACA;YACA;YACA;SACD;QACD,SAAS;YACP;YACA;YACA;YACA;SACD;IACH;IACA;QACE,IAAI;QACJ,OAAO;QACP,aAAa;QACb,kBAAkB;QAClB,OAAO;QACP,UAAU;QACV,cAAc;YAAC;YAAS;YAAW;YAAW;YAAW;SAAS;QAClE,QAAQ;QACR,UAAU;QACV,UAAU;QACV,QAAQ;QACR,QAAQ;QACR,aAAa;QACb,UAAU;YACR;YACA;YACA;YACA;YACA;YACA;SACD;QACD,SAAS;YACP;YACA;YACA;YACA;SACD;IACH;CACD;AAED,MAAM,aAAa;IAAC;IAAO;IAAc;IAAc;IAAW;IAAa;IAAa;CAAe;AAC3G,MAAM,WAAW;IAAC;IAAO;IAAa;CAAc;AAErC,SAAS;IACtB,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAErD,MAAM,mBAAmB,SAAS,MAAM,CAAC,CAAA;QACvC,MAAM,gBAAgB,QAAQ,KAAK,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW,OAC5D,QAAQ,WAAW,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW,OACjE,QAAQ,MAAM,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW;QACjF,MAAM,kBAAkB,qBAAqB,SAAS,QAAQ,QAAQ,KAAK;QAC3E,MAAM,gBAAgB,mBAAmB,SAAS,QAAQ,MAAM,KAAK;QAErE,OAAO,iBAAiB,mBAAmB;IAC7C;IAEA,MAAM,aAAa,CAAC;QAClB,IAAI,CAAC,YAAY,OAAO;QACxB,OAAO,IAAI,KAAK,YAAY,kBAAkB,CAAC,SAAS;YACtD,MAAM;YACN,OAAO;YACP,KAAK;QACP;IACF;IAEA,MAAM,iBAAiB,CAAC;QACtB,OAAQ;YACN,KAAK;gBAAa,OAAO;YACzB,KAAK;gBAAe,OAAO;YAC3B;gBAAS,OAAO;QAClB;IACF;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC,4HAAA,CAAA,SAAM;;;;;0BAEP,8OAAC;gBAAK,WAAU;;kCAEd,8OAAC;wBAAQ,WAAU;kCACjB,cAAA,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gCACT,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAG;gCAC7B,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAE;gCAC5B,YAAY;oCAAE,UAAU;gCAAI;gCAC5B,WAAU;;kDAEV,8OAAC,0LAAA,CAAA,SAAM,CAAC,EAAE;wCACR,SAAS;4CAAE,SAAS;4CAAG,GAAG;wCAAG;wCAC7B,SAAS;4CAAE,SAAS;4CAAG,GAAG;wCAAE;wCAC5B,YAAY;4CAAE,UAAU;4CAAK,OAAO;wCAAI;wCACxC,WAAU;;4CACX;0DACK,8OAAC;gDAAK,WAAU;0DAAgB;;;;;;;;;;;;kDAGtC,8OAAC,0LAAA,CAAA,SAAM,CAAC,CAAC;wCACP,SAAS;4CAAE,SAAS;4CAAG,GAAG;wCAAG;wCAC7B,SAAS;4CAAE,SAAS;4CAAG,GAAG;wCAAE;wCAC5B,YAAY;4CAAE,UAAU;4CAAK,OAAO;wCAAI;wCACxC,WAAU;kDACX;;;;;;;;;;;;;;;;;;;;;;kCASP,8OAAC;wBAAQ,WAAU;kCACjB,cAAA,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;0CACZ;oCACC;wCAAE,OAAO;wCAAsB,OAAO;oCAAO;oCAC7C;wCAAE,OAAO;wCAAiB,OAAO;oCAAO;oCACxC;wCAAE,OAAO;wCAAgB,OAAO;oCAAM;oCACtC;wCAAE,OAAO;wCAAoB,OAAO;oCAAM;iCAC3C,CAAC,GAAG,CAAC,CAAC,MAAM,sBACX,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wCAET,SAAS;4CAAE,SAAS;4CAAG,OAAO;wCAAI;wCAClC,aAAa;4CAAE,SAAS;4CAAG,OAAO;wCAAE;wCACpC,YAAY;4CAAE,UAAU;4CAAK,OAAO,QAAQ;wCAAI;wCAChD,UAAU;4CAAE,MAAM;wCAAK;wCACvB,WAAU;;0DAEV,8OAAC;gDAAI,WAAU;0DACZ,KAAK,KAAK;;;;;;0DAEb,8OAAC;gDAAI,WAAU;0DACZ,KAAK,KAAK;;;;;;;uCAXR,KAAK,KAAK;;;;;;;;;;;;;;;;;;;;kCAoBzB,8OAAC;wBAAQ,WAAU;kCACjB,cAAA,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;;kDAEb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC,qOAAA,CAAA,sBAAmB;oDAAC,WAAU;;;;;;;;;;;0DAEjC,8OAAC;gDACC,MAAK;gDACL,aAAY;gDACZ,OAAO;gDACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;gDAC7C,WAAU;;;;;;;;;;;;kDAKd,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDACC,OAAO;gDACP,UAAU,CAAC,IAAM,oBAAoB,EAAE,MAAM,CAAC,KAAK;gDACnD,WAAU;0DAET,WAAW,GAAG,CAAC,CAAA,yBACd,8OAAC;wDAAsB,OAAO;kEAAW;uDAA5B;;;;;;;;;;0DAIjB,8OAAC;gDACC,OAAO;gDACP,UAAU,CAAC,IAAM,kBAAkB,EAAE,MAAM,CAAC,KAAK;gDACjD,WAAU;0DAET,SAAS,GAAG,CAAC,CAAA,uBACZ,8OAAC;wDAAoB,OAAO;kEAAS;uDAAxB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCASzB,8OAAC;wBAAQ,WAAU;kCACjB,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;8CACZ,iBAAiB,GAAG,CAAC,CAAC,SAAS,sBAC9B,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4CAET,SAAS;gDAAE,SAAS;gDAAG,GAAG;4CAAG;4CAC7B,aAAa;gDAAE,SAAS;gDAAG,GAAG;4CAAE;4CAChC,YAAY;gDAAE,UAAU;gDAAK,OAAO,QAAQ;4CAAI;4CAChD,UAAU;gDAAE,MAAM;4CAAK;4CACvB,WAAU;;8DAEV,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DACC,KAAK,QAAQ,KAAK;4DAClB,KAAK,QAAQ,KAAK;4DAClB,WAAU;;;;;;sEAEZ,8OAAC;4DAAI,WAAU;sEACb,cAAA,8OAAC;gEAAK,WAAW,CAAC,wEAAwE,EAAE,eAAe,QAAQ,MAAM,GAAG;0EACzH,QAAQ,MAAM;;;;;;;;;;;sEAGnB,8OAAC;4DAAI,WAAU;sEACb,cAAA,8OAAC;gEAAK,WAAU;0EACb,QAAQ,QAAQ;;;;;;;;;;;;;;;;;8DAKvB,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAG,WAAU;sEACX,QAAQ,KAAK;;;;;;sEAGhB,8OAAC;4DAAE,WAAU;sEACV,QAAQ,gBAAgB;;;;;;sEAG3B,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAI,WAAU;;sFACb,8OAAC,yNAAA,CAAA,gBAAa;4EAAC,WAAU;;;;;;wEAAiB;wEACjC,QAAQ,MAAM;;;;;;;8EAEzB,8OAAC;oEAAI,WAAU;;sFACb,8OAAC,uNAAA,CAAA,eAAY;4EAAC,WAAU;;;;;;wEAAiB;wEAC9B,QAAQ,QAAQ;;;;;;;8EAE7B,8OAAC;oEAAI,WAAU;;sFACb,8OAAC,mOAAA,CAAA,qBAAkB;4EAAC,WAAU;;;;;;wEAAiB;wEACtC,QAAQ,MAAM;;;;;;;;;;;;;sEAI3B,8OAAC;4DAAI,WAAU;sEACb,cAAA,8OAAC;gEAAI,WAAU;;oEACZ,QAAQ,YAAY,CAAC,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,qBACrC,8OAAC;4EAEC,WAAU;sFAET;2EAHI;;;;;oEAMR,QAAQ,YAAY,CAAC,MAAM,GAAG,mBAC7B,8OAAC;wEAAK,WAAU;;4EAA2F;4EACvG,QAAQ,YAAY,CAAC,MAAM,GAAG;;;;;;;;;;;;;;;;;;sEAMxC,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAK,WAAU;8EACb,QAAQ,MAAM,KAAK,cAAc,cAAc;;;;;;8EAElD,8OAAC,4JAAA,CAAA,UAAI;oEACH,MAAM,CAAC,UAAU,EAAE,QAAQ,EAAE,EAAE;oEAC/B,WAAU;;wEACX;sFAEC,8OAAC,iPAAA,CAAA,4BAAyB;4EAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;2CA5EtC,QAAQ,EAAE;;;;;;;;;;gCAqFpB,iBAAiB,MAAM,KAAK,mBAC3B,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC,mNAAA,CAAA,aAAU;gDAAC,WAAU;;;;;;;;;;;sDAExB,8OAAC;4CAAG,WAAU;sDAAyC;;;;;;sDACvD,8OAAC;4CAAE,WAAU;sDAAgB;;;;;;;;;;;;;;;;;;;;;;;kCASrC,8OAAC;wBAAQ,WAAU;kCACjB,cAAA,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gCACT,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAG;gCAC7B,aAAa;oCAAE,SAAS;oCAAG,GAAG;gCAAE;gCAChC,YAAY;oCAAE,UAAU;gCAAI;gCAC5B,UAAU;oCAAE,MAAM;gCAAK;gCACvB,WAAU;;kDAEV,8OAAC;wCAAG,WAAU;kDAA2D;;;;;;kDAGzE,8OAAC;wCAAE,WAAU;kDAA+C;;;;;;kDAI5D,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,4JAAA,CAAA,UAAI;gDACH,MAAK;gDACL,WAAU;0DACX;;;;;;0DAGD,8OAAC,4JAAA,CAAA,UAAI;gDACH,MAAK;gDACL,WAAU;0DACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BASX,8OAAC,4HAAA,CAAA,SAAM;;;;;;;;;;;AAGb", "debugId": null}}]}