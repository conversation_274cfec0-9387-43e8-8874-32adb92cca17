{"name": "@technoloway/web", "version": "1.0.0", "private": true, "scripts": {"build": "next build", "dev": "next dev", "lint": "next lint", "start": "next start", "type-check": "tsc --noEmit", "test": "jest", "test:watch": "jest --watch", "test:e2e": "playwright test", "test:e2e:ui": "playwright test --ui"}, "dependencies": {"@clerk/nextjs": "^4.29.0", "@headlessui/react": "^1.7.17", "@heroicons/react": "^2.0.18", "@hookform/resolvers": "^3.3.2", "@next/mdx": "^14.0.4", "@tailwindcss/forms": "^0.5.7", "@tailwindcss/typography": "^0.5.10", "@technoloway/api": "workspace:*", "@technoloway/database": "workspace:*", "@technoloway/ui": "workspace:*", "@trpc/client": "^10.45.0", "@trpc/next": "^10.45.0", "@trpc/react-query": "^10.45.0", "@trpc/server": "^10.45.0", "clsx": "^2.0.0", "framer-motion": "^10.16.16", "lucide-react": "^0.294.0", "next": "^14.0.4", "react": "^18.2.0", "react-dom": "^18.2.0", "react-hook-form": "^7.48.2", "tailwind-merge": "^2.1.0", "zod": "^3.22.4"}, "devDependencies": {"@playwright/test": "^1.40.0", "@testing-library/jest-dom": "^6.1.5", "@testing-library/react": "^14.1.2", "@types/jest": "^29.5.8", "@types/node": "^20.10.0", "@types/react": "^18.2.39", "@types/react-dom": "^18.2.17", "autoprefixer": "^10.4.16", "eslint": "^8.54.0", "eslint-config-next": "^14.0.4", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "postcss": "^8.4.32", "tailwindcss": "^3.3.6", "typescript": "^5.3.0"}}