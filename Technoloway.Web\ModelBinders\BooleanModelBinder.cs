using Microsoft.AspNetCore.Mvc.ModelBinding;

namespace Technoloway.Web.ModelBinders
{
    /// <summary>
    /// Custom model binder for boolean values that handles checkbox "on" values
    /// </summary>
    public class BooleanModelBinder : IModelBinder
    {
        public Task BindModelAsync(ModelBindingContext bindingContext)
        {
            if (bindingContext == null)
            {
                throw new ArgumentNullException(nameof(bindingContext));
            }

            // Only handle boolean types
            if (bindingContext.ModelType != typeof(bool) && bindingContext.ModelType != typeof(bool?))
            {
                return Task.CompletedTask;
            }

            var valueProviderResult = bindingContext.ValueProvider.GetValue(bindingContext.ModelName);

            if (valueProviderResult == ValueProviderResult.None)
            {
                // No value found, set to false for non-nullable bool, null for nullable bool
                if (bindingContext.ModelType == typeof(bool))
                {
                    bindingContext.Result = ModelBindingResult.Success(false);
                }
                else
                {
                    bindingContext.Result = ModelBindingResult.Success(null);
                }
                return Task.CompletedTask;
            }

            bindingContext.ModelState.SetModelValue(bindingContext.ModelName, valueProviderResult);

            var value = valueProviderResult.FirstValue;

            if (string.IsNullOrEmpty(value))
            {
                // Empty value, set to false for non-nullable bool, null for nullable bool
                if (bindingContext.ModelType == typeof(bool))
                {
                    bindingContext.Result = ModelBindingResult.Success(false);
                }
                else
                {
                    bindingContext.Result = ModelBindingResult.Success(null);
                }
                return Task.CompletedTask;
            }

            // Handle various boolean representations
            bool result;
            if (TryParseBooleanValue(value, out result))
            {
                bindingContext.Result = ModelBindingResult.Success(result);
            }
            else
            {
                // Invalid value
                bindingContext.ModelState.TryAddModelError(bindingContext.ModelName, "Invalid boolean value");
                bindingContext.Result = ModelBindingResult.Failed();
            }

            return Task.CompletedTask;
        }

        private static bool TryParseBooleanValue(string value, out bool result)
        {
            result = false;

            if (string.IsNullOrWhiteSpace(value))
            {
                return true; // Empty string is false
            }

            // Handle standard boolean values
            if (bool.TryParse(value, out result))
            {
                return true;
            }

            // Handle checkbox "on" value
            if (string.Equals(value, "on", StringComparison.OrdinalIgnoreCase))
            {
                result = true;
                return true;
            }

            // Handle numeric values (1 = true, 0 = false)
            if (int.TryParse(value, out int intValue))
            {
                result = intValue != 0;
                return true;
            }

            // Handle yes/no values
            if (string.Equals(value, "yes", StringComparison.OrdinalIgnoreCase) ||
                string.Equals(value, "y", StringComparison.OrdinalIgnoreCase))
            {
                result = true;
                return true;
            }

            if (string.Equals(value, "no", StringComparison.OrdinalIgnoreCase) ||
                string.Equals(value, "n", StringComparison.OrdinalIgnoreCase))
            {
                result = false;
                return true;
            }

            // Handle checkbox values that might come as "true,false" (when hidden input is also present)
            var values = value.Split(',');
            if (values.Length > 1)
            {
                // Take the first non-false value
                foreach (var val in values)
                {
                    if (TryParseBooleanValue(val.Trim(), out bool tempResult) && tempResult)
                    {
                        result = true;
                        return true;
                    }
                }
                result = false;
                return true;
            }

            return false; // Unable to parse
        }
    }

    /// <summary>
    /// Model binder provider for the BooleanModelBinder
    /// </summary>
    public class BooleanModelBinderProvider : IModelBinderProvider
    {
        public IModelBinder? GetBinder(ModelBinderProviderContext context)
        {
            if (context == null)
            {
                throw new ArgumentNullException(nameof(context));
            }

            if (context.Metadata.ModelType == typeof(bool) ||
                context.Metadata.ModelType == typeof(bool?))
            {
                return new BooleanModelBinder();
            }

            return null;
        }
    }
}
