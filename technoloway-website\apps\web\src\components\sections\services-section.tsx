'use client';

import { motion } from 'framer-motion';
import { 
  CodeBracketIcon, 
  DevicePhoneMobileIcon, 
  CloudIcon,
  CogIcon,
  ShieldCheckIcon,
  ChartBarIcon 
} from '@heroicons/react/24/outline';

const services = [
  {
    name: 'Web Development',
    description: 'Modern, responsive web applications built with the latest technologies and best practices.',
    icon: CodeBracketIcon,
    features: ['React/Next.js', 'TypeScript', 'Tailwind CSS', 'API Integration'],
  },
  {
    name: 'Mobile Development',
    description: 'Native and cross-platform mobile applications for iOS and Android.',
    icon: DevicePhoneMobileIcon,
    features: ['React Native', 'Flutter', 'iOS/Android', 'App Store Deployment'],
  },
  {
    name: 'Cloud Solutions',
    description: 'Scalable cloud infrastructure and deployment solutions for modern applications.',
    icon: CloudIcon,
    features: ['AWS/Azure/GCP', 'Docker/Kubernetes', 'CI/CD Pipelines', 'Monitoring'],
  },
  {
    name: 'API Development',
    description: 'Robust and scalable APIs and microservices architecture.',
    icon: CogIcon,
    features: ['REST/GraphQL', 'Microservices', 'Database Design', 'Documentation'],
  },
  {
    name: 'Security & Testing',
    description: 'Comprehensive security audits and automated testing solutions.',
    icon: ShieldCheckIcon,
    features: ['Security Audits', 'Automated Testing', 'Code Review', 'Performance'],
  },
  {
    name: 'Analytics & Insights',
    description: 'Data-driven insights and analytics solutions for business growth.',
    icon: ChartBarIcon,
    features: ['Data Analytics', 'Business Intelligence', 'Reporting', 'Dashboards'],
  },
];

export function ServicesSection() {
  return (
    <section id="services" className="section-padding bg-white">
      <div className="container">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          viewport={{ once: true }}
          className="text-center mb-16"
        >
          <h2 className="text-3xl font-bold tracking-tight text-gray-900 sm:text-4xl lg:text-5xl">
            Our <span className="gradient-text">Services</span>
          </h2>
          <p className="mt-4 text-lg text-gray-600 max-w-3xl mx-auto">
            We offer comprehensive software development services to help your business 
            thrive in the digital world. From concept to deployment, we've got you covered.
          </p>
        </motion.div>

        <div className="grid grid-cols-1 gap-8 md:grid-cols-2 lg:grid-cols-3">
          {services.map((service, index) => (
            <motion.div
              key={service.name}
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: index * 0.1 }}
              viewport={{ once: true }}
              className="group relative bg-white p-8 rounded-2xl border border-gray-200 hover:border-primary-300 hover:shadow-lg transition-all duration-300"
            >
              <div className="flex items-center justify-center w-12 h-12 bg-primary-100 rounded-lg mb-6 group-hover:bg-primary-200 transition-colors">
                <service.icon className="w-6 h-6 text-primary-600" />
              </div>
              
              <h3 className="text-xl font-semibold text-gray-900 mb-3">
                {service.name}
              </h3>
              
              <p className="text-gray-600 mb-6">
                {service.description}
              </p>
              
              <ul className="space-y-2">
                {service.features.map((feature) => (
                  <li key={feature} className="flex items-center text-sm text-gray-500">
                    <div className="w-1.5 h-1.5 bg-primary-500 rounded-full mr-3" />
                    {feature}
                  </li>
                ))}
              </ul>

              {/* Hover effect */}
              <div className="absolute inset-0 bg-gradient-to-r from-primary-50 to-secondary-50 rounded-2xl opacity-0 group-hover:opacity-100 transition-opacity duration-300 -z-10" />
            </motion.div>
          ))}
        </div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.8 }}
          viewport={{ once: true }}
          className="text-center mt-16"
        >
          <p className="text-lg text-gray-600 mb-8">
            Need something custom? We'd love to discuss your unique requirements.
          </p>
          <a
            href="/contact"
            className="inline-flex items-center px-8 py-3 border border-transparent text-base font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700 transition-colors"
          >
            Get Custom Quote
          </a>
        </motion.div>
      </div>
    </section>
  );
}
