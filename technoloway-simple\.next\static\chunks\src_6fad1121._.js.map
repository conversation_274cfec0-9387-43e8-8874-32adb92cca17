{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Project%20By%20AI/Technoloway/Technoloway%20%28Processing%29/technoloway-simple/src/components/header.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport Link from 'next/link';\nimport { usePathname } from 'next/navigation';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport { Bars3Icon, XMarkIcon } from '@heroicons/react/24/outline';\n\nconst navigation = [\n  { name: 'Home', href: '/' },\n  { name: 'About', href: '/about' },\n  { name: 'Services', href: '/services' },\n  { name: 'Projects', href: '/projects' },\n  { name: 'Technologies', href: '/technologies' },\n  { name: 'Contact', href: '/contact' },\n];\n\nexport function Header() {\n  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);\n  const pathname = usePathname();\n\n  const isActive = (href: string) => {\n    if (href === '/') {\n      return pathname === '/';\n    }\n    return pathname.startsWith(href);\n  };\n\n  return (\n    <header className=\"fixed inset-x-0 top-0 z-50 bg-white/80 backdrop-blur-md border-b border-gray-200\">\n      <nav className=\"container flex items-center justify-between py-4\" aria-label=\"Global\">\n        <div className=\"flex lg:flex-1\">\n          <Link href=\"/\" className=\"-m-1.5 p-1.5\">\n            <span className=\"sr-only\">Technoloway</span>\n            <div className=\"flex items-center space-x-2\">\n              <div className=\"h-8 w-8 bg-gradient-to-r from-blue-600 to-blue-400 rounded-lg flex items-center justify-center\">\n                <span className=\"text-white font-bold text-sm\">T</span>\n              </div>\n              <span className=\"text-xl font-bold text-gray-900\">Technoloway</span>\n            </div>\n          </Link>\n        </div>\n        \n        <div className=\"flex lg:hidden\">\n          <button\n            type=\"button\"\n            className=\"-m-2.5 inline-flex items-center justify-center rounded-md p-2.5 text-gray-700\"\n            onClick={() => setMobileMenuOpen(true)}\n          >\n            <span className=\"sr-only\">Open main menu</span>\n            <Bars3Icon className=\"h-6 w-6\" aria-hidden=\"true\" />\n          </button>\n        </div>\n        \n        <div className=\"hidden lg:flex lg:gap-x-8\">\n          {navigation.map((item) => (\n            <Link\n              key={item.name}\n              href={item.href}\n              className={`text-sm font-semibold leading-6 transition-colors hover:text-blue-600 ${\n                isActive(item.href) ? 'text-blue-600' : 'text-gray-900'\n              }`}\n            >\n              {item.name}\n            </Link>\n          ))}\n        </div>\n        \n        <div className=\"hidden lg:flex lg:flex-1 lg:justify-end\">\n          <Link href=\"/contact\" className=\"btn-primary\">\n            Get Started\n          </Link>\n        </div>\n      </nav>\n      \n      {/* Mobile menu */}\n      <AnimatePresence>\n        {mobileMenuOpen && (\n          <motion.div\n            initial={{ opacity: 0 }}\n            animate={{ opacity: 1 }}\n            exit={{ opacity: 0 }}\n            className=\"lg:hidden\"\n          >\n            <div className=\"fixed inset-0 z-50\" />\n            <motion.div\n              initial={{ x: '100%' }}\n              animate={{ x: 0 }}\n              exit={{ x: '100%' }}\n              transition={{ type: 'spring', damping: 25, stiffness: 200 }}\n              className=\"fixed inset-y-0 right-0 z-50 w-full overflow-y-auto bg-white px-6 py-6 sm:max-w-sm sm:ring-1 sm:ring-gray-900/10\"\n            >\n              <div className=\"flex items-center justify-between\">\n                <Link href=\"/\" className=\"-m-1.5 p-1.5\">\n                  <span className=\"sr-only\">Technoloway</span>\n                  <div className=\"flex items-center space-x-2\">\n                    <div className=\"h-8 w-8 bg-gradient-to-r from-blue-600 to-blue-400 rounded-lg flex items-center justify-center\">\n                      <span className=\"text-white font-bold text-sm\">T</span>\n                    </div>\n                    <span className=\"text-xl font-bold text-gray-900\">Technoloway</span>\n                  </div>\n                </Link>\n                <button\n                  type=\"button\"\n                  className=\"-m-2.5 rounded-md p-2.5 text-gray-700\"\n                  onClick={() => setMobileMenuOpen(false)}\n                >\n                  <span className=\"sr-only\">Close menu</span>\n                  <XMarkIcon className=\"h-6 w-6\" aria-hidden=\"true\" />\n                </button>\n              </div>\n              <div className=\"mt-6 flow-root\">\n                <div className=\"-my-6 divide-y divide-gray-500/10\">\n                  <div className=\"space-y-2 py-6\">\n                    {navigation.map((item) => (\n                      <Link\n                        key={item.name}\n                        href={item.href}\n                        className={`-mx-3 block rounded-lg px-3 py-2 text-base font-semibold leading-7 hover:bg-gray-50 ${\n                          isActive(item.href) ? 'text-blue-600' : 'text-gray-900'\n                        }`}\n                        onClick={() => setMobileMenuOpen(false)}\n                      >\n                        {item.name}\n                      </Link>\n                    ))}\n                  </div>\n                  <div className=\"py-6\">\n                    <Link\n                      href=\"/contact\"\n                      className=\"-mx-3 block rounded-lg px-3 py-2.5 text-base font-semibold leading-7 text-blue-600 hover:bg-gray-50\"\n                      onClick={() => setMobileMenuOpen(false)}\n                    >\n                      Get Started\n                    </Link>\n                  </div>\n                </div>\n              </div>\n            </motion.div>\n          </motion.div>\n        )}\n      </AnimatePresence>\n    </header>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AACA;AAAA;;;AANA;;;;;;AAQA,MAAM,aAAa;IACjB;QAAE,MAAM;QAAQ,MAAM;IAAI;IAC1B;QAAE,MAAM;QAAS,MAAM;IAAS;IAChC;QAAE,MAAM;QAAY,MAAM;IAAY;IACtC;QAAE,MAAM;QAAY,MAAM;IAAY;IACtC;QAAE,MAAM;QAAgB,MAAM;IAAgB;IAC9C;QAAE,MAAM;QAAW,MAAM;IAAW;CACrC;AAEM,SAAS;;IACd,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,WAAW,CAAA,GAAA,qIAAA,CAAA,cAAW,AAAD;IAE3B,MAAM,WAAW,CAAC;QAChB,IAAI,SAAS,KAAK;YAChB,OAAO,aAAa;QACtB;QACA,OAAO,SAAS,UAAU,CAAC;IAC7B;IAEA,qBACE,6LAAC;QAAO,WAAU;;0BAChB,6LAAC;gBAAI,WAAU;gBAAmD,cAAW;;kCAC3E,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC,+JAAA,CAAA,UAAI;4BAAC,MAAK;4BAAI,WAAU;;8CACvB,6LAAC;oCAAK,WAAU;8CAAU;;;;;;8CAC1B,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDAAK,WAAU;0DAA+B;;;;;;;;;;;sDAEjD,6LAAC;4CAAK,WAAU;sDAAkC;;;;;;;;;;;;;;;;;;;;;;;kCAKxD,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BACC,MAAK;4BACL,WAAU;4BACV,SAAS,IAAM,kBAAkB;;8CAEjC,6LAAC;oCAAK,WAAU;8CAAU;;;;;;8CAC1B,6LAAC,oNAAA,CAAA,YAAS;oCAAC,WAAU;oCAAU,eAAY;;;;;;;;;;;;;;;;;kCAI/C,6LAAC;wBAAI,WAAU;kCACZ,WAAW,GAAG,CAAC,CAAC,qBACf,6LAAC,+JAAA,CAAA,UAAI;gCAEH,MAAM,KAAK,IAAI;gCACf,WAAW,CAAC,sEAAsE,EAChF,SAAS,KAAK,IAAI,IAAI,kBAAkB,iBACxC;0CAED,KAAK,IAAI;+BANL,KAAK,IAAI;;;;;;;;;;kCAWpB,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC,+JAAA,CAAA,UAAI;4BAAC,MAAK;4BAAW,WAAU;sCAAc;;;;;;;;;;;;;;;;;0BAOlD,6LAAC,4LAAA,CAAA,kBAAe;0BACb,gCACC,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;oBAAE;oBACtB,SAAS;wBAAE,SAAS;oBAAE;oBACtB,MAAM;wBAAE,SAAS;oBAAE;oBACnB,WAAU;;sCAEV,6LAAC;4BAAI,WAAU;;;;;;sCACf,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,SAAS;gCAAE,GAAG;4BAAO;4BACrB,SAAS;gCAAE,GAAG;4BAAE;4BAChB,MAAM;gCAAE,GAAG;4BAAO;4BAClB,YAAY;gCAAE,MAAM;gCAAU,SAAS;gCAAI,WAAW;4BAAI;4BAC1D,WAAU;;8CAEV,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,+JAAA,CAAA,UAAI;4CAAC,MAAK;4CAAI,WAAU;;8DACvB,6LAAC;oDAAK,WAAU;8DAAU;;;;;;8DAC1B,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAI,WAAU;sEACb,cAAA,6LAAC;gEAAK,WAAU;0EAA+B;;;;;;;;;;;sEAEjD,6LAAC;4DAAK,WAAU;sEAAkC;;;;;;;;;;;;;;;;;;sDAGtD,6LAAC;4CACC,MAAK;4CACL,WAAU;4CACV,SAAS,IAAM,kBAAkB;;8DAEjC,6LAAC;oDAAK,WAAU;8DAAU;;;;;;8DAC1B,6LAAC,oNAAA,CAAA,YAAS;oDAAC,WAAU;oDAAU,eAAY;;;;;;;;;;;;;;;;;;8CAG/C,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;0DACZ,WAAW,GAAG,CAAC,CAAC,qBACf,6LAAC,+JAAA,CAAA,UAAI;wDAEH,MAAM,KAAK,IAAI;wDACf,WAAW,CAAC,oFAAoF,EAC9F,SAAS,KAAK,IAAI,IAAI,kBAAkB,iBACxC;wDACF,SAAS,IAAM,kBAAkB;kEAEhC,KAAK,IAAI;uDAPL,KAAK,IAAI;;;;;;;;;;0DAWpB,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC,+JAAA,CAAA,UAAI;oDACH,MAAK;oDACL,WAAU;oDACV,SAAS,IAAM,kBAAkB;8DAClC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAYrB;GA/HgB;;QAEG,qIAAA,CAAA,cAAW;;;KAFd", "debugId": null}}, {"offset": {"line": 412, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Project%20By%20AI/Technoloway/Technoloway%20%28Processing%29/technoloway-simple/src/components/footer.tsx"], "sourcesContent": ["import Link from 'next/link';\n\nexport function Footer() {\n  return (\n    <footer className=\"bg-gray-900 text-white\">\n      <div className=\"container py-16\">\n        <div className=\"grid grid-cols-1 md:grid-cols-4 gap-8\">\n          <div className=\"col-span-1 md:col-span-2\">\n            <div className=\"flex items-center space-x-2 mb-6\">\n              <div className=\"h-8 w-8 bg-gradient-to-r from-blue-600 to-blue-400 rounded-lg flex items-center justify-center\">\n                <span className=\"text-white font-bold text-sm\">T</span>\n              </div>\n              <span className=\"text-xl font-bold\">Technoloway</span>\n            </div>\n            <p className=\"text-gray-300 mb-6 max-w-md\">\n              Transforming ideas into digital reality. We build exceptional software \n              solutions that drive business growth and innovation.\n            </p>\n            <div className=\"space-y-2\">\n              <p className=\"text-gray-300\"><EMAIL></p>\n              <p className=\"text-gray-300\">+****************</p>\n              <p className=\"text-gray-300\">San Francisco, CA</p>\n            </div>\n          </div>\n          \n          <div>\n            <h3 className=\"text-lg font-semibold mb-4\">Services</h3>\n            <ul className=\"space-y-2 text-gray-300\">\n              <li><Link href=\"/#services\" className=\"hover:text-white transition-colors\">Web Development</Link></li>\n              <li><Link href=\"/#services\" className=\"hover:text-white transition-colors\">Mobile Apps</Link></li>\n              <li><Link href=\"/#services\" className=\"hover:text-white transition-colors\">Cloud Solutions</Link></li>\n              <li><Link href=\"/#services\" className=\"hover:text-white transition-colors\">API Development</Link></li>\n            </ul>\n          </div>\n          \n          <div>\n            <h3 className=\"text-lg font-semibold mb-4\">Company</h3>\n            <ul className=\"space-y-2 text-gray-300\">\n              <li><Link href=\"/team\" className=\"hover:text-white transition-colors\">About</Link></li>\n              <li><Link href=\"/team\" className=\"hover:text-white transition-colors\">Team</Link></li>\n              <li><Link href=\"/portfolio\" className=\"hover:text-white transition-colors\">Portfolio</Link></li>\n              <li><Link href=\"/blog\" className=\"hover:text-white transition-colors\">Blog</Link></li>\n              <li><Link href=\"/#contact\" className=\"hover:text-white transition-colors\">Contact</Link></li>\n            </ul>\n          </div>\n        </div>\n        \n        <div className=\"border-t border-gray-700 mt-12 pt-8 text-center\">\n          <p className=\"text-gray-400\">\n            &copy; {new Date().getFullYear()} Technoloway. All rights reserved.\n          </p>\n        </div>\n      </div>\n    </footer>\n  );\n}\n"], "names": [], "mappings": ";;;;AAAA;;;AAEO,SAAS;IACd,qBACE,6LAAC;QAAO,WAAU;kBAChB,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDAAK,WAAU;0DAA+B;;;;;;;;;;;sDAEjD,6LAAC;4CAAK,WAAU;sDAAoB;;;;;;;;;;;;8CAEtC,6LAAC;oCAAE,WAAU;8CAA8B;;;;;;8CAI3C,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAE,WAAU;sDAAgB;;;;;;sDAC7B,6LAAC;4CAAE,WAAU;sDAAgB;;;;;;sDAC7B,6LAAC;4CAAE,WAAU;sDAAgB;;;;;;;;;;;;;;;;;;sCAIjC,6LAAC;;8CACC,6LAAC;oCAAG,WAAU;8CAA6B;;;;;;8CAC3C,6LAAC;oCAAG,WAAU;;sDACZ,6LAAC;sDAAG,cAAA,6LAAC,+JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAa,WAAU;0DAAqC;;;;;;;;;;;sDAC3E,6LAAC;sDAAG,cAAA,6LAAC,+JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAa,WAAU;0DAAqC;;;;;;;;;;;sDAC3E,6LAAC;sDAAG,cAAA,6LAAC,+JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAa,WAAU;0DAAqC;;;;;;;;;;;sDAC3E,6LAAC;sDAAG,cAAA,6LAAC,+JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAa,WAAU;0DAAqC;;;;;;;;;;;;;;;;;;;;;;;sCAI/E,6LAAC;;8CACC,6LAAC;oCAAG,WAAU;8CAA6B;;;;;;8CAC3C,6LAAC;oCAAG,WAAU;;sDACZ,6LAAC;sDAAG,cAAA,6LAAC,+JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAQ,WAAU;0DAAqC;;;;;;;;;;;sDACtE,6LAAC;sDAAG,cAAA,6LAAC,+JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAQ,WAAU;0DAAqC;;;;;;;;;;;sDACtE,6LAAC;sDAAG,cAAA,6LAAC,+JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAa,WAAU;0DAAqC;;;;;;;;;;;sDAC3E,6LAAC;sDAAG,cAAA,6LAAC,+JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAQ,WAAU;0DAAqC;;;;;;;;;;;sDACtE,6LAAC;sDAAG,cAAA,6LAAC,+JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAY,WAAU;0DAAqC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAKhF,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAE,WAAU;;4BAAgB;4BACnB,IAAI,OAAO,WAAW;4BAAG;;;;;;;;;;;;;;;;;;;;;;;AAM7C;KArDgB", "debugId": null}}, {"offset": {"line": 744, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Project%20By%20AI/Technoloway/Technoloway%20%28Processing%29/technoloway-simple/src/app/services/page.tsx"], "sourcesContent": ["'use client';\n\nimport Link from 'next/link';\nimport { motion } from 'framer-motion';\nimport {\n  CodeBracketIcon,\n  DevicePhoneMobileIcon,\n  CloudIcon,\n  CogIcon,\n  ShieldCheckIcon,\n  ChartBarIcon,\n  ArrowRightIcon,\n  CheckIcon,\n  CurrencyDollarIcon,\n  ClockIcon,\n  UserGroupIcon,\n} from '@heroicons/react/24/outline';\nimport { Header } from '@/components/header';\nimport { Footer } from '@/components/footer';\n\nconst services = [\n  {\n    id: 'web-development',\n    name: 'Web Development',\n    description: 'Modern, responsive web applications built with the latest technologies and best practices.',\n    shortDescription: 'Custom web applications and websites',\n    icon: CodeBracketIcon,\n    features: ['React/Next.js', 'TypeScript', 'Tailwind CSS', 'API Integration', 'SEO Optimization', 'Performance Optimization'],\n    pricing: {\n      startingPrice: 5000,\n      currency: 'USD',\n      billingType: 'project'\n    },\n    deliveryTime: '4-8 weeks',\n    category: 'Development',\n    technologies: ['React', 'Next.js', 'TypeScript', 'Node.js', 'PostgreSQL', 'AWS'],\n    benefits: [\n      'Responsive design that works on all devices',\n      'Fast loading times and optimal performance',\n      'SEO-friendly architecture',\n      'Scalable and maintainable codebase',\n      'Modern UI/UX design',\n      'Cross-browser compatibility'\n    ]\n  },\n  {\n    id: 'mobile-development',\n    name: 'Mobile Development',\n    description: 'Native and cross-platform mobile applications for iOS and Android with seamless user experiences.',\n    shortDescription: 'iOS and Android mobile applications',\n    icon: DevicePhoneMobileIcon,\n    features: ['React Native', 'Flutter', 'iOS/Android', 'App Store Deployment', 'Push Notifications', 'Offline Support'],\n    pricing: {\n      startingPrice: 8000,\n      currency: 'USD',\n      billingType: 'project'\n    },\n    deliveryTime: '6-12 weeks',\n    category: 'Development',\n    technologies: ['React Native', 'Flutter', 'Swift', 'Kotlin', 'Firebase', 'App Store Connect'],\n    benefits: [\n      'Native performance and user experience',\n      'Cross-platform compatibility',\n      'App store optimization',\n      'Push notification integration',\n      'Offline functionality',\n      'Regular updates and maintenance'\n    ]\n  },\n  {\n    id: 'cloud-solutions',\n    name: 'Cloud Solutions',\n    description: 'Scalable cloud infrastructure and deployment solutions for modern applications with high availability.',\n    shortDescription: 'Cloud infrastructure and DevOps',\n    icon: CloudIcon,\n    features: ['AWS/Azure/GCP', 'Docker/Kubernetes', 'CI/CD Pipelines', 'Monitoring', 'Auto-scaling', 'Security'],\n    pricing: {\n      startingPrice: 3000,\n      currency: 'USD',\n      billingType: 'monthly'\n    },\n    deliveryTime: '2-4 weeks',\n    category: 'Infrastructure',\n    technologies: ['AWS', 'Docker', 'Kubernetes', 'Terraform', 'Jenkins', 'Prometheus'],\n    benefits: [\n      'Scalable infrastructure that grows with your business',\n      'High availability and disaster recovery',\n      'Cost optimization and monitoring',\n      'Automated deployment pipelines',\n      'Enhanced security and compliance',\n      '24/7 monitoring and support'\n    ]\n  },\n  {\n    id: 'api-development',\n    name: 'API Development',\n    description: 'Robust and scalable APIs and microservices architecture for enterprise applications.',\n    shortDescription: 'RESTful APIs and microservices',\n    icon: CogIcon,\n    features: ['REST/GraphQL', 'Microservices', 'Database Design', 'Documentation', 'Authentication', 'Rate Limiting'],\n    pricing: {\n      startingPrice: 4000,\n      currency: 'USD',\n      billingType: 'project'\n    },\n    deliveryTime: '3-6 weeks',\n    category: 'Development',\n    technologies: ['Node.js', 'Python', 'GraphQL', 'PostgreSQL', 'Redis', 'JWT'],\n    benefits: [\n      'RESTful and GraphQL API design',\n      'Comprehensive documentation',\n      'Authentication and authorization',\n      'Rate limiting and security',\n      'Scalable microservices architecture',\n      'API versioning and maintenance'\n    ]\n  },\n  {\n    id: 'security-testing',\n    name: 'Security & Testing',\n    description: 'Comprehensive security audits and automated testing solutions to ensure application reliability.',\n    shortDescription: 'Security audits and quality assurance',\n    icon: ShieldCheckIcon,\n    features: ['Security Audits', 'Automated Testing', 'Code Review', 'Performance Testing', 'Penetration Testing', 'Compliance'],\n    pricing: {\n      startingPrice: 2500,\n      currency: 'USD',\n      billingType: 'project'\n    },\n    deliveryTime: '2-3 weeks',\n    category: 'Quality Assurance',\n    technologies: ['Jest', 'Cypress', 'OWASP', 'SonarQube', 'Burp Suite', 'Selenium'],\n    benefits: [\n      'Comprehensive security assessments',\n      'Automated testing pipelines',\n      'Code quality analysis',\n      'Performance optimization',\n      'Compliance with industry standards',\n      'Detailed reporting and recommendations'\n    ]\n  },\n  {\n    id: 'analytics-insights',\n    name: 'Analytics & Insights',\n    description: 'Data-driven insights and analytics solutions for business growth and decision making.',\n    shortDescription: 'Business intelligence and analytics',\n    icon: ChartBarIcon,\n    features: ['Data Analytics', 'Business Intelligence', 'Reporting', 'Dashboards', 'Machine Learning', 'Data Visualization'],\n    pricing: {\n      startingPrice: 3500,\n      currency: 'USD',\n      billingType: 'project'\n    },\n    deliveryTime: '4-8 weeks',\n    category: 'Analytics',\n    technologies: ['Python', 'R', 'Tableau', 'Power BI', 'TensorFlow', 'Apache Spark'],\n    benefits: [\n      'Custom analytics dashboards',\n      'Real-time data visualization',\n      'Predictive analytics and ML models',\n      'Business intelligence reporting',\n      'Data pipeline automation',\n      'Actionable insights and recommendations'\n    ]\n  },\n];\n\nconst formatPrice = (pricing: any) => {\n  const { startingPrice, currency, billingType } = pricing;\n  const formattedPrice = new Intl.NumberFormat('en-US', {\n    style: 'currency',\n    currency: currency,\n    minimumFractionDigits: 0,\n  }).format(startingPrice);\n  \n  return `Starting at ${formattedPrice}${billingType === 'monthly' ? '/month' : ''}`;\n};\n\nexport default function ServicesPage() {\n  return (\n    <div className=\"min-h-screen bg-white\">\n      <Header />\n\n      <main className=\"pt-20\">\n        {/* Hero Section */}\n        <section className=\"relative py-24 bg-gradient-to-br from-gray-50 via-white to-gray-100\">\n          <div className=\"container\">\n            <motion.div\n              initial={{ opacity: 0, y: 20 }}\n              animate={{ opacity: 1, y: 0 }}\n              transition={{ duration: 0.6 }}\n              className=\"text-center max-w-4xl mx-auto\"\n            >\n              <motion.h1\n                initial={{ opacity: 0, y: 20 }}\n                animate={{ opacity: 1, y: 0 }}\n                transition={{ duration: 0.6, delay: 0.1 }}\n                className=\"text-4xl font-bold tracking-tight text-gray-900 sm:text-6xl lg:text-7xl\"\n              >\n                Our <span className=\"gradient-text\">Services</span>\n              </motion.h1>\n\n              <motion.p\n                initial={{ opacity: 0, y: 20 }}\n                animate={{ opacity: 1, y: 0 }}\n                transition={{ duration: 0.6, delay: 0.2 }}\n                className=\"mt-6 text-lg leading-8 text-gray-600 sm:text-xl lg:text-2xl\"\n              >\n                Comprehensive software development services to help your business\n                thrive in the digital world. From concept to deployment, we've got you covered.\n              </motion.p>\n            </motion.div>\n          </div>\n        </section>\n\n        {/* Services Grid */}\n        <section className=\"py-24 bg-white\">\n          <div className=\"container\">\n            <div className=\"grid grid-cols-1 gap-8 lg:grid-cols-2\">\n              {services.map((service, index) => (\n                <motion.div\n                  key={service.id}\n                  initial={{ opacity: 0, y: 20 }}\n                  whileInView={{ opacity: 1, y: 0 }}\n                  transition={{ duration: 0.6, delay: index * 0.1 }}\n                  viewport={{ once: true }}\n                  className=\"group relative bg-white p-8 rounded-2xl border border-gray-200 hover:border-blue-300 hover:shadow-xl transition-all duration-300\"\n                >\n                  <div className=\"flex items-start justify-between mb-6\">\n                    <div className=\"flex items-center justify-center w-12 h-12 bg-blue-100 rounded-lg group-hover:bg-blue-200 transition-colors\">\n                      <service.icon className=\"w-6 h-6 text-blue-600\" />\n                    </div>\n                    <div className=\"text-right\">\n                      <div className=\"text-sm text-gray-500 mb-1\">Starting at</div>\n                      <div className=\"text-lg font-bold text-blue-600\">\n                        {formatPrice(service.pricing)}\n                      </div>\n                    </div>\n                  </div>\n\n                  <h3 className=\"text-2xl font-semibold text-gray-900 mb-3\">\n                    {service.name}\n                  </h3>\n\n                  <p className=\"text-gray-600 mb-6 leading-relaxed\">\n                    {service.description}\n                  </p>\n\n                  <div className=\"space-y-4 mb-8\">\n                    <div className=\"flex items-center text-sm text-gray-500\">\n                      <ClockIcon className=\"w-4 h-4 mr-2\" />\n                      Delivery: {service.deliveryTime}\n                    </div>\n                    <div className=\"flex items-center text-sm text-gray-500\">\n                      <UserGroupIcon className=\"w-4 h-4 mr-2\" />\n                      Category: {service.category}\n                    </div>\n                  </div>\n\n                  <div className=\"mb-8\">\n                    <h4 className=\"text-sm font-medium text-gray-700 mb-3\">Key Features:</h4>\n                    <div className=\"grid grid-cols-2 gap-2\">\n                      {service.features.slice(0, 4).map((feature) => (\n                        <div key={feature} className=\"flex items-center text-sm text-gray-600\">\n                          <CheckIcon className=\"w-4 h-4 text-green-500 mr-2 flex-shrink-0\" />\n                          {feature}\n                        </div>\n                      ))}\n                    </div>\n                    {service.features.length > 4 && (\n                      <div className=\"mt-2 text-sm text-gray-500\">\n                        +{service.features.length - 4} more features\n                      </div>\n                    )}\n                  </div>\n\n                  <div className=\"flex items-center justify-between\">\n                    <Link\n                      href={`/services/${service.id}`}\n                      className=\"inline-flex items-center text-blue-600 hover:text-blue-700 font-medium group/link\"\n                    >\n                      Learn More\n                      <ArrowRightIcon className=\"ml-2 h-4 w-4 transition-transform group-hover/link:translate-x-1\" />\n                    </Link>\n                    <Link\n                      href=\"/contact\"\n                      className=\"btn-primary\"\n                    >\n                      Get Quote\n                    </Link>\n                  </div>\n\n                  {/* Hover effect */}\n                  <div className=\"absolute inset-0 bg-gradient-to-r from-blue-50 to-purple-50 rounded-2xl opacity-0 group-hover:opacity-100 transition-opacity duration-300 -z-10\" />\n                </motion.div>\n              ))}\n            </div>\n          </div>\n        </section>\n\n        {/* Process Section */}\n        <section className=\"py-24 bg-gray-50\">\n          <div className=\"container\">\n            <motion.div\n              initial={{ opacity: 0, y: 20 }}\n              whileInView={{ opacity: 1, y: 0 }}\n              transition={{ duration: 0.6 }}\n              viewport={{ once: true }}\n              className=\"text-center mb-16\"\n            >\n              <h2 className=\"text-3xl font-bold tracking-tight text-gray-900 sm:text-4xl\">\n                Our <span className=\"gradient-text\">Process</span>\n              </h2>\n              <p className=\"mt-4 text-lg text-gray-600 max-w-3xl mx-auto\">\n                We follow a proven methodology to ensure successful project delivery\n                and client satisfaction at every step.\n              </p>\n            </motion.div>\n\n            <div className=\"grid grid-cols-1 md:grid-cols-4 gap-8\">\n              {[\n                {\n                  step: '01',\n                  title: 'Discovery',\n                  description: 'We understand your business goals, requirements, and technical needs through detailed consultation.'\n                },\n                {\n                  step: '02',\n                  title: 'Planning',\n                  description: 'We create a comprehensive project plan with timelines, milestones, and resource allocation.'\n                },\n                {\n                  step: '03',\n                  title: 'Development',\n                  description: 'Our expert team builds your solution using agile methodology with regular updates and feedback.'\n                },\n                {\n                  step: '04',\n                  title: 'Delivery',\n                  description: 'We deploy your solution and provide ongoing support to ensure optimal performance.'\n                }\n              ].map((process, index) => (\n                <motion.div\n                  key={process.step}\n                  initial={{ opacity: 0, y: 20 }}\n                  whileInView={{ opacity: 1, y: 0 }}\n                  transition={{ duration: 0.6, delay: index * 0.1 }}\n                  viewport={{ once: true }}\n                  className=\"text-center\"\n                >\n                  <div className=\"w-16 h-16 bg-blue-600 text-white rounded-full flex items-center justify-center text-xl font-bold mx-auto mb-4\">\n                    {process.step}\n                  </div>\n                  <h3 className=\"text-xl font-semibold text-gray-900 mb-3\">\n                    {process.title}\n                  </h3>\n                  <p className=\"text-gray-600 leading-relaxed\">\n                    {process.description}\n                  </p>\n                </motion.div>\n              ))}\n            </div>\n          </div>\n        </section>\n\n        {/* CTA Section */}\n        <section className=\"py-24 bg-gradient-to-r from-blue-600 to-purple-600\">\n          <div className=\"container\">\n            <motion.div\n              initial={{ opacity: 0, y: 20 }}\n              whileInView={{ opacity: 1, y: 0 }}\n              transition={{ duration: 0.6 }}\n              viewport={{ once: true }}\n              className=\"text-center\"\n            >\n              <h2 className=\"text-3xl font-bold tracking-tight text-white sm:text-4xl\">\n                Ready to Get Started?\n              </h2>\n              <p className=\"mt-4 text-lg text-blue-100 max-w-3xl mx-auto\">\n                Let's discuss your project requirements and create a custom solution\n                that drives your business forward.\n              </p>\n              <div className=\"mt-8 flex flex-col sm:flex-row gap-4 justify-center\">\n                <Link\n                  href=\"/contact\"\n                  className=\"inline-flex items-center px-8 py-3 border border-transparent text-base font-medium rounded-md text-blue-600 bg-white hover:bg-gray-50 transition-colors\"\n                >\n                  Start Your Project\n                </Link>\n                <Link\n                  href=\"/portfolio\"\n                  className=\"inline-flex items-center px-8 py-3 border-2 border-white text-base font-medium rounded-md text-white hover:bg-white hover:text-blue-600 transition-colors\"\n                >\n                  View Our Work\n                </Link>\n              </div>\n            </motion.div>\n          </div>\n        </section>\n      </main>\n\n      <Footer />\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAaA;AACA;AAlBA;;;;;;;AAoBA,MAAM,WAAW;IACf;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,kBAAkB;QAClB,MAAM,gOAAA,CAAA,kBAAe;QACrB,UAAU;YAAC;YAAiB;YAAc;YAAgB;YAAmB;YAAoB;SAA2B;QAC5H,SAAS;YACP,eAAe;YACf,UAAU;YACV,aAAa;QACf;QACA,cAAc;QACd,UAAU;QACV,cAAc;YAAC;YAAS;YAAW;YAAc;YAAW;YAAc;SAAM;QAChF,UAAU;YACR;YACA;YACA;YACA;YACA;YACA;SACD;IACH;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,kBAAkB;QAClB,MAAM,4OAAA,CAAA,wBAAqB;QAC3B,UAAU;YAAC;YAAgB;YAAW;YAAe;YAAwB;YAAsB;SAAkB;QACrH,SAAS;YACP,eAAe;YACf,UAAU;YACV,aAAa;QACf;QACA,cAAc;QACd,UAAU;QACV,cAAc;YAAC;YAAgB;YAAW;YAAS;YAAU;YAAY;SAAoB;QAC7F,UAAU;YACR;YACA;YACA;YACA;YACA;YACA;SACD;IACH;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,kBAAkB;QAClB,MAAM,oNAAA,CAAA,YAAS;QACf,UAAU;YAAC;YAAiB;YAAqB;YAAmB;YAAc;YAAgB;SAAW;QAC7G,SAAS;YACP,eAAe;YACf,UAAU;YACV,aAAa;QACf;QACA,cAAc;QACd,UAAU;QACV,cAAc;YAAC;YAAO;YAAU;YAAc;YAAa;YAAW;SAAa;QACnF,UAAU;YACR;YACA;YACA;YACA;YACA;YACA;SACD;IACH;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,kBAAkB;QAClB,MAAM,gNAAA,CAAA,UAAO;QACb,UAAU;YAAC;YAAgB;YAAiB;YAAmB;YAAiB;YAAkB;SAAgB;QAClH,SAAS;YACP,eAAe;YACf,UAAU;YACV,aAAa;QACf;QACA,cAAc;QACd,UAAU;QACV,cAAc;YAAC;YAAW;YAAU;YAAW;YAAc;YAAS;SAAM;QAC5E,UAAU;YACR;YACA;YACA;YACA;YACA;YACA;SACD;IACH;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,kBAAkB;QAClB,MAAM,gOAAA,CAAA,kBAAe;QACrB,UAAU;YAAC;YAAmB;YAAqB;YAAe;YAAuB;YAAuB;SAAa;QAC7H,SAAS;YACP,eAAe;YACf,UAAU;YACV,aAAa;QACf;QACA,cAAc;QACd,UAAU;QACV,cAAc;YAAC;YAAQ;YAAW;YAAS;YAAa;YAAc;SAAW;QACjF,UAAU;YACR;YACA;YACA;YACA;YACA;YACA;SACD;IACH;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,kBAAkB;QAClB,MAAM,0NAAA,CAAA,eAAY;QAClB,UAAU;YAAC;YAAkB;YAAyB;YAAa;YAAc;YAAoB;SAAqB;QAC1H,SAAS;YACP,eAAe;YACf,UAAU;YACV,aAAa;QACf;QACA,cAAc;QACd,UAAU;QACV,cAAc;YAAC;YAAU;YAAK;YAAW;YAAY;YAAc;SAAe;QAClF,UAAU;YACR;YACA;YACA;YACA;YACA;YACA;SACD;IACH;CACD;AAED,MAAM,cAAc,CAAC;IACnB,MAAM,EAAE,aAAa,EAAE,QAAQ,EAAE,WAAW,EAAE,GAAG;IACjD,MAAM,iBAAiB,IAAI,KAAK,YAAY,CAAC,SAAS;QACpD,OAAO;QACP,UAAU;QACV,uBAAuB;IACzB,GAAG,MAAM,CAAC;IAEV,OAAO,CAAC,YAAY,EAAE,iBAAiB,gBAAgB,YAAY,WAAW,IAAI;AACpF;AAEe,SAAS;IACtB,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC,+HAAA,CAAA,SAAM;;;;;0BAEP,6LAAC;gBAAK,WAAU;;kCAEd,6LAAC;wBAAQ,WAAU;kCACjB,cAAA,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gCACT,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAG;gCAC7B,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAE;gCAC5B,YAAY;oCAAE,UAAU;gCAAI;gCAC5B,WAAU;;kDAEV,6LAAC,6LAAA,CAAA,SAAM,CAAC,EAAE;wCACR,SAAS;4CAAE,SAAS;4CAAG,GAAG;wCAAG;wCAC7B,SAAS;4CAAE,SAAS;4CAAG,GAAG;wCAAE;wCAC5B,YAAY;4CAAE,UAAU;4CAAK,OAAO;wCAAI;wCACxC,WAAU;;4CACX;0DACK,6LAAC;gDAAK,WAAU;0DAAgB;;;;;;;;;;;;kDAGtC,6LAAC,6LAAA,CAAA,SAAM,CAAC,CAAC;wCACP,SAAS;4CAAE,SAAS;4CAAG,GAAG;wCAAG;wCAC7B,SAAS;4CAAE,SAAS;4CAAG,GAAG;wCAAE;wCAC5B,YAAY;4CAAE,UAAU;4CAAK,OAAO;wCAAI;wCACxC,WAAU;kDACX;;;;;;;;;;;;;;;;;;;;;;kCASP,6LAAC;wBAAQ,WAAU;kCACjB,cAAA,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;0CACZ,SAAS,GAAG,CAAC,CAAC,SAAS,sBACtB,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wCAET,SAAS;4CAAE,SAAS;4CAAG,GAAG;wCAAG;wCAC7B,aAAa;4CAAE,SAAS;4CAAG,GAAG;wCAAE;wCAChC,YAAY;4CAAE,UAAU;4CAAK,OAAO,QAAQ;wCAAI;wCAChD,UAAU;4CAAE,MAAM;wCAAK;wCACvB,WAAU;;0DAEV,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;kEACb,cAAA,6LAAC,QAAQ,IAAI;4DAAC,WAAU;;;;;;;;;;;kEAE1B,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAI,WAAU;0EAA6B;;;;;;0EAC5C,6LAAC;gEAAI,WAAU;0EACZ,YAAY,QAAQ,OAAO;;;;;;;;;;;;;;;;;;0DAKlC,6LAAC;gDAAG,WAAU;0DACX,QAAQ,IAAI;;;;;;0DAGf,6LAAC;gDAAE,WAAU;0DACV,QAAQ,WAAW;;;;;;0DAGtB,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;;0EACb,6LAAC,oNAAA,CAAA,YAAS;gEAAC,WAAU;;;;;;4DAAiB;4DAC3B,QAAQ,YAAY;;;;;;;kEAEjC,6LAAC;wDAAI,WAAU;;0EACb,6LAAC,4NAAA,CAAA,gBAAa;gEAAC,WAAU;;;;;;4DAAiB;4DAC/B,QAAQ,QAAQ;;;;;;;;;;;;;0DAI/B,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAG,WAAU;kEAAyC;;;;;;kEACvD,6LAAC;wDAAI,WAAU;kEACZ,QAAQ,QAAQ,CAAC,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,wBACjC,6LAAC;gEAAkB,WAAU;;kFAC3B,6LAAC,oNAAA,CAAA,YAAS;wEAAC,WAAU;;;;;;oEACpB;;+DAFO;;;;;;;;;;oDAMb,QAAQ,QAAQ,CAAC,MAAM,GAAG,mBACzB,6LAAC;wDAAI,WAAU;;4DAA6B;4DACxC,QAAQ,QAAQ,CAAC,MAAM,GAAG;4DAAE;;;;;;;;;;;;;0DAKpC,6LAAC;gDAAI,WAAU;;kEACb,6LAAC,+JAAA,CAAA,UAAI;wDACH,MAAM,CAAC,UAAU,EAAE,QAAQ,EAAE,EAAE;wDAC/B,WAAU;;4DACX;0EAEC,6LAAC,8NAAA,CAAA,iBAAc;gEAAC,WAAU;;;;;;;;;;;;kEAE5B,6LAAC,+JAAA,CAAA,UAAI;wDACH,MAAK;wDACL,WAAU;kEACX;;;;;;;;;;;;0DAMH,6LAAC;gDAAI,WAAU;;;;;;;uCAxEV,QAAQ,EAAE;;;;;;;;;;;;;;;;;;;;kCAgFzB,6LAAC;wBAAQ,WAAU;kCACjB,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oCACT,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAG;oCAC7B,aAAa;wCAAE,SAAS;wCAAG,GAAG;oCAAE;oCAChC,YAAY;wCAAE,UAAU;oCAAI;oCAC5B,UAAU;wCAAE,MAAM;oCAAK;oCACvB,WAAU;;sDAEV,6LAAC;4CAAG,WAAU;;gDAA8D;8DACtE,6LAAC;oDAAK,WAAU;8DAAgB;;;;;;;;;;;;sDAEtC,6LAAC;4CAAE,WAAU;sDAA+C;;;;;;;;;;;;8CAM9D,6LAAC;oCAAI,WAAU;8CACZ;wCACC;4CACE,MAAM;4CACN,OAAO;4CACP,aAAa;wCACf;wCACA;4CACE,MAAM;4CACN,OAAO;4CACP,aAAa;wCACf;wCACA;4CACE,MAAM;4CACN,OAAO;4CACP,aAAa;wCACf;wCACA;4CACE,MAAM;4CACN,OAAO;4CACP,aAAa;wCACf;qCACD,CAAC,GAAG,CAAC,CAAC,SAAS,sBACd,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4CAET,SAAS;gDAAE,SAAS;gDAAG,GAAG;4CAAG;4CAC7B,aAAa;gDAAE,SAAS;gDAAG,GAAG;4CAAE;4CAChC,YAAY;gDAAE,UAAU;gDAAK,OAAO,QAAQ;4CAAI;4CAChD,UAAU;gDAAE,MAAM;4CAAK;4CACvB,WAAU;;8DAEV,6LAAC;oDAAI,WAAU;8DACZ,QAAQ,IAAI;;;;;;8DAEf,6LAAC;oDAAG,WAAU;8DACX,QAAQ,KAAK;;;;;;8DAEhB,6LAAC;oDAAE,WAAU;8DACV,QAAQ,WAAW;;;;;;;2CAdjB,QAAQ,IAAI;;;;;;;;;;;;;;;;;;;;;kCAuB3B,6LAAC;wBAAQ,WAAU;kCACjB,cAAA,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gCACT,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAG;gCAC7B,aAAa;oCAAE,SAAS;oCAAG,GAAG;gCAAE;gCAChC,YAAY;oCAAE,UAAU;gCAAI;gCAC5B,UAAU;oCAAE,MAAM;gCAAK;gCACvB,WAAU;;kDAEV,6LAAC;wCAAG,WAAU;kDAA2D;;;;;;kDAGzE,6LAAC;wCAAE,WAAU;kDAA+C;;;;;;kDAI5D,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,+JAAA,CAAA,UAAI;gDACH,MAAK;gDACL,WAAU;0DACX;;;;;;0DAGD,6LAAC,+JAAA,CAAA,UAAI;gDACH,MAAK;gDACL,WAAU;0DACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BASX,6LAAC,+HAAA,CAAA,SAAM;;;;;;;;;;;AAGb;KAlOwB", "debugId": null}}]}