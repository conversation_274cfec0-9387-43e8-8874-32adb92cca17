how to run the last project

11:42 AM
Based on the retrieved information, there are multiple projects in the codebase. I'll provide instructions for running the most recently referenced projects:
------------------------------------------------
For technoloway-simple (Next.js project):-------

# Navigate to the project directory
cd technoloway-simple

# Install dependencies (if not already done)
npm install

# Start the development server
npm run dev

The application will be available at http://localhost:3000

-------------------------------------------------

For technoloway-website (Turborepo monorepo):----

# Navigate to the project directory
cd technoloway-website

# Install dependencies
npm install

# Start the development server
npm run dev

The application will be available at http://localhost:3000

-------------------------------------------------

For the .NET project (using run-app.bat):--------

# Simply run the batch file
run-app.bat

This will start the application at http://localhost:5258

