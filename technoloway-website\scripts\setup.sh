#!/bin/bash

# Technoloway Website Setup Script
echo "🚀 Setting up Technoloway Website..."

# Check if Node.js is installed
if ! command -v node &> /dev/null; then
    echo "❌ Node.js is not installed. Please install Node.js 18+ and try again."
    exit 1
fi

# Check Node.js version
NODE_VERSION=$(node -v | cut -d'v' -f2 | cut -d'.' -f1)
if [ "$NODE_VERSION" -lt 18 ]; then
    echo "❌ Node.js version 18+ is required. Current version: $(node -v)"
    exit 1
fi

echo "✅ Node.js version: $(node -v)"

# Install dependencies
echo "📦 Installing dependencies..."
npm install

if [ $? -ne 0 ]; then
    echo "❌ Failed to install dependencies"
    exit 1
fi

echo "✅ Dependencies installed successfully"

# Copy environment file
if [ ! -f "apps/web/.env.local" ]; then
    echo "📝 Creating environment file..."
    cp apps/web/.env.example apps/web/.env.local
    echo "✅ Environment file created at apps/web/.env.local"
    echo "⚠️  Please update the environment variables in apps/web/.env.local"
else
    echo "✅ Environment file already exists"
fi

# Generate Prisma client
echo "🗄️  Generating Prisma client..."
npm run db:generate

if [ $? -ne 0 ]; then
    echo "❌ Failed to generate Prisma client"
    exit 1
fi

echo "✅ Prisma client generated successfully"

# Check if database URL is set
if grep -q "DATABASE_URL=\"postgresql://username:password@localhost:5432/technoloway_db\"" apps/web/.env.local; then
    echo "⚠️  Please update the DATABASE_URL in apps/web/.env.local with your actual database credentials"
    echo "   Example: postgresql://username:password@localhost:5432/technoloway_db"
fi

# Check if Clerk keys are set
if grep -q "NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY=\"pk_test_...\"" apps/web/.env.local; then
    echo "⚠️  Please update the Clerk authentication keys in apps/web/.env.local"
    echo "   Get your keys from: https://dashboard.clerk.com/"
fi

echo ""
echo "🎉 Setup completed successfully!"
echo ""
echo "Next steps:"
echo "1. Update environment variables in apps/web/.env.local"
echo "2. Set up your PostgreSQL database"
echo "3. Run 'npm run db:push' to sync the database schema"
echo "4. Run 'npm run dev' to start the development server"
echo ""
echo "📚 Documentation: https://github.com/your-repo/technoloway-website"
echo "🆘 Support: <EMAIL>"
