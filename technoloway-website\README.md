# Technoloway Website

A modern, full-stack TypeScript website for Technoloway software development company built with Next.js, tRPC, Prisma, and Tailwind CSS.

## 🚀 Tech Stack

### Frontend
- **Framework**: Next.js 14 (App Router)
- **Styling**: Tailwind CSS
- **UI Components**: Custom components with Headless UI
- **Animations**: Framer Motion
- **Icons**: Heroicons & Lucide React

### Backend
- **API**: tRPC with Next.js API Routes
- **Database**: PostgreSQL with Prisma ORM
- **Authentication**: Clerk
- **Type Safety**: TypeScript (strict mode)

### Testing
- **E2E Testing**: Playwright
- **Unit/Integration**: Jest + React Testing Library

### Deployment
- **Platform**: Vercel (recommended)
- **Database**: Railway/Supabase PostgreSQL

## 📁 Project Structure

```
technoloway-website/
├── apps/
│   └── web/                 # Next.js frontend application
├── packages/
│   ├── ui/                  # Shared UI components
│   ├── database/            # Prisma schema & database utilities
│   ├── api/                 # tRPC API routers
│   └── config/              # Shared TypeScript configurations
├── docs/                    # Documentation
└── tools/                   # Build tools & scripts
```

## 🛠️ Getting Started

### Prerequisites

- Node.js 18+ 
- npm 9+
- PostgreSQL database

### Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd technoloway-website
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Set up environment variables**
   ```bash
   cp apps/web/.env.example apps/web/.env.local
   ```
   
   Fill in the required environment variables:
   - Database URL
   - Clerk authentication keys
   - Other optional services

4. **Set up the database**
   ```bash
   npm run db:generate
   npm run db:push
   ```

5. **Start the development server**
   ```bash
   npm run dev
   ```

   The application will be available at `http://localhost:3000`

## 📝 Available Scripts

### Root Level
- `npm run dev` - Start development servers
- `npm run build` - Build all applications
- `npm run lint` - Lint all packages
- `npm run test` - Run all tests
- `npm run test:e2e` - Run E2E tests
- `npm run type-check` - Type check all packages

### Database
- `npm run db:generate` - Generate Prisma client
- `npm run db:push` - Push schema to database
- `npm run db:migrate` - Run database migrations
- `npm run db:studio` - Open Prisma Studio

## 🏗️ Architecture Decisions

### Why tRPC over REST/GraphQL?
- **Type Safety**: End-to-end type safety from database to frontend
- **Developer Experience**: Excellent autocomplete and error handling
- **Performance**: Efficient batching and caching
- **Simplicity**: No schema definition files needed

### Why Clerk over NextAuth?
- **B2B Features**: Built-in organization management and team features
- **User Management**: Comprehensive admin dashboard
- **SSO Support**: Enterprise-grade authentication options
- **Developer Experience**: Simple setup and excellent documentation

### Why Monorepo with Turborepo?
- **Code Sharing**: Shared components, utilities, and types
- **Build Performance**: Intelligent caching and parallel execution
- **Scalability**: Easy to add new applications and packages
- **Developer Experience**: Consistent tooling across packages

## 🧪 Testing Strategy

### Unit & Integration Tests
- **Framework**: Jest + React Testing Library
- **Coverage**: Minimum 70% coverage required
- **Location**: `src/**/*.test.{ts,tsx}`

### E2E Tests
- **Framework**: Playwright
- **Browsers**: Chrome, Firefox, Safari, Mobile
- **Location**: `tests/e2e/`

### Running Tests
```bash
# Unit tests
npm run test

# E2E tests
npm run test:e2e

# E2E tests with UI
npm run test:e2e:ui
```

## 🚀 Deployment

### Vercel (Recommended)
1. Connect your GitHub repository to Vercel
2. Set environment variables in Vercel dashboard
3. Deploy automatically on push to main branch

### Manual Deployment
```bash
# Build the application
npm run build

# Start production server
npm start
```

## 🔧 Configuration

### Environment Variables
See `apps/web/.env.example` for all required and optional environment variables.

### Database Schema
The database schema is defined in `packages/database/prisma/schema.prisma`. After making changes:

```bash
npm run db:generate
npm run db:migrate
```

## 📚 Key Features

### 🎨 Modern Design
- Responsive design with mobile-first approach
- Dark/light mode support
- Smooth animations and transitions
- Accessibility compliant (WCAG 2.1)

### 🔐 Authentication & Authorization
- User authentication with Clerk
- Role-based access control
- Team/organization management
- Protected routes and API endpoints

### 📝 Content Management
- Dynamic blog with MDX support
- Case studies and portfolio showcase
- Contact form with server-side validation
- Admin dashboard for content management

### ⚡ Performance
- Server-side rendering (SSR)
- Static site generation (SSG) where appropriate
- Image optimization
- Code splitting and lazy loading

### 🔍 SEO & Analytics
- Meta tags and Open Graph support
- Structured data (JSON-LD)
- Sitemap generation
- Google Analytics integration

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🆘 Support

For support, email <EMAIL> or create an issue in this repository.
