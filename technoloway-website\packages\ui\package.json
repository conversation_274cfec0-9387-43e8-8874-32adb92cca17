{"name": "@technoloway/ui", "version": "1.0.0", "private": true, "main": "./src/index.ts", "types": "./src/index.ts", "scripts": {"type-check": "tsc --noEmit"}, "dependencies": {"@headlessui/react": "^1.7.17", "@heroicons/react": "^2.0.18", "class-variance-authority": "^0.7.0", "clsx": "^2.0.0", "framer-motion": "^10.16.16", "lucide-react": "^0.294.0", "react": "^18.2.0", "react-dom": "^18.2.0", "tailwind-merge": "^2.1.0"}, "devDependencies": {"@types/react": "^18.2.39", "@types/react-dom": "^18.2.17", "typescript": "^5.3.0"}, "peerDependencies": {"react": "^18.2.0", "react-dom": "^18.2.0"}}