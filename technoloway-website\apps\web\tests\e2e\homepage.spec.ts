import { test, expect } from '@playwright/test';

test.describe('Homepage', () => {
  test('should load the homepage', async ({ page }) => {
    await page.goto('/');
    
    // Check if the main heading is visible
    await expect(page.getByRole('heading', { name: /Transform Your Ideas Into Digital Reality/i })).toBeVisible();
    
    // Check if the navigation is present
    await expect(page.getByRole('navigation')).toBeVisible();
    
    // Check if the CTA buttons are present
    await expect(page.getByRole('link', { name: /Start Your Project/i })).toBeVisible();
    await expect(page.getByRole('button', { name: /Watch Our Story/i })).toBeVisible();
  });

  test('should navigate to contact page', async ({ page }) => {
    await page.goto('/');
    
    // Click on the contact link in navigation
    await page.getByRole('link', { name: /Contact/i }).click();
    
    // Should navigate to contact page
    await expect(page).toHaveURL('/contact');
  });

  test('should have proper meta tags', async ({ page }) => {
    await page.goto('/');
    
    // Check title
    await expect(page).toHaveTitle(/Technoloway/);
    
    // Check meta description
    const metaDescription = page.locator('meta[name="description"]');
    await expect(metaDescription).toHaveAttribute('content', /software development/i);
  });

  test('should be responsive', async ({ page }) => {
    // Test mobile viewport
    await page.setViewportSize({ width: 375, height: 667 });
    await page.goto('/');
    
    // Check if mobile menu button is visible
    await expect(page.getByRole('button', { name: /Open main menu/i })).toBeVisible();
    
    // Test desktop viewport
    await page.setViewportSize({ width: 1280, height: 720 });
    await page.reload();
    
    // Check if desktop navigation is visible
    await expect(page.getByRole('link', { name: /Services/i })).toBeVisible();
  });

  test('should have working scroll indicator', async ({ page }) => {
    await page.goto('/');
    
    // Check if scroll indicator is present
    const scrollIndicator = page.locator('[data-testid="scroll-indicator"]');
    // Note: You'll need to add data-testid to the scroll indicator component
  });
});
