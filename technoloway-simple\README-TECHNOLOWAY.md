# 🚀 Technoloway - Software Development Company Website

A modern, responsive website for Technoloway software development company built with Next.js 15, TypeScript, Tailwind CSS, and Framer Motion.

## ✨ Features

### 🎨 Modern Design
- **Responsive Layout**: Mobile-first design that works on all devices
- **Smooth Animations**: Beautiful animations powered by Framer Motion
- **Modern UI**: Clean, professional design with gradient accents
- **Interactive Elements**: Hover effects and smooth transitions

### 🧩 Components
- **Hero Section**: Eye-catching landing area with animated statistics
- **Services Section**: Showcase of development services with icons
- **Contact Section**: Contact form and company information
- **Mobile Navigation**: Responsive mobile menu with smooth animations
- **Footer**: Company links and contact information

### 🛠️ Tech Stack
- **Framework**: Next.js 15 (App Router)
- **Language**: TypeScript
- **Styling**: Tailwind CSS
- **Animations**: Framer Motion
- **Icons**: Heroicons
- **Fonts**: Inter (Google Fonts)

## 🚀 Getting Started

### Prerequisites
- Node.js 18+ 
- npm or yarn

### Installation

1. **Clone or navigate to the project**
   ```bash
   cd technoloway-simple
   ```

2. **Install dependencies** (already done)
   ```bash
   npm install
   ```

3. **Start the development server**
   ```bash
   npm run dev
   ```

4. **Open your browser**
   Navigate to `http://localhost:3000`

## 📁 Project Structure

```
technoloway-simple/
├── src/
│   └── app/
│       ├── globals.css      # Global styles and Tailwind config
│       ├── layout.tsx       # Root layout with metadata
│       └── page.tsx         # Main homepage component
├── public/                  # Static assets
├── package.json            # Dependencies and scripts
└── tailwind.config.ts      # Tailwind configuration
```

## 🎨 Customization

### Colors
The website uses a blue color scheme. To change colors, update the CSS variables in `globals.css`:

```css
.gradient-text {
  @apply bg-gradient-to-r from-blue-600 to-blue-400 bg-clip-text text-transparent;
}

.btn-primary {
  @apply bg-blue-600 hover:bg-blue-700;
}
```

### Content
Update the content in `src/app/page.tsx`:

- **Company Name**: Change "Technoloway" throughout the file
- **Services**: Modify the `services` array
- **Statistics**: Update the `stats` array
- **Contact Info**: Update email, phone, and address
- **Navigation**: Modify the `navigation` array

### Styling
- **Global Styles**: Edit `src/app/globals.css`
- **Component Styles**: Use Tailwind classes in `page.tsx`
- **Animations**: Customize Framer Motion animations

## 📱 Responsive Design

The website is fully responsive with breakpoints:
- **Mobile**: < 640px
- **Tablet**: 640px - 1024px  
- **Desktop**: > 1024px

## 🎭 Animations

Powered by Framer Motion:
- **Page Load**: Staggered animations on hero section
- **Scroll Animations**: Elements animate as they come into view
- **Hover Effects**: Interactive hover states on cards and buttons
- **Mobile Menu**: Smooth slide-in animation

## 🚀 Deployment

### Vercel (Recommended)
1. Push your code to GitHub
2. Connect your repository to Vercel
3. Deploy automatically

### Other Platforms
The website can be deployed to any platform that supports Next.js:
- Netlify
- Railway
- AWS Amplify
- DigitalOcean App Platform

## 📝 Available Scripts

```bash
npm run dev          # Start development server
npm run build        # Build for production
npm run start        # Start production server
npm run lint         # Run ESLint
```

## 🔧 Environment Variables

Create a `.env.local` file for environment-specific variables:

```env
NEXT_PUBLIC_APP_URL=http://localhost:3000
NEXT_PUBLIC_GOOGLE_ANALYTICS_ID=your-ga-id
```

## 📞 Support

For questions or support:
- **Email**: <EMAIL>
- **Phone**: +****************

## 📄 License

This project is created for Technoloway. All rights reserved.

---

**Built with ❤️ by Technoloway Team**
