{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Project%20By%20AI/Technoloway/Technoloway%20%28Processing%29/technoloway-simple/src/app/admin/users/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport { motion } from 'framer-motion';\nimport {\n  PlusIcon,\n  PencilIcon,\n  TrashIcon,\n  EyeIcon,\n  UserIcon,\n  ShieldCheckIcon,\n  EnvelopeIcon,\n  CalendarIcon,\n  MagnifyingGlassIcon,\n  CheckCircleIcon,\n  XCircleIcon,\n  KeyIcon,\n} from '@heroicons/react/24/outline';\n\n// Mock data - in real app, this would come from API\nconst users = [\n  {\n    id: 1,\n    name: 'Admin User',\n    email: '<EMAIL>',\n    role: 'Super Admin',\n    status: 'Active',\n    avatar: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=100&h=100&fit=crop&crop=face',\n    lastLogin: '2024-01-22T14:30:00Z',\n    createdAt: '2024-01-01T10:00:00Z',\n    permissions: ['all'],\n    department: 'Administration',\n    phone: '+****************',\n    twoFactorEnabled: true,\n  },\n  {\n    id: 2,\n    name: '<PERSON>',\n    email: '<EMAIL>',\n    role: 'Project Manager',\n    status: 'Active',\n    avatar: 'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=100&h=100&fit=crop&crop=face',\n    lastLogin: '2024-01-22T09:15:00Z',\n    createdAt: '2024-01-05T11:30:00Z',\n    permissions: ['projects', 'clients', 'team'],\n    department: 'Management',\n    phone: '+****************',\n    twoFactorEnabled: true,\n  },\n  {\n    id: 3,\n    name: 'Mike Chen',\n    email: '<EMAIL>',\n    role: 'Developer',\n    status: 'Active',\n    avatar: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=100&h=100&fit=crop&crop=face',\n    lastLogin: '2024-01-21T16:45:00Z',\n    createdAt: '2024-01-08T14:20:00Z',\n    permissions: ['projects', 'blog'],\n    department: 'Engineering',\n    phone: '+****************',\n    twoFactorEnabled: false,\n  },\n  {\n    id: 4,\n    name: 'Emily Davis',\n    email: '<EMAIL>',\n    role: 'Designer',\n    status: 'Active',\n    avatar: 'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=100&h=100&fit=crop&crop=face',\n    lastLogin: '2024-01-22T11:20:00Z',\n    createdAt: '2024-01-10T09:45:00Z',\n    permissions: ['blog', 'testimonials'],\n    department: 'Design',\n    phone: '+****************',\n    twoFactorEnabled: true,\n  },\n  {\n    id: 5,\n    name: 'David Park',\n    email: '<EMAIL>',\n    role: 'Marketing Manager',\n    status: 'Inactive',\n    avatar: 'https://images.unsplash.com/photo-1500648767791-00dcc994a43e?w=100&h=100&fit=crop&crop=face',\n    lastLogin: '2024-01-15T13:30:00Z',\n    createdAt: '2024-01-12T16:10:00Z',\n    permissions: ['blog', 'testimonials', 'contact-forms'],\n    department: 'Marketing',\n    phone: '+****************',\n    twoFactorEnabled: false,\n  },\n];\n\nconst roles = ['All', 'Super Admin', 'Admin', 'Project Manager', 'Developer', 'Designer', 'Marketing Manager'];\nconst statuses = ['All', 'Active', 'Inactive', 'Suspended'];\nconst departments = ['All', 'Administration', 'Management', 'Engineering', 'Design', 'Marketing'];\n\nexport default function UsersPage() {\n  const [usersList, setUsersList] = useState(users);\n  const [searchTerm, setSearchTerm] = useState('');\n  const [selectedRole, setSelectedRole] = useState('All');\n  const [selectedStatus, setSelectedStatus] = useState('All');\n  const [selectedDepartment, setSelectedDepartment] = useState('All');\n  const [selectedUser, setSelectedUser] = useState<any>(null);\n  const [showAddModal, setShowAddModal] = useState(false);\n\n  const filteredUsers = usersList.filter(user => {\n    const matchesSearch = user.name.toLowerCase().includes(searchTerm.toLowerCase()) ||\n                         user.email.toLowerCase().includes(searchTerm.toLowerCase()) ||\n                         user.department.toLowerCase().includes(searchTerm.toLowerCase());\n    const matchesRole = selectedRole === 'All' || user.role === selectedRole;\n    const matchesStatus = selectedStatus === 'All' || user.status === selectedStatus;\n    const matchesDepartment = selectedDepartment === 'All' || user.department === selectedDepartment;\n    \n    return matchesSearch && matchesRole && matchesStatus && matchesDepartment;\n  });\n\n  const handleStatusChange = (id: number, newStatus: string) => {\n    setUsersList(prev => prev.map(user => \n      user.id === id \n        ? { ...user, status: newStatus }\n        : user\n    ));\n  };\n\n  const handleDelete = (id: number) => {\n    if (confirm('Are you sure you want to delete this user?')) {\n      setUsersList(prev => prev.filter(user => user.id !== id));\n    }\n  };\n\n  const formatDate = (dateString: string) => {\n    return new Date(dateString).toLocaleDateString('en-US', {\n      year: 'numeric',\n      month: 'short',\n      day: 'numeric',\n      hour: '2-digit',\n      minute: '2-digit',\n    });\n  };\n\n  const getStatusColor = (status: string) => {\n    switch (status) {\n      case 'Active': return 'bg-green-100 text-green-800';\n      case 'Inactive': return 'bg-gray-100 text-gray-800';\n      case 'Suspended': return 'bg-red-100 text-red-800';\n      default: return 'bg-gray-100 text-gray-800';\n    }\n  };\n\n  const getRoleColor = (role: string) => {\n    switch (role) {\n      case 'Super Admin': return 'bg-purple-100 text-purple-800';\n      case 'Admin': return 'bg-blue-100 text-blue-800';\n      case 'Project Manager': return 'bg-indigo-100 text-indigo-800';\n      case 'Developer': return 'bg-green-100 text-green-800';\n      case 'Designer': return 'bg-pink-100 text-pink-800';\n      case 'Marketing Manager': return 'bg-yellow-100 text-yellow-800';\n      default: return 'bg-gray-100 text-gray-800';\n    }\n  };\n\n  return (\n    <div className=\"py-6\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 md:px-8\">\n        {/* Header */}\n        <div className=\"md:flex md:items-center md:justify-between mb-8\">\n          <div className=\"flex-1 min-w-0\">\n            <h2 className=\"text-2xl font-bold leading-7 text-gray-900 sm:text-3xl sm:truncate\">\n              User Management\n            </h2>\n            <p className=\"mt-1 text-sm text-gray-500\">\n              Manage user accounts, roles, and permissions\n            </p>\n          </div>\n          <div className=\"mt-4 flex md:mt-0 md:ml-4\">\n            <button\n              onClick={() => setShowAddModal(true)}\n              className=\"ml-3 inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500\"\n            >\n              <PlusIcon className=\"-ml-1 mr-2 h-5 w-5\" />\n              Add User\n            </button>\n          </div>\n        </div>\n\n        {/* Stats */}\n        <div className=\"mb-6 grid grid-cols-1 gap-5 sm:grid-cols-4\">\n          <div className=\"bg-white overflow-hidden shadow rounded-lg\">\n            <div className=\"p-5\">\n              <div className=\"flex items-center\">\n                <div className=\"flex-shrink-0\">\n                  <div className=\"w-8 h-8 bg-blue-500 rounded-md flex items-center justify-center\">\n                    <span className=\"text-white text-sm font-bold\">{usersList.length}</span>\n                  </div>\n                </div>\n                <div className=\"ml-5 w-0 flex-1\">\n                  <dl>\n                    <dt className=\"text-sm font-medium text-gray-500 truncate\">Total Users</dt>\n                    <dd className=\"text-lg font-medium text-gray-900\">Registered</dd>\n                  </dl>\n                </div>\n              </div>\n            </div>\n          </div>\n\n          <div className=\"bg-white overflow-hidden shadow rounded-lg\">\n            <div className=\"p-5\">\n              <div className=\"flex items-center\">\n                <div className=\"flex-shrink-0\">\n                  <div className=\"w-8 h-8 bg-green-500 rounded-md flex items-center justify-center\">\n                    <span className=\"text-white text-sm font-bold\">\n                      {usersList.filter(u => u.status === 'Active').length}\n                    </span>\n                  </div>\n                </div>\n                <div className=\"ml-5 w-0 flex-1\">\n                  <dl>\n                    <dt className=\"text-sm font-medium text-gray-500 truncate\">Active Users</dt>\n                    <dd className=\"text-lg font-medium text-gray-900\">Online</dd>\n                  </dl>\n                </div>\n              </div>\n            </div>\n          </div>\n\n          <div className=\"bg-white overflow-hidden shadow rounded-lg\">\n            <div className=\"p-5\">\n              <div className=\"flex items-center\">\n                <div className=\"flex-shrink-0\">\n                  <div className=\"w-8 h-8 bg-purple-500 rounded-md flex items-center justify-center\">\n                    <span className=\"text-white text-sm font-bold\">\n                      {usersList.filter(u => u.role.includes('Admin')).length}\n                    </span>\n                  </div>\n                </div>\n                <div className=\"ml-5 w-0 flex-1\">\n                  <dl>\n                    <dt className=\"text-sm font-medium text-gray-500 truncate\">Admins</dt>\n                    <dd className=\"text-lg font-medium text-gray-900\">Privileged</dd>\n                  </dl>\n                </div>\n              </div>\n            </div>\n          </div>\n\n          <div className=\"bg-white overflow-hidden shadow rounded-lg\">\n            <div className=\"p-5\">\n              <div className=\"flex items-center\">\n                <div className=\"flex-shrink-0\">\n                  <div className=\"w-8 h-8 bg-yellow-500 rounded-md flex items-center justify-center\">\n                    <span className=\"text-white text-sm font-bold\">\n                      {usersList.filter(u => u.twoFactorEnabled).length}\n                    </span>\n                  </div>\n                </div>\n                <div className=\"ml-5 w-0 flex-1\">\n                  <dl>\n                    <dt className=\"text-sm font-medium text-gray-500 truncate\">2FA Enabled</dt>\n                    <dd className=\"text-lg font-medium text-gray-900\">Secured</dd>\n                  </dl>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        {/* Filters */}\n        <div className=\"mb-6 bg-white shadow rounded-lg\">\n          <div className=\"px-4 py-5 sm:p-6\">\n            <div className=\"grid grid-cols-1 gap-4 sm:grid-cols-4\">\n              {/* Search */}\n              <div className=\"relative\">\n                <div className=\"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\">\n                  <MagnifyingGlassIcon className=\"h-5 w-5 text-gray-400\" />\n                </div>\n                <input\n                  type=\"text\"\n                  placeholder=\"Search users...\"\n                  value={searchTerm}\n                  onChange={(e) => setSearchTerm(e.target.value)}\n                  className=\"block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-blue-500 focus:border-blue-500 sm:text-sm\"\n                />\n              </div>\n\n              {/* Role Filter */}\n              <div>\n                <select\n                  value={selectedRole}\n                  onChange={(e) => setSelectedRole(e.target.value)}\n                  className=\"block w-full pl-3 pr-10 py-2 text-base border border-gray-300 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm rounded-md\"\n                >\n                  {roles.map(role => (\n                    <option key={role} value={role}>{role}</option>\n                  ))}\n                </select>\n              </div>\n\n              {/* Status Filter */}\n              <div>\n                <select\n                  value={selectedStatus}\n                  onChange={(e) => setSelectedStatus(e.target.value)}\n                  className=\"block w-full pl-3 pr-10 py-2 text-base border border-gray-300 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm rounded-md\"\n                >\n                  {statuses.map(status => (\n                    <option key={status} value={status}>{status}</option>\n                  ))}\n                </select>\n              </div>\n\n              {/* Department Filter */}\n              <div>\n                <select\n                  value={selectedDepartment}\n                  onChange={(e) => setSelectedDepartment(e.target.value)}\n                  className=\"block w-full pl-3 pr-10 py-2 text-base border border-gray-300 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm rounded-md\"\n                >\n                  {departments.map(dept => (\n                    <option key={dept} value={dept}>{dept}</option>\n                  ))}\n                </select>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        {/* Users List */}\n        <div className=\"bg-white shadow overflow-hidden sm:rounded-md\">\n          <ul className=\"divide-y divide-gray-200\">\n            {filteredUsers.map((user, index) => (\n              <motion.li\n                key={user.id}\n                initial={{ opacity: 0, y: 20 }}\n                animate={{ opacity: 1, y: 0 }}\n                transition={{ duration: 0.3, delay: index * 0.1 }}\n              >\n                <div className=\"px-4 py-4 sm:px-6\">\n                  <div className=\"flex items-center justify-between\">\n                    <div className=\"flex items-start space-x-4 flex-1\">\n                      <div className=\"flex-shrink-0\">\n                        <img\n                          className=\"h-12 w-12 rounded-full object-cover\"\n                          src={user.avatar}\n                          alt={user.name}\n                        />\n                      </div>\n                      \n                      <div className=\"flex-1 min-w-0\">\n                        <div className=\"flex items-center space-x-2 mb-1\">\n                          <h3 className=\"text-lg font-medium text-gray-900\">{user.name}</h3>\n                          <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getRoleColor(user.role)}`}>\n                            {user.role}\n                          </span>\n                          <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(user.status)}`}>\n                            {user.status}\n                          </span>\n                          {user.twoFactorEnabled && (\n                            <span className=\"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800\">\n                              <ShieldCheckIcon className=\"w-3 h-3 mr-1\" />\n                              2FA\n                            </span>\n                          )}\n                        </div>\n                        \n                        <div className=\"flex items-center space-x-4 text-sm text-gray-500 mb-2\">\n                          <div className=\"flex items-center\">\n                            <EnvelopeIcon className=\"h-4 w-4 mr-1\" />\n                            {user.email}\n                          </div>\n                          <span>{user.department}</span>\n                          <span>{user.phone}</span>\n                        </div>\n                        \n                        <div className=\"flex items-center space-x-4 text-sm text-gray-500\">\n                          <div className=\"flex items-center\">\n                            <CalendarIcon className=\"h-4 w-4 mr-1\" />\n                            Last login: {formatDate(user.lastLogin)}\n                          </div>\n                          <span>Joined: {formatDate(user.createdAt)}</span>\n                        </div>\n                      </div>\n                    </div>\n                    \n                    <div className=\"flex items-center space-x-2 ml-4\">\n                      <button\n                        onClick={() => setSelectedUser(user)}\n                        className=\"text-gray-400 hover:text-blue-600 transition-colors\"\n                        title=\"View Details\"\n                      >\n                        <EyeIcon className=\"h-5 w-5\" />\n                      </button>\n                      <button\n                        className=\"text-gray-400 hover:text-blue-600 transition-colors\"\n                        title=\"Edit\"\n                      >\n                        <PencilIcon className=\"h-5 w-5\" />\n                      </button>\n                      <button\n                        onClick={() => handleStatusChange(user.id, user.status === 'Active' ? 'Inactive' : 'Active')}\n                        className={`text-gray-400 hover:text-green-600 transition-colors ${\n                          user.status === 'Active' ? 'text-green-600' : ''\n                        }`}\n                        title={user.status === 'Active' ? 'Deactivate' : 'Activate'}\n                      >\n                        <CheckCircleIcon className=\"h-5 w-5\" />\n                      </button>\n                      <button\n                        onClick={() => handleDelete(user.id)}\n                        className=\"text-gray-400 hover:text-red-600 transition-colors\"\n                        title=\"Delete\"\n                      >\n                        <TrashIcon className=\"h-5 w-5\" />\n                      </button>\n                    </div>\n                  </div>\n                </div>\n              </motion.li>\n            ))}\n          </ul>\n        </div>\n\n        {/* Empty State */}\n        {filteredUsers.length === 0 && (\n          <div className=\"bg-white shadow rounded-lg\">\n            <div className=\"text-center py-12\">\n              <UserIcon className=\"mx-auto h-12 w-12 text-gray-400\" />\n              <h3 className=\"mt-2 text-sm font-medium text-gray-900\">No users found</h3>\n              <p className=\"mt-1 text-sm text-gray-500\">\n                Try adjusting your search or filter criteria.\n              </p>\n            </div>\n          </div>\n        )}\n\n        {/* User Detail Modal */}\n        {selectedUser && (\n          <div className=\"fixed inset-0 z-50 overflow-y-auto\">\n            <div className=\"flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0\">\n              <div className=\"fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity\" onClick={() => setSelectedUser(null)} />\n              \n              <div className=\"inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-2xl sm:w-full\">\n                <div className=\"bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4\">\n                  <div className=\"sm:flex sm:items-start\">\n                    <div className=\"mt-3 text-center sm:mt-0 sm:text-left w-full\">\n                      <div className=\"flex items-center space-x-3 mb-4\">\n                        <img\n                          className=\"h-12 w-12 rounded-full object-cover\"\n                          src={selectedUser.avatar}\n                          alt={selectedUser.name}\n                        />\n                        <div>\n                          <h3 className=\"text-lg leading-6 font-medium text-gray-900\">\n                            {selectedUser.name}\n                          </h3>\n                          <p className=\"text-sm text-gray-500\">{selectedUser.email}</p>\n                        </div>\n                      </div>\n                      \n                      <div className=\"space-y-4\">\n                        <div className=\"grid grid-cols-2 gap-4\">\n                          <div>\n                            <label className=\"block text-sm font-medium text-gray-700\">Role</label>\n                            <span className={`mt-1 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getRoleColor(selectedUser.role)}`}>\n                              {selectedUser.role}\n                            </span>\n                          </div>\n                          <div>\n                            <label className=\"block text-sm font-medium text-gray-700\">Status</label>\n                            <span className={`mt-1 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(selectedUser.status)}`}>\n                              {selectedUser.status}\n                            </span>\n                          </div>\n                          <div>\n                            <label className=\"block text-sm font-medium text-gray-700\">Department</label>\n                            <p className=\"mt-1 text-sm text-gray-900\">{selectedUser.department}</p>\n                          </div>\n                          <div>\n                            <label className=\"block text-sm font-medium text-gray-700\">Phone</label>\n                            <p className=\"mt-1 text-sm text-gray-900\">{selectedUser.phone}</p>\n                          </div>\n                        </div>\n                        \n                        <div>\n                          <label className=\"block text-sm font-medium text-gray-700\">Permissions</label>\n                          <div className=\"mt-1 flex flex-wrap gap-1\">\n                            {selectedUser.permissions.map((permission: string) => (\n                              <span\n                                key={permission}\n                                className=\"inline-flex items-center px-2 py-1 rounded text-xs font-medium bg-blue-100 text-blue-800\"\n                              >\n                                {permission}\n                              </span>\n                            ))}\n                          </div>\n                        </div>\n                        \n                        <div className=\"grid grid-cols-2 gap-4\">\n                          <div>\n                            <label className=\"block text-sm font-medium text-gray-700\">Two-Factor Authentication</label>\n                            <div className=\"mt-1 flex items-center\">\n                              {selectedUser.twoFactorEnabled ? (\n                                <CheckCircleIcon className=\"h-5 w-5 text-green-500 mr-2\" />\n                              ) : (\n                                <XCircleIcon className=\"h-5 w-5 text-red-500 mr-2\" />\n                              )}\n                              <span className=\"text-sm text-gray-900\">\n                                {selectedUser.twoFactorEnabled ? 'Enabled' : 'Disabled'}\n                              </span>\n                            </div>\n                          </div>\n                          <div>\n                            <label className=\"block text-sm font-medium text-gray-700\">Account Created</label>\n                            <p className=\"mt-1 text-sm text-gray-900\">{formatDate(selectedUser.createdAt)}</p>\n                          </div>\n                        </div>\n                        \n                        <div>\n                          <label className=\"block text-sm font-medium text-gray-700\">Last Login</label>\n                          <p className=\"mt-1 text-sm text-gray-900\">{formatDate(selectedUser.lastLogin)}</p>\n                        </div>\n                      </div>\n                    </div>\n                  </div>\n                </div>\n                <div className=\"bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse\">\n                  <button\n                    type=\"button\"\n                    className=\"w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-blue-600 text-base font-medium text-white hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 sm:ml-3 sm:w-auto sm:text-sm\"\n                  >\n                    Edit User\n                  </button>\n                  <button\n                    type=\"button\"\n                    className=\"mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 sm:mt-0 sm:w-auto sm:text-sm\"\n                    onClick={() => setSelectedUser(null)}\n                  >\n                    Close\n                  </button>\n                </div>\n              </div>\n            </div>\n          </div>\n        )}\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAJA;;;;;AAmBA,oDAAoD;AACpD,MAAM,QAAQ;IACZ;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,MAAM;QACN,QAAQ;QACR,QAAQ;QACR,WAAW;QACX,WAAW;QACX,aAAa;YAAC;SAAM;QACpB,YAAY;QACZ,OAAO;QACP,kBAAkB;IACpB;IACA;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,MAAM;QACN,QAAQ;QACR,QAAQ;QACR,WAAW;QACX,WAAW;QACX,aAAa;YAAC;YAAY;YAAW;SAAO;QAC5C,YAAY;QACZ,OAAO;QACP,kBAAkB;IACpB;IACA;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,MAAM;QACN,QAAQ;QACR,QAAQ;QACR,WAAW;QACX,WAAW;QACX,aAAa;YAAC;YAAY;SAAO;QACjC,YAAY;QACZ,OAAO;QACP,kBAAkB;IACpB;IACA;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,MAAM;QACN,QAAQ;QACR,QAAQ;QACR,WAAW;QACX,WAAW;QACX,aAAa;YAAC;YAAQ;SAAe;QACrC,YAAY;QACZ,OAAO;QACP,kBAAkB;IACpB;IACA;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,MAAM;QACN,QAAQ;QACR,QAAQ;QACR,WAAW;QACX,WAAW;QACX,aAAa;YAAC;YAAQ;YAAgB;SAAgB;QACtD,YAAY;QACZ,OAAO;QACP,kBAAkB;IACpB;CACD;AAED,MAAM,QAAQ;IAAC;IAAO;IAAe;IAAS;IAAmB;IAAa;IAAY;CAAoB;AAC9G,MAAM,WAAW;IAAC;IAAO;IAAU;IAAY;CAAY;AAC3D,MAAM,cAAc;IAAC;IAAO;IAAkB;IAAc;IAAe;IAAU;CAAY;AAElF,SAAS;IACtB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7D,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAO;IACtD,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEjD,MAAM,gBAAgB,UAAU,MAAM,CAAC,CAAA;QACrC,MAAM,gBAAgB,KAAK,IAAI,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW,OACxD,KAAK,KAAK,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW,OACxD,KAAK,UAAU,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW;QAClF,MAAM,cAAc,iBAAiB,SAAS,KAAK,IAAI,KAAK;QAC5D,MAAM,gBAAgB,mBAAmB,SAAS,KAAK,MAAM,KAAK;QAClE,MAAM,oBAAoB,uBAAuB,SAAS,KAAK,UAAU,KAAK;QAE9E,OAAO,iBAAiB,eAAe,iBAAiB;IAC1D;IAEA,MAAM,qBAAqB,CAAC,IAAY;QACtC,aAAa,CAAA,OAAQ,KAAK,GAAG,CAAC,CAAA,OAC5B,KAAK,EAAE,KAAK,KACR;oBAAE,GAAG,IAAI;oBAAE,QAAQ;gBAAU,IAC7B;IAER;IAEA,MAAM,eAAe,CAAC;QACpB,IAAI,QAAQ,+CAA+C;YACzD,aAAa,CAAA,OAAQ,KAAK,MAAM,CAAC,CAAA,OAAQ,KAAK,EAAE,KAAK;QACvD;IACF;IAEA,MAAM,aAAa,CAAC;QAClB,OAAO,IAAI,KAAK,YAAY,kBAAkB,CAAC,SAAS;YACtD,MAAM;YACN,OAAO;YACP,KAAK;YACL,MAAM;YACN,QAAQ;QACV;IACF;IAEA,MAAM,iBAAiB,CAAC;QACtB,OAAQ;YACN,KAAK;gBAAU,OAAO;YACtB,KAAK;gBAAY,OAAO;YACxB,KAAK;gBAAa,OAAO;YACzB;gBAAS,OAAO;QAClB;IACF;IAEA,MAAM,eAAe,CAAC;QACpB,OAAQ;YACN,KAAK;gBAAe,OAAO;YAC3B,KAAK;gBAAS,OAAO;YACrB,KAAK;gBAAmB,OAAO;YAC/B,KAAK;gBAAa,OAAO;YACzB,KAAK;gBAAY,OAAO;YACxB,KAAK;gBAAqB,OAAO;YACjC;gBAAS,OAAO;QAClB;IACF;IAEA,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;;8BAEb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAAqE;;;;;;8CAGnF,8OAAC;oCAAE,WAAU;8CAA6B;;;;;;;;;;;;sCAI5C,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCACC,SAAS,IAAM,gBAAgB;gCAC/B,WAAU;;kDAEV,8OAAC,+MAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;oCAAuB;;;;;;;;;;;;;;;;;;8BAOjD,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDAAK,WAAU;8DAAgC,UAAU,MAAM;;;;;;;;;;;;;;;;sDAGpE,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;;kEACC,8OAAC;wDAAG,WAAU;kEAA6C;;;;;;kEAC3D,8OAAC;wDAAG,WAAU;kEAAoC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAO5D,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDAAK,WAAU;8DACb,UAAU,MAAM,CAAC,CAAA,IAAK,EAAE,MAAM,KAAK,UAAU,MAAM;;;;;;;;;;;;;;;;sDAI1D,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;;kEACC,8OAAC;wDAAG,WAAU;kEAA6C;;;;;;kEAC3D,8OAAC;wDAAG,WAAU;kEAAoC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAO5D,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDAAK,WAAU;8DACb,UAAU,MAAM,CAAC,CAAA,IAAK,EAAE,IAAI,CAAC,QAAQ,CAAC,UAAU,MAAM;;;;;;;;;;;;;;;;sDAI7D,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;;kEACC,8OAAC;wDAAG,WAAU;kEAA6C;;;;;;kEAC3D,8OAAC;wDAAG,WAAU;kEAAoC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAO5D,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDAAK,WAAU;8DACb,UAAU,MAAM,CAAC,CAAA,IAAK,EAAE,gBAAgB,EAAE,MAAM;;;;;;;;;;;;;;;;sDAIvD,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;;kEACC,8OAAC;wDAAG,WAAU;kEAA6C;;;;;;kEAC3D,8OAAC;wDAAG,WAAU;kEAAoC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAS9D,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CAEb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC,qOAAA,CAAA,sBAAmB;gDAAC,WAAU;;;;;;;;;;;sDAEjC,8OAAC;4CACC,MAAK;4CACL,aAAY;4CACZ,OAAO;4CACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;4CAC7C,WAAU;;;;;;;;;;;;8CAKd,8OAAC;8CACC,cAAA,8OAAC;wCACC,OAAO;wCACP,UAAU,CAAC,IAAM,gBAAgB,EAAE,MAAM,CAAC,KAAK;wCAC/C,WAAU;kDAET,MAAM,GAAG,CAAC,CAAA,qBACT,8OAAC;gDAAkB,OAAO;0DAAO;+CAApB;;;;;;;;;;;;;;;8CAMnB,8OAAC;8CACC,cAAA,8OAAC;wCACC,OAAO;wCACP,UAAU,CAAC,IAAM,kBAAkB,EAAE,MAAM,CAAC,KAAK;wCACjD,WAAU;kDAET,SAAS,GAAG,CAAC,CAAA,uBACZ,8OAAC;gDAAoB,OAAO;0DAAS;+CAAxB;;;;;;;;;;;;;;;8CAMnB,8OAAC;8CACC,cAAA,8OAAC;wCACC,OAAO;wCACP,UAAU,CAAC,IAAM,sBAAsB,EAAE,MAAM,CAAC,KAAK;wCACrD,WAAU;kDAET,YAAY,GAAG,CAAC,CAAA,qBACf,8OAAC;gDAAkB,OAAO;0DAAO;+CAApB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BASzB,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAG,WAAU;kCACX,cAAc,GAAG,CAAC,CAAC,MAAM,sBACxB,8OAAC,0LAAA,CAAA,SAAM,CAAC,EAAE;gCAER,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAG;gCAC7B,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAE;gCAC5B,YAAY;oCAAE,UAAU;oCAAK,OAAO,QAAQ;gCAAI;0CAEhD,cAAA,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC;4DACC,WAAU;4DACV,KAAK,KAAK,MAAM;4DAChB,KAAK,KAAK,IAAI;;;;;;;;;;;kEAIlB,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEAAG,WAAU;kFAAqC,KAAK,IAAI;;;;;;kFAC5D,8OAAC;wEAAK,WAAW,CAAC,wEAAwE,EAAE,aAAa,KAAK,IAAI,GAAG;kFAClH,KAAK,IAAI;;;;;;kFAEZ,8OAAC;wEAAK,WAAW,CAAC,wEAAwE,EAAE,eAAe,KAAK,MAAM,GAAG;kFACtH,KAAK,MAAM;;;;;;oEAEb,KAAK,gBAAgB,kBACpB,8OAAC;wEAAK,WAAU;;0FACd,8OAAC,6NAAA,CAAA,kBAAe;gFAAC,WAAU;;;;;;4EAAiB;;;;;;;;;;;;;0EAMlD,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEAAI,WAAU;;0FACb,8OAAC,uNAAA,CAAA,eAAY;gFAAC,WAAU;;;;;;4EACvB,KAAK,KAAK;;;;;;;kFAEb,8OAAC;kFAAM,KAAK,UAAU;;;;;;kFACtB,8OAAC;kFAAM,KAAK,KAAK;;;;;;;;;;;;0EAGnB,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEAAI,WAAU;;0FACb,8OAAC,uNAAA,CAAA,eAAY;gFAAC,WAAU;;;;;;4EAAiB;4EAC5B,WAAW,KAAK,SAAS;;;;;;;kFAExC,8OAAC;;4EAAK;4EAAS,WAAW,KAAK,SAAS;;;;;;;;;;;;;;;;;;;;;;;;;0DAK9C,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDACC,SAAS,IAAM,gBAAgB;wDAC/B,WAAU;wDACV,OAAM;kEAEN,cAAA,8OAAC,6MAAA,CAAA,UAAO;4DAAC,WAAU;;;;;;;;;;;kEAErB,8OAAC;wDACC,WAAU;wDACV,OAAM;kEAEN,cAAA,8OAAC,mNAAA,CAAA,aAAU;4DAAC,WAAU;;;;;;;;;;;kEAExB,8OAAC;wDACC,SAAS,IAAM,mBAAmB,KAAK,EAAE,EAAE,KAAK,MAAM,KAAK,WAAW,aAAa;wDACnF,WAAW,CAAC,qDAAqD,EAC/D,KAAK,MAAM,KAAK,WAAW,mBAAmB,IAC9C;wDACF,OAAO,KAAK,MAAM,KAAK,WAAW,eAAe;kEAEjD,cAAA,8OAAC,6NAAA,CAAA,kBAAe;4DAAC,WAAU;;;;;;;;;;;kEAE7B,8OAAC;wDACC,SAAS,IAAM,aAAa,KAAK,EAAE;wDACnC,WAAU;wDACV,OAAM;kEAEN,cAAA,8OAAC,iNAAA,CAAA,YAAS;4DAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;+BAhFxB,KAAK,EAAE;;;;;;;;;;;;;;;gBA2FnB,cAAc,MAAM,KAAK,mBACxB,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,+MAAA,CAAA,WAAQ;gCAAC,WAAU;;;;;;0CACpB,8OAAC;gCAAG,WAAU;0CAAyC;;;;;;0CACvD,8OAAC;gCAAE,WAAU;0CAA6B;;;;;;;;;;;;;;;;;gBAQ/C,8BACC,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;gCAA6D,SAAS,IAAM,gBAAgB;;;;;;0CAE3G,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEACC,WAAU;gEACV,KAAK,aAAa,MAAM;gEACxB,KAAK,aAAa,IAAI;;;;;;0EAExB,8OAAC;;kFACC,8OAAC;wEAAG,WAAU;kFACX,aAAa,IAAI;;;;;;kFAEpB,8OAAC;wEAAE,WAAU;kFAAyB,aAAa,KAAK;;;;;;;;;;;;;;;;;;kEAI5D,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;;0FACC,8OAAC;gFAAM,WAAU;0FAA0C;;;;;;0FAC3D,8OAAC;gFAAK,WAAW,CAAC,6EAA6E,EAAE,aAAa,aAAa,IAAI,GAAG;0FAC/H,aAAa,IAAI;;;;;;;;;;;;kFAGtB,8OAAC;;0FACC,8OAAC;gFAAM,WAAU;0FAA0C;;;;;;0FAC3D,8OAAC;gFAAK,WAAW,CAAC,6EAA6E,EAAE,eAAe,aAAa,MAAM,GAAG;0FACnI,aAAa,MAAM;;;;;;;;;;;;kFAGxB,8OAAC;;0FACC,8OAAC;gFAAM,WAAU;0FAA0C;;;;;;0FAC3D,8OAAC;gFAAE,WAAU;0FAA8B,aAAa,UAAU;;;;;;;;;;;;kFAEpE,8OAAC;;0FACC,8OAAC;gFAAM,WAAU;0FAA0C;;;;;;0FAC3D,8OAAC;gFAAE,WAAU;0FAA8B,aAAa,KAAK;;;;;;;;;;;;;;;;;;0EAIjE,8OAAC;;kFACC,8OAAC;wEAAM,WAAU;kFAA0C;;;;;;kFAC3D,8OAAC;wEAAI,WAAU;kFACZ,aAAa,WAAW,CAAC,GAAG,CAAC,CAAC,2BAC7B,8OAAC;gFAEC,WAAU;0FAET;+EAHI;;;;;;;;;;;;;;;;0EASb,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;;0FACC,8OAAC;gFAAM,WAAU;0FAA0C;;;;;;0FAC3D,8OAAC;gFAAI,WAAU;;oFACZ,aAAa,gBAAgB,iBAC5B,8OAAC,6NAAA,CAAA,kBAAe;wFAAC,WAAU;;;;;6GAE3B,8OAAC,qNAAA,CAAA,cAAW;wFAAC,WAAU;;;;;;kGAEzB,8OAAC;wFAAK,WAAU;kGACb,aAAa,gBAAgB,GAAG,YAAY;;;;;;;;;;;;;;;;;;kFAInD,8OAAC;;0FACC,8OAAC;gFAAM,WAAU;0FAA0C;;;;;;0FAC3D,8OAAC;gFAAE,WAAU;0FAA8B,WAAW,aAAa,SAAS;;;;;;;;;;;;;;;;;;0EAIhF,8OAAC;;kFACC,8OAAC;wEAAM,WAAU;kFAA0C;;;;;;kFAC3D,8OAAC;wEAAE,WAAU;kFAA8B,WAAW,aAAa,SAAS;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kDAMtF,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDACC,MAAK;gDACL,WAAU;0DACX;;;;;;0DAGD,8OAAC;gDACC,MAAK;gDACL,WAAU;gDACV,SAAS,IAAM,gBAAgB;0DAChC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAWnB", "debugId": null}}]}