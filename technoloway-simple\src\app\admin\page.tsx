'use client';

import { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import {
  UserGroupIcon,
  BriefcaseIcon,
  DocumentTextIcon,
  CurrencyDollarIcon,
  ChartBarIcon,
  ClockIcon,
  ExclamationTriangleIcon,
  CheckCircleIcon,
  ArrowUpIcon,
  ArrowDownIcon,
} from '@heroicons/react/24/outline';

// Mock data - in real app, this would come from API
const dashboardStats = {
  projects: { total: 45, active: 12, completed: 33, growth: 8.2 },
  clients: { total: 28, active: 22, new: 3, growth: 12.5 },
  invoices: { total: 156, pending: 8, paid: 148, growth: -2.1 },
  revenue: { total: 485000, pending: 45000, paid: 440000, growth: 15.3 },
  contactForms: { total: 89, unread: 5, today: 2 },
  jobApplications: { total: 34, pending: 8, reviewed: 26 },
  testimonials: { total: 18, published: 15, pending: 3 },
  blogPosts: { total: 24, published: 20, draft: 4 },
};

const recentActivity = [
  { id: 1, type: 'project', title: 'New project created: E-commerce Platform', time: '2 hours ago', status: 'success' },
  { id: 2, type: 'client', title: 'New client registered: TechCorp Inc.', time: '4 hours ago', status: 'info' },
  { id: 3, type: 'invoice', title: 'Invoice #INV-2024-001 marked as paid', time: '6 hours ago', status: 'success' },
  { id: 4, type: 'contact', title: 'New contact form submission', time: '8 hours ago', status: 'warning' },
  { id: 5, type: 'application', title: 'New job application received', time: '1 day ago', status: 'info' },
];

const StatCard = ({ title, value, subtitle, icon: Icon, growth, color = 'blue' }: any) => {
  const isPositive = growth > 0;
  
  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
      className="bg-white overflow-hidden shadow rounded-lg"
    >
      <div className="p-5">
        <div className="flex items-center">
          <div className="flex-shrink-0">
            <Icon className={`h-6 w-6 text-${color}-600`} />
          </div>
          <div className="ml-5 w-0 flex-1">
            <dl>
              <dt className="text-sm font-medium text-gray-500 truncate">{title}</dt>
              <dd>
                <div className="text-lg font-medium text-gray-900">{value}</div>
              </dd>
            </dl>
          </div>
        </div>
      </div>
      <div className="bg-gray-50 px-5 py-3">
        <div className="text-sm">
          <div className="flex items-center justify-between">
            <span className="text-gray-500">{subtitle}</span>
            <div className={`flex items-center ${isPositive ? 'text-green-600' : 'text-red-600'}`}>
              {isPositive ? (
                <ArrowUpIcon className="h-4 w-4 mr-1" />
              ) : (
                <ArrowDownIcon className="h-4 w-4 mr-1" />
              )}
              <span className="font-medium">{Math.abs(growth)}%</span>
            </div>
          </div>
        </div>
      </div>
    </motion.div>
  );
};

const ActivityItem = ({ activity }: any) => {
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'success': return 'text-green-600 bg-green-100';
      case 'warning': return 'text-yellow-600 bg-yellow-100';
      case 'info': return 'text-blue-600 bg-blue-100';
      default: return 'text-gray-600 bg-gray-100';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'success': return CheckCircleIcon;
      case 'warning': return ExclamationTriangleIcon;
      case 'info': return ClockIcon;
      default: return ClockIcon;
    }
  };

  const StatusIcon = getStatusIcon(activity.status);

  return (
    <div className="flex items-center space-x-3 py-3">
      <div className={`flex-shrink-0 w-8 h-8 rounded-full flex items-center justify-center ${getStatusColor(activity.status)}`}>
        <StatusIcon className="h-4 w-4" />
      </div>
      <div className="flex-1 min-w-0">
        <p className="text-sm font-medium text-gray-900 truncate">{activity.title}</p>
        <p className="text-sm text-gray-500">{activity.time}</p>
      </div>
    </div>
  );
};

export default function AdminDashboard() {
  const [currentTime, setCurrentTime] = useState(new Date());

  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentTime(new Date());
    }, 1000);

    return () => clearInterval(timer);
  }, []);

  return (
    <div className="py-6">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 md:px-8">
        {/* Header */}
        <div className="md:flex md:items-center md:justify-between">
          <div className="flex-1 min-w-0">
            <h2 className="text-2xl font-bold leading-7 text-gray-900 sm:text-3xl sm:truncate">
              Dashboard
            </h2>
            <p className="mt-1 text-sm text-gray-500">
              Welcome back! Here's what's happening with your business today.
              <span className="ml-2 text-blue-600">
                {currentTime.toLocaleString('en-US', {
                  weekday: 'long',
                  year: 'numeric',
                  month: 'long',
                  day: 'numeric',
                  hour: '2-digit',
                  minute: '2-digit'
                })}
              </span>
            </p>
          </div>
        </div>

        {/* Stats Grid */}
        <div className="mt-8">
          <div className="grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4">
            <StatCard
              title="Total Projects"
              value={dashboardStats.projects.total}
              subtitle={`${dashboardStats.projects.active} active, ${dashboardStats.projects.completed} completed`}
              icon={BriefcaseIcon}
              growth={dashboardStats.projects.growth}
              color="blue"
            />
            <StatCard
              title="Total Clients"
              value={dashboardStats.clients.total}
              subtitle={`${dashboardStats.clients.active} active, ${dashboardStats.clients.new} new this month`}
              icon={UserGroupIcon}
              growth={dashboardStats.clients.growth}
              color="green"
            />
            <StatCard
              title="Total Invoices"
              value={dashboardStats.invoices.total}
              subtitle={`${dashboardStats.invoices.pending} pending, ${dashboardStats.invoices.paid} paid`}
              icon={DocumentTextIcon}
              growth={dashboardStats.invoices.growth}
              color="yellow"
            />
            <StatCard
              title="Total Revenue"
              value={`$${(dashboardStats.revenue.total / 1000).toFixed(0)}K`}
              subtitle={`$${(dashboardStats.revenue.pending / 1000).toFixed(0)}K pending`}
              icon={CurrencyDollarIcon}
              growth={dashboardStats.revenue.growth}
              color="purple"
            />
          </div>
        </div>

        {/* Secondary Stats */}
        <div className="mt-8">
          <div className="grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4">
            <StatCard
              title="Contact Forms"
              value={dashboardStats.contactForms.total}
              subtitle={`${dashboardStats.contactForms.unread} unread, ${dashboardStats.contactForms.today} today`}
              icon={DocumentTextIcon}
              growth={5.2}
              color="indigo"
            />
            <StatCard
              title="Job Applications"
              value={dashboardStats.jobApplications.total}
              subtitle={`${dashboardStats.jobApplications.pending} pending review`}
              icon={BriefcaseIcon}
              growth={8.7}
              color="pink"
            />
            <StatCard
              title="Testimonials"
              value={dashboardStats.testimonials.total}
              subtitle={`${dashboardStats.testimonials.published} published, ${dashboardStats.testimonials.pending} pending`}
              icon={ChartBarIcon}
              growth={12.1}
              color="green"
            />
            <StatCard
              title="Blog Posts"
              value={dashboardStats.blogPosts.total}
              subtitle={`${dashboardStats.blogPosts.published} published, ${dashboardStats.blogPosts.draft} drafts`}
              icon={DocumentTextIcon}
              growth={3.4}
              color="blue"
            />
          </div>
        </div>

        {/* Recent Activity */}
        <div className="mt-8">
          <div className="bg-white shadow rounded-lg">
            <div className="px-4 py-5 sm:p-6">
              <h3 className="text-lg leading-6 font-medium text-gray-900 mb-4">Recent Activity</h3>
              <div className="flow-root">
                <ul className="-mb-8">
                  {recentActivity.map((activity, index) => (
                    <li key={activity.id}>
                      <div className="relative pb-8">
                        {index !== recentActivity.length - 1 && (
                          <span className="absolute top-4 left-4 -ml-px h-full w-0.5 bg-gray-200" />
                        )}
                        <ActivityItem activity={activity} />
                      </div>
                    </li>
                  ))}
                </ul>
              </div>
            </div>
          </div>
        </div>

        {/* Quick Actions */}
        <div className="mt-8">
          <div className="bg-white shadow rounded-lg">
            <div className="px-4 py-5 sm:p-6">
              <h3 className="text-lg leading-6 font-medium text-gray-900 mb-4">Quick Actions</h3>
              <div className="grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-4">
                <button className="relative group bg-gray-50 p-6 focus-within:ring-2 focus-within:ring-inset focus-within:ring-blue-500 rounded-lg hover:bg-gray-100 transition-colors">
                  <div>
                    <span className="rounded-lg inline-flex p-3 bg-blue-50 text-blue-700 ring-4 ring-white">
                      <BriefcaseIcon className="h-6 w-6" />
                    </span>
                  </div>
                  <div className="mt-8">
                    <h3 className="text-lg font-medium">
                      <span className="absolute inset-0" />
                      Create Project
                    </h3>
                    <p className="mt-2 text-sm text-gray-500">
                      Start a new project for a client
                    </p>
                  </div>
                </button>

                <button className="relative group bg-gray-50 p-6 focus-within:ring-2 focus-within:ring-inset focus-within:ring-blue-500 rounded-lg hover:bg-gray-100 transition-colors">
                  <div>
                    <span className="rounded-lg inline-flex p-3 bg-green-50 text-green-700 ring-4 ring-white">
                      <UserGroupIcon className="h-6 w-6" />
                    </span>
                  </div>
                  <div className="mt-8">
                    <h3 className="text-lg font-medium">
                      <span className="absolute inset-0" />
                      Add Client
                    </h3>
                    <p className="mt-2 text-sm text-gray-500">
                      Register a new client
                    </p>
                  </div>
                </button>

                <button className="relative group bg-gray-50 p-6 focus-within:ring-2 focus-within:ring-inset focus-within:ring-blue-500 rounded-lg hover:bg-gray-100 transition-colors">
                  <div>
                    <span className="rounded-lg inline-flex p-3 bg-yellow-50 text-yellow-700 ring-4 ring-white">
                      <DocumentTextIcon className="h-6 w-6" />
                    </span>
                  </div>
                  <div className="mt-8">
                    <h3 className="text-lg font-medium">
                      <span className="absolute inset-0" />
                      Create Invoice
                    </h3>
                    <p className="mt-2 text-sm text-gray-500">
                      Generate a new invoice
                    </p>
                  </div>
                </button>

                <button className="relative group bg-gray-50 p-6 focus-within:ring-2 focus-within:ring-inset focus-within:ring-blue-500 rounded-lg hover:bg-gray-100 transition-colors">
                  <div>
                    <span className="rounded-lg inline-flex p-3 bg-purple-50 text-purple-700 ring-4 ring-white">
                      <DocumentTextIcon className="h-6 w-6" />
                    </span>
                  </div>
                  <div className="mt-8">
                    <h3 className="text-lg font-medium">
                      <span className="absolute inset-0" />
                      Write Blog Post
                    </h3>
                    <p className="mt-2 text-sm text-gray-500">
                      Create a new blog article
                    </p>
                  </div>
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
